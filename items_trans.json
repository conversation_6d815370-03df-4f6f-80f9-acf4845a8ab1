{"CommonItemTooltip": {"UsesLife": "使用{0}生命", "UsesMana": "使用{0}魔力", "RestoresLife": "恢复{0}生命", "RestoresLifeRange": "恢复{0}到{1}生命", "RestoresMana": "恢复{0}魔力", "MinuteDuration": "{0}分钟持续时间", "SecondDuration": "{0}秒持续时间", "PlaceableOnXmasTree": "可放置在圣诞树上", "String": "增加悠悠球射程", "Counterweight": "在用悠悠球击中敌人后投掷平衡锤", "BannerBonus": "附近的玩家获得针对以下怪的加成：", "BannerBonusReduced": "附近的玩家获得针对以下怪的小加成：", "SpecialCrafting": "用于特殊制作", "DevItem": "“非常适合冒充开发者！”", "FlightAndSlowfall": "可飞行和缓慢坠落", "PressDownToHover": "按向下键可开关悬停状态\n按向上键可停用悬停", "PressUpToBooster": "按住向上键可以更快上升！", "RightClickToOpen": "<right>打开", "RightClickToClose": "<right>关闭", "MinorStats": "所有属性小幅提升", "MediumStats": "所有属性中幅提升", "MajorStats": "所有属性大幅提升", "TipsyStats": "近战属性小幅提升、防御力降低", "EtherianManaCost10": "在保卫永恒水晶时，每次使用都需要消耗10埃特尼亚魔力", "GolfBall": "可用高尔夫球杆击打", "Sentry": "召唤哨兵", "GolfIron": "全面的高尔夫球杆，最适合中距离\n高尔夫球会以恰当的垂直倾角飞出相当长距离", "GolfPutter": "专门用于最后进洞的高尔夫球杆\n高尔夫球会紧贴地面滚过较短距离，以实现精确击球", "GolfWedge": "专门用于沙坑或高障碍物的高尔夫球杆\n高尔夫球会具有很大的垂直倾角，但不会飞得很远", "GolfDriver": "针对长距离的强力高尔夫球杆\n高尔夫球会飞得很远，垂直倾角很小", "Kite": "有风的日子可以放风筝\n用<right>来收卷风筝线", "LavaFishing": "可以在熔岩中钓鱼", "CreativeSacrificeNeeded": "再研究{0}个即可解锁复制功能", "CreativeSacrificeComplete": "复制功能已解锁", "TeleportationPylon": "附近有2个城镇居民时可以传送至另一个晶塔\n只能放置在匹配的生物群系中且每种类型只能放置一个", "Whips": "你的召唤物将集中攻击被打中的敌人", "WizardHatDuringAnniversary": "仆从数量上限增加1", "MechSummonDuringEverything": "“套装中的一件”", "MechdusaSummonNotDuringEverything": "“它在这个世界中没有任何作用”", "LuminiteVariant": "“来自域外的禁忌建筑材料”"}, "PaintingArtist": {"Crowno": "'<PERSON><PERSON>'", "Garner": "'<PERSON><PERSON>'", "Moosdijk": "'<PERSON><PERSON>'", "Lazure": "'<PERSON><PERSON>'", "Myhre": "'<PERSON><PERSON> <PERSON><PERSON>'", "Burczyk": "'<PERSON><PERSON>'", "Craig": "'<PERSON><PERSON>'", "Kolf": "'<PERSON><PERSON> <PERSON><PERSON>'", "Wright": "'<PERSON><PERSON>'", "Phelps": "'<PERSON><PERSON>'", "Duncan": "'<PERSON><PERSON> <PERSON><PERSON>'", "Ness": "'<PERSON><PERSON> <PERSON><PERSON>'", "Leinfors": "'<PERSON><PERSON>'", "Aurora": "'<PERSON><PERSON>'", "Criddle": "'<PERSON><PERSON>'", "Darthkitten": "'<PERSON><PERSON>'", "darthmorf": "'<PERSON><PERSON>'", "Khaios": "'<PERSON><PERSON>'", "UnitOne": "'U. One'", "Xman101": "'<PERSON><PERSON>'", "Zoomo": "'<PERSON><PERSON>'", "Klei": "'<PERSON><PERSON><PERSON><PERSON>'", "Serenity": "'<PERSON><PERSON>'", "Altermaven": "'<PERSON><PERSON>'", "Redigit": "'<PERSON><PERSON>'", "Jenosis": "'<PERSON><PERSON>'", "Kargoh": "'<PERSON><PERSON>'", "Disc": "'<PERSON><PERSON><PERSON><PERSON>'", "Loki": "'<PERSON><PERSON>'", "Boba": "'<PERSON><PERSON>'", "Grox": "'<PERSON><PERSON>'", "Sigma": "'<PERSON><PERSON>'", "Suweeka": "'<PERSON><PERSON>'", "Marcus": "'<PERSON><PERSON>'", "Cenx": "'<PERSON><PERSON>'", "ManaUser": "'<PERSON><PERSON> User'", "Kazzymodus": "'<PERSON><PERSON>'", "Cheesy": "'<PERSON><PERSON>'", "Yorai": "'<PERSON><PERSON>'"}, "BuffDescription": {"WellFed_Expert": "所有属性小幅提升，生命再生速度提高", "WellFed2_Expert": "所有属性中幅提升，生命再生速度提高", "WellFed3_Expert": "所有属性大幅提升，生命再生速度提高"}, "Prefix": {"Dull": "钝", "Unhappy": "倒霉", "Bulky": "笨重", "Shameful": "可耻", "Heavy": "重", "Light": "轻", "Sighted": "精准", "Rapid": "迅速", "Hasty": "急速", "Intimidating": "恐怖", "Large": "大", "Deadly": "致命", "Staunch": "可靠", "Awful": "讨厌", "Lethargic": "无力", "Awkward": "粗笨", "Powerful": "强大", "Mystic": "神秘", "Adept": "精巧", "Masterful": "精湛", "Inept": "笨拙", "Massive": "巨大", "Ignorant": "无知", "Deranged": "错乱", "Intense": "威猛", "Taboo": "禁忌", "Celestial": "天界", "Furious": "狂怒", "Keen": "锐利", "Superior": "高端", "Forceful": "强力", "Broken": "碎裂", "Dangerous": "危险", "Damaged": "破损", "Shoddy": "粗劣", "Quick": "迅捷", "Deadly2": "致命", "Agile": "灵活", "Nimble": "灵巧", "Murderous": "残暴", "Slow": "缓慢", "Sluggish": "迟钝", "Lazy": "呆滞", "Savage": "凶残", "Annoying": "惹恼", "Nasty": "凶险", "Manic": "狂躁", "Hurtful": "致伤", "Strong": "强劲", "Unpleasant": "粗鲁", "Weak": "虚弱", "Ruthless": "无情", "Frenzying": "暴怒", "Godly": "神级", "Sharp": "锋利", "Demonic": "恶魔", "Zealous": "狂热", "Hard": "坚硬", "Guarding": "守护", "Armored": "装甲", "Warding": "护佑", "Arcane": "奥秘", "Precise": "精确", "Lucky": "幸运", "Jagged": "锯齿", "Pointy": "尖锐", "Spiked": "尖刺", "Angry": "愤怒", "Menacing": "险恶", "Brisk": "轻快", "Fleeting": "快速", "Hasty2": "急速", "Quick2": "迅捷", "Wild": "狂野", "Rash": "鲁莽", "Intrepid": "勇猛", "Tiny": "微小", "Violent": "暴力", "Legendary": "传奇", "Legendary2": "传奇", "Unreal": "虚幻", "Mythical": "神话", "Terrible": "可怕", "Small": "小"}, "ItemName": {"BloodMoonMonolith": "血月天塔柱", "CrimstoneBrick": "猩红石砖", "CrimstoneBrickWall": "猩红石砖墙", "SmoothSandstone": "光面沙岩", "SmoothSandstoneWall": "光面沙岩墙", "IronPickaxe": "铁镐", "IronAxe": "铁斧", "ShadowGreaves": "暗影护胫", "ConfettiGun": "彩纸枪", "ChlorophyteMask": "叶绿面具", "ChlorophyteHelmet": "叶绿头盔", "ChlorophyteHeadgear": "叶绿头饰", "ChlorophytePlateMail": "叶绿板甲", "ChlorophyteGreaves": "叶绿护胫", "ChlorophyteBar": "叶绿锭", "RedDye": "红染料", "OrangeDye": "橙染料", "YellowDye": "黄染料", "ShadowScalemail": "暗影鳞甲", "LimeDye": "橙绿染料", "GreenDye": "绿染料", "TealDye": "青绿染料", "CyanDye": "青染料", "SkyBlueDye": "天蓝染料", "BlueDye": "蓝染料", "PurpleDye": "紫染料", "VioletDye": "蓝紫染料", "PinkDye": "粉染料", "RedandBlackDye": "红黑染料", "ShadowHelmet": "暗影头盔", "OrangeandBlackDye": "橙黑染料", "YellowandBlackDye": "黄黑染料", "LimeandBlackDye": "橙绿黑染料", "GreenandBlackDye": "绿黑染料", "TealandBlackDye": "青绿黑染料", "CyanandBlackDye": "青黑染料", "SkyBlueandBlackDye": "天蓝黑染料", "BlueandBlackDye": "蓝黑染料", "PurpleandBlackDye": "紫黑染料", "VioletandBlackDye": "蓝紫黑染料", "NightmarePickaxe": "梦魇镐", "PinkandBlackDye": "粉黑染料", "FlameDye": "红焰染料", "FlameAndBlackDye": "红焰黑染料", "GreenFlameDye": "绿焰染料", "GreenFlameAndBlackDye": "绿焰黑染料", "BlueFlameDye": "蓝焰染料", "BlueFlameAndBlackDye": "蓝焰黑染料", "SilverDye": "银染料", "BrightRedDye": "淡红染料", "BrightOrangeDye": "淡橙染料", "TheBreaker": "魔锤", "BrightYellowDye": "淡黄染料", "BrightLimeDye": "淡橙绿染料", "BrightGreenDye": "淡绿染料", "BrightTealDye": "淡青绿染料", "BrightCyanDye": "淡青染料", "BrightSkyBlueDye": "淡天蓝染料", "BrightBlueDye": "淡蓝染料", "BrightPurpleDye": "淡紫染料", "BrightVioletDye": "淡蓝紫染料", "BrightPinkDye": "淡粉染料", "Candle": "蜡烛", "BlackDye": "黑染料", "RedandSilverDye": "红银染料", "OrangeandSilverDye": "橙银染料", "YellowandSilverDye": "黄银染料", "LimeandSilverDye": "橙绿银染料", "GreenandSilverDye": "绿银染料", "TealandSilverDye": "青绿银染料", "CyanandSilverDye": "青银染料", "SkyBlueandSilverDye": "天蓝银染料", "BlueandSilverDye": "蓝银染料", "CopperChandelier": "铜吊灯", "PurpleandSilverDye": "紫银染料", "VioletandSilverDye": "蓝紫银染料", "PinkandSilverDye": "粉银染料", "IntenseFlameDye": "亮红焰染料", "IntenseGreenFlameDye": "亮绿焰染料", "IntenseBlueFlameDye": "亮蓝焰染料", "RainbowDye": "彩虹染料", "IntenseRainbowDye": "亮彩虹染料", "YellowGradientDye": "渐变黄染料", "CyanGradientDye": "渐变青染料", "SilverChandelier": "银吊灯", "VioletGradientDye": "渐变蓝紫染料", "Paintbrush": "漆刷", "PaintRoller": "涂漆滚刷", "RedPaint": "红漆", "OrangePaint": "橙漆", "YellowPaint": "黄漆", "LimePaint": "橙绿漆", "GreenPaint": "绿漆", "TealPaint": "青绿漆", "CyanPaint": "青漆", "GoldChandelier": "金吊灯", "SkyBluePaint": "天蓝漆", "BluePaint": "蓝漆", "PurplePaint": "紫漆", "VioletPaint": "蓝紫漆", "PinkPaint": "粉漆", "DeepRedPaint": "深红漆", "DeepOrangePaint": "深橙漆", "DeepYellowPaint": "深黄漆", "DeepLimePaint": "深橙绿漆", "DeepGreenPaint": "深绿漆", "ManaCrystal": "魔力水晶", "DeepTealPaint": "深青绿漆", "DeepCyanPaint": "深青漆", "DeepSkyBluePaint": "深天蓝漆", "DeepBluePaint": "深蓝漆", "DeepPurplePaint": "深紫漆", "DeepVioletPaint": "深蓝紫漆", "DeepPinkPaint": "深粉漆", "BlackPaint": "黑漆", "WhitePaint": "白漆", "GrayPaint": "灰漆", "IronOre": "铁矿", "LesserManaPotion": "弱效魔力药水", "PaintScraper": "漆铲", "LihzahrdBrick": "丛林蜥蜴砖", "LihzahrdBrickWall": "丛林蜥蜴砖墙", "SlushBlock": "雪泥块", "PalladiumOre": "钯金矿", "OrichalcumOre": "山铜矿", "TitaniumOre": "钛金矿", "TealMushroom": "青绿蘑菇", "GreenMushroom": "绿蘑菇", "SkyBlueFlower": "天蓝花朵", "BandofStarpower": "星力手环", "YellowMarigold": "黄万寿菊", "BlueBerries": "蓝浆果", "LimeKelp": "橙绿海藻", "PinkPricklyPear": "粉仙人掌果", "OrangeBloodroot": "橙血根草", "RedHusk": "红外壳", "CyanHusk": "青外壳", "VioletHusk": "蓝紫外壳", "PurpleMucos": "紫黏液", "BlackInk": "黑墨水", "FlowerofFire": "火之花", "DyeVat": "染缸", "BeeGun": "蜜蜂枪", "PossessedHatchet": "疯狂飞斧", "BeeKeeper": "养蜂人", "Hive": "蜂巢", "HoneyBlock": "蜂蜜块", "HiveWall": "蜂巢墙", "CrispyHoneyBlock": "松脆蜂蜜块", "HoneyBucket": "蜂蜜桶", "HiveWand": "蜂巢魔棒", "MagicMissile": "魔法飞弹", "Beenade": "蜜蜂手榴弹", "GravityGlobe": "重力球", "HoneyComb": "蜂窝", "Abeemination": "憎恶之蜂", "BottledHoney": "瓶装蜂蜜", "RainHat": "雨帽", "RainCoat": "雨衣", "LihzahrdDoor": "丛林蜥蜴门", "DungeonDoor": "地牢门", "LeadDoor": "铅门", "DirtRod": "土魔杖", "IronDoor": "铁门", "TempleKey": "神庙钥匙", "LihzahrdChest": "丛林蜥蜴箱", "LihzahrdChair": "丛林蜥蜴椅", "LihzahrdTable": "丛林蜥蜴桌", "LihzahrdWorkBench": "丛林蜥蜴工作台", "SuperDartTrap": "超级飞镖机关", "FlameTrap": "烈焰机关", "SpikyBallTrap": "尖球机关", "SpearTrap": "长矛机关", "ShadowOrb": "暗影珠", "WoodenSpike": "木尖刺", "LihzahrdPressurePlate": "丛林蜥蜴压力板", "LihzahrdStatue": "丛林蜥蜴雕像", "LihzahrdWatcherStatue": "丛林蜥蜴看守人雕像", "LihzahrdGuardianStatue": "丛林蜥蜴守卫雕像", "WaspGun": "胡蜂枪", "PiranhaGun": "食人鱼枪", "PygmyStaff": "矮人法杖", "PygmyNecklace": "矮人项链", "TikiMask": "提基面具", "Meteorite": "陨石", "TikiShirt": "提基衣", "TikiPants": "提基裤", "LeafWings": "叶之翼", "BlizzardinaBalloon": "暴雪气球", "BundleofBalloons": "气球束", "BatWings": "蝙蝠之翼", "BoneSword": "骨剑", "HerculesBeetle": "大力士甲虫", "SmokeBomb": "烟雾弹", "BoneKey": "骨头钥匙", "MeteoriteBar": "陨石锭", "Nectar": "花蜜", "TikiTotem": "提基图腾", "LizardEgg": "蜥蜴蛋", "GraveMarker": "墓石碑", "CrossGraveMarker": "十字墓石碑", "Headstone": "碑石", "Gravestone": "墓碑", "Obelisk": "方尖碑", "LeafBlower": "吹叶机", "ChlorophyteBullet": "叶绿弹", "Hook": "爪钩", "ParrotCracker": "鹦鹉饼干", "StrangeGlowingMushroom": "奇异发光蘑菇", "Seedling": "幼苗", "WispinaBottle": "妖灵瓶", "PalladiumBar": "钯金锭", "PalladiumSword": "钯金剑", "PalladiumPike": "钯金刺矛", "PalladiumRepeater": "钯金连弩", "PalladiumPickaxe": "钯金镐", "PalladiumDrill": "钯金钻头", "Flamarang": "烈焰回旋镖", "PalladiumChainsaw": "钯金链锯", "OrichalcumBar": "山铜锭", "OrichalcumSword": "山铜剑", "OrichalcumHalberd": "山铜长戟", "OrichalcumRepeater": "山铜连弩", "OrichalcumPickaxe": "山铜镐", "OrichalcumDrill": "山铜钻头", "OrichalcumChainsaw": "山铜链锯", "TitaniumBar": "钛金锭", "TitaniumSword": "钛金剑", "CopperOre": "铜矿", "MoltenFury": "熔火之怒", "TitaniumTrident": "钛金三叉戟", "TitaniumRepeater": "钛金连弩", "TitaniumPickaxe": "钛金镐", "TitaniumDrill": "钛金钻头", "TitaniumChainsaw": "钛金链锯", "PalladiumMask": "钯金面具", "PalladiumHelmet": "钯金头盔", "PalladiumHeadgear": "钯金头饰", "PalladiumBreastplate": "钯金胸甲", "PalladiumLeggings": "钯金护腿", "FieryGreatsword": "火山", "OrichalcumMask": "山铜面具", "OrichalcumHelmet": "山铜头盔", "OrichalcumHeadgear": "山铜头饰", "OrichalcumBreastplate": "山铜胸甲", "OrichalcumLeggings": "山铜护腿", "TitaniumMask": "钛金面具", "TitaniumHelmet": "钛金头盔", "TitaniumHeadgear": "钛金头饰", "TitaniumBreastplate": "钛金胸甲", "TitaniumLeggings": "钛金护腿", "MoltenPickaxe": "熔岩镐", "OrichalcumAnvil": "山铜砧", "TitaniumForge": "钛金熔炉", "PalladiumWaraxe": "钯金战斧", "OrichalcumWaraxe": "山铜战斧", "TitaniumWaraxe": "钛金战斧", "HallowedBar": "神圣锭", "ChlorophyteClaymore": "叶绿双刃刀", "ChlorophyteSaber": "叶绿军刀", "ChlorophytePartisan": "叶绿镋", "ChlorophyteShotbow": "叶绿连弩", "MeteorHelmet": "流星头盔", "ChlorophytePickaxe": "叶绿镐", "ChlorophyteDrill": "叶绿钻头", "ChlorophyteChainsaw": "叶绿链锯", "ChlorophyteGreataxe": "叶绿巨斧", "ChlorophyteWarhammer": "叶绿战锤", "ChlorophyteArrow": "叶绿箭", "AmethystHook": "紫晶钩", "TopazHook": "黄玉钩", "SapphireHook": "蓝玉钩", "EmeraldHook": "翡翠钩", "MeteorSuit": "流星护甲", "RubyHook": "红玉钩", "DiamondHook": "钻石钩", "AmberMosquito": "蚊子琥珀", "UmbrellaHat": "伞帽", "NimbusRod": "雨云魔杖", "OrangeTorch": "橙火把", "CrimsandBlock": "猩红沙块", "BeeCloak": "蜜蜂斗篷", "EyeoftheGolem": "石巨人之眼", "HoneyBalloon": "蜂蜜气球", "MeteorLeggings": "流星护腿", "BlueHorseshoeBalloon": "蓝马掌气球", "WhiteHorseshoeBalloon": "白马掌气球", "YellowHorseshoeBalloon": "黄马掌气球", "FrozenTurtleShell": "冰冻海龟壳", "SniperRifle": "狙击步枪", "VenusMagnum": "维纳斯万能枪", "CrimsonRod": "猩红魔杖", "CrimtaneBar": "猩红矿锭", "Stynger": "毒刺发射器", "FlowerPow": "花之力", "BottledWater": "瓶装水", "RainbowGun": "彩虹枪", "StyngerBolt": "毒刺矢", "ChlorophyteJackhammer": "叶绿手提钻", "Teleporter": "传送机", "FlowerofFrost": "寒霜之花", "Uzi": "乌兹冲锋枪", "MagnetSphere": "磁球", "PurpleStainedGlass": "紫花窗玻璃", "YellowStainedGlass": "黄花窗玻璃", "BlueStainedGlass": "蓝花窗玻璃", "SpaceGun": "太空枪", "GreenStainedGlass": "绿花窗玻璃", "RedStainedGlass": "红花窗玻璃", "MulticoloredStainedGlass": "五彩花窗玻璃", "SkeletronHand": "骷髅王之手", "Skull": "骷髅头", "BallaHat": "巴拉帽", "GangstaHat": "黑帮帽", "SailorHat": "水手帽", "EyePatch": "眼罩", "SailorShirt": "水手衣", "RocketBoots": "火箭靴", "SailorPants": "水手裤", "SkeletronMask": "骷髅王面具", "AmethystRobe": "紫晶长袍", "TopazRobe": "黄玉长袍", "SapphireRobe": "蓝玉长袍", "EmeraldRobe": "翡翠长袍", "RubyRobe": "红玉长袍", "DiamondRobe": "钻石长袍", "WhiteTuxedoShirt": "白西装衣", "WhiteTuxedoPants": "白西装裤", "GrayBrick": "灰砖", "PanicNecklace": "恐慌项链", "LifeFruit": "生命果", "LihzahrdAltar": "丛林蜥蜴祭坛", "LihzahrdPowerCell": "丛林蜥蜴电池", "Picksaw": "锯刃镐", "HeatRay": "高温射线枪", "StaffofEarth": "大地法杖", "GolemFist": "石巨人之拳", "WaterChest": "水中箱", "Binoculars": "双筒望远镜", "GoldOre": "金矿", "GrayBrickWall": "灰砖墙", "RifleScope": "步枪瞄准镜", "DestroyerEmblem": "毁灭者徽章", "HighVelocityBullet": "高速子弹", "JellyfishNecklace": "水母项链", "ZombieArm": "僵尸臂", "TheAxe": "吉他斧", "IceSickle": "冰雪镰刀", "ClothierVoodooDoll": "服装商巫毒娃娃", "PoisonStaff": "剧毒法杖", "SlimeStaff": "史莱姆法杖", "RedBrick": "红砖", "PoisonDart": "毒镖", "EyeSpring": "弹簧眼", "ToySled": "玩具雪橇", "BookofSkulls": "骷髅头法书", "KOCannon": "致胜炮", "PirateMap": "海盗地图", "TurtleHelmet": "海龟头盔", "TurtleScaleMail": "海龟铠甲", "TurtleLeggings": "海龟护腿", "SnowballCannon": "雪球炮", "RedBrickWall": "红砖墙", "BonePickaxe": "骨镐", "MagicQuiver": "魔法箭袋", "MagmaStone": "岩浆石", "ObsidianRose": "黑曜石玫瑰", "Bananarang": "香蕉回旋镖", "ChainKnife": "链刀", "RodofDiscord": "混沌传送杖", "DeathSickle": "死神镰刀", "TurtleShell": "海龟壳", "TissueSample": "组织样本", "ClayBlock": "粘土块", "Vertebrae": "椎骨", "BloodySpine": "血腥脊椎", "Ichor": "灵液", "IchorTorch": "灵液火把", "IchorArrow": "灵液箭", "IchorBullet": "灵液弹", "GoldenShower": "黄金雨", "BunnyCannon": "兔兔炮", "ExplosiveBunny": "爆炸兔", "VialofVenom": "小瓶毒液", "BlueBrick": "蓝砖", "FlaskofVenom": "毒液药剂", "VenomArrow": "毒液箭", "VenomBullet": "毒液弹", "FireGauntlet": "烈火手套", "Cog": "齿轮", "Confetti": "彩纸", "Nanites": "纳米机器人", "ExplosivePowder": "爆炸粉", "GoldDust": "金尘", "PartyBullet": "派对弹", "BlueBrickWall": "蓝砖墙", "NanoBullet": "纳米弹", "ExplodingBullet": "爆破弹", "GoldenBullet": "金子弹", "FlaskofCursedFlames": "诅咒焰药剂", "FlaskofFire": "烈火药剂", "FlaskofGold": "金药剂", "FlaskofIchor": "灵液药剂", "FlaskofNanites": "纳米机器人药剂", "FlaskofParty": "派对药剂", "FlaskofPoison": "毒药剂", "ChainLantern": "挂链灯笼", "EyeofCthulhuTrophy": "克苏鲁之眼纪念章", "EaterofWorldsTrophy": "世界吞噬怪纪念章", "BrainofCthulhuTrophy": "克苏鲁之脑纪念章", "SkeletronTrophy": "骷髅王纪念章", "QueenBeeTrophy": "蜂王纪念章", "WallofFleshTrophy": "血肉墙纪念章", "DestroyerTrophy": "毁灭者纪念章", "SkeletronPrimeTrophy": "机械骷髅王纪念章", "RetinazerTrophy": "激光眼纪念章", "SpazmatismTrophy": "魔焰眼纪念章", "GreenBrick": "绿砖", "PlanteraTrophy": "世纪之花纪念章", "GolemTrophy": "石巨人纪念章", "BloodMoonRising": "血月升空", "TheHangedMan": "上吊的人", "GloryoftheFire": "烈火荣耀", "BoneWarp": "扭曲的骨头", "WallSkeleton": "壁挂骷髅", "HangingSkeleton": "吊挂骷髅", "BlueSlabWall": "蓝板墙", "BlueTiledWall": "蓝瓷砖墙", "GreenBrickWall": "绿砖墙", "PinkSlabWall": "粉板墙", "PinkTiledWall": "粉瓷砖墙", "GreenSlabWall": "绿板墙", "GreenTiledWall": "绿瓷砖墙", "BlueBrickPlatform": "蓝砖平台", "PinkBrickPlatform": "粉砖平台", "GreenBrickPlatform": "绿砖平台", "MetalShelf": "金属架", "BrassShelf": "黄铜架", "WoodShelf": "木架", "PinkBrick": "粉砖", "BrassLantern": "黄铜灯笼", "CagedLantern": "笼式灯笼", "CarriageLantern": "马车灯笼", "AlchemyLantern": "炼金灯笼", "DiablostLamp": "魔教徒灯", "OilRagSconse": "油布烛台", "BlueDungeonChair": "蓝地牢椅", "BlueDungeonTable": "蓝地牢桌", "BlueDungeonWorkBench": "蓝地牢工作台", "GreenDungeonChair": "绿地牢椅", "SilverOre": "银矿", "PinkBrickWall": "粉砖墙", "GreenDungeonTable": "绿地牢桌", "GreenDungeonWorkBench": "绿地牢工作台", "PinkDungeonChair": "粉地牢椅", "PinkDungeonTable": "粉地牢桌", "PinkDungeonWorkBench": "粉地牢工作台", "BlueDungeonCandle": "蓝地牢蜡烛", "GreenDungeonCandle": "绿地牢蜡烛", "PinkDungeonCandle": "粉地牢蜡烛", "BlueDungeonVase": "蓝地牢花瓶", "GreenDungeonVase": "绿地牢花瓶", "GoldBrick": "金砖", "PinkDungeonVase": "粉地牢花瓶", "BlueDungeonDoor": "蓝地牢门", "GreenDungeonDoor": "绿地牢门", "PinkDungeonDoor": "粉地牢门", "BlueDungeonBookcase": "蓝地牢书架", "GreenDungeonBookcase": "绿地牢书架", "PinkDungeonBookcase": "粉地牢书架", "Catacomb": "地下墓穴", "DungeonShelf": "地牢架", "SkellingtonJSkellingsworth": "骷髅杰克", "GoldBrickWall": "金砖墙", "TheCursedMan": "被诅咒的人", "TheEyeSeestheEnd": "最后一眼", "SomethingEvilisWatchingYou": "恶魔注视", "TheTwinsHaveAwoken": "苏醒的双子魔眼", "TheScreamer": "呐喊者", "GoblinsPlayingPoker": "哥布林打扑克牌", "Dryadisque": "树妖女", "Sunflowers": "向日葵", "TerrarianGothic": "泰拉哥特式", "Beanie": "小便帽", "SilverBrick": "银砖", "ImbuingStation": "灌注站", "StarinaBottle": "星星瓶", "EmptyBullet": "空心子弹", "Impact": "碰撞", "PoweredbyBirds": "鸟力驱动", "TheDestroyer": "毁灭者", "ThePersistencyofEyes": "眼睛的永恒", "UnicornCrossingtheHallows": "独角兽穿越神圣之地", "GreatWave": "浪里", "StarryNight": "星空", "SilverBrickWall": "银砖墙", "GuidePicasso": "向导毕加索", "TheGuardiansGaze": "守卫凝视", "FatherofSomeone": "某人之父", "NurseLisa": "护士丽莎", "ShadowbeamStaff": "暗影束法杖", "InfernoFork": "狱火叉", "SpectreStaff": "幽灵法杖", "WoodenFence": "木栅栏", "LeadFence": "铅栅栏", "BubbleMachine": "泡泡机", "CopperBrick": "铜砖", "BubbleWand": "泡泡魔棒", "MarchingBonesBanner": "骷髅行军旗", "NecromanticSign": "死灵标旗", "RustedCompanyStandard": "锈蚀连队旗", "RaggedBrotherhoodSigil": "褴褛兄弟会旗", "MoltenLegionFlag": "熔火军团旗", "DiabolicSigil": "恶魔纹章旗", "ObsidianPlatform": "黑曜石平台", "ObsidianDoor": "黑曜石门", "ObsidianChair": "黑曜石椅", "CopperBrickWall": "铜砖墙", "ObsidianTable": "黑曜石桌", "ObsidianWorkBench": "黑曜石工作台", "ObsidianVase": "黑曜石花瓶", "ObsidianBookcase": "黑曜石书架", "HellboundBanner": "地狱之界旗", "HellHammerBanner": "地狱之锤旗", "HelltowerBanner": "地狱之塔旗", "LostHopesofManBanner": "绝望人旗", "ObsidianWatcherBanner": "黑曜石看守人旗", "LavaEruptsBanner": "熔岩喷液旗", "Spike": "尖刺", "BlueDungeonBed": "蓝地牢床", "GreenDungeonBed": "绿地牢床", "PinkDungeonBed": "粉地牢床", "ObsidianBed": "黑曜石床", "Waldo": "沃尔多", "Darkness": "黑暗", "DarkSoulReaper": "暗魂死神", "Land": "大地", "TrappedGhost": "受困鬼魂", "DemonsEye": "恶魔眼", "WaterCandle": "水蜡烛", "FindingGold": "淘金", "FirstEncounter": "初次邂逅", "GoodMorning": "早安", "UndergroundReward": "地下馈赠", "ThroughtheWindow": "窗外", "PlaceAbovetheClouds": "云之上", "DoNotStepontheGrass": "请勿践踏草坪", "ColdWatersintheWhiteLand": "冰雪寒溪", "LightlessChasms": "阴暗幽谷", "TheLandofDeceivingLooks": "浮华假象的大地", "Book": "书", "Daylight": "天光", "SecretoftheSands": "沙的秘密", "DeadlandComesAlive": "死地复生", "EvilPresence": "恶灵现世", "SkyGuardian": "天空守卫", "AmericanExplosive": "美国爆炸式", "Discover": "探索", "HandEarth": "大地之手", "OldMiner": "老矿工", "Skelehead": "骨蛇之头", "CopperWatch": "铜表", "Cobweb": "蛛网", "FacingtheCerebralMastermind": "直面幕后大脑", "LakeofFire": "火湖", "TrioSuperHeroes": "超级英雄三剑客", "SpectreHood": "幽灵兜帽", "SpectreRobe": "幽灵长袍", "SpectrePants": "幽灵裤", "SpectrePickaxe": "幽灵镐", "SpectreHamaxe": "幽灵锤斧", "Ectoplasm": "灵气", "GothicChair": "哥特椅", "NecroHelmet": "死灵头盔", "GothicTable": "哥特桌", "GothicWorkBench": "哥特工作台", "GothicBookcase": "哥特书架", "PaladinsHammer": "圣骑士锤", "SWATHelmet": "特战头盔", "BeeWings": "蜜蜂之翼", "GiantHarpyFeather": "巨型鸟妖之羽", "BoneFeather": "骨之羽", "FireFeather": "火羽", "IceFeather": "冰雪羽", "NecroBreastplate": "死灵胸甲", "BrokenBatWing": "破蝙蝠之翼", "TatteredBeeWing": "褴褛蜜蜂之翼", "LargeAmethyst": "大紫晶", "LargeTopaz": "大黄玉", "LargeSapphire": "大蓝玉", "LargeEmerald": "大翡翠", "LargeRuby": "大红玉", "LargeDiamond": "大钻石", "JungleChest": "丛林箱", "CorruptionChest": "腐化箱", "NecroGreaves": "死灵护胫", "CrimsonChest": "猩红箱", "HallowedChest": "神圣箱", "FrozenChest": "冰雪箱", "JungleKey": "丛林钥匙", "CorruptionKey": "腐化钥匙", "CrimsonKey": "猩红钥匙", "HallowedKey": "神圣钥匙", "FrozenKey": "冰冻钥匙", "ImpFace": "小鬼脸", "OminousPresence": "凶兆", "Bone": "骨头", "ShiningMoon": "闪亮月亮", "LivingGore": "血肉之躯", "FlowingMagma": "岩浆漫流", "SpectrePaintbrush": "幽灵漆刷", "SpectrePaintRoller": "幽灵涂漆滚刷", "SpectrePaintScraper": "幽灵漆铲", "ShroomiteHeadgear": "蘑菇矿头饰", "ShroomiteMask": "蘑菇矿面具", "ShroomiteHelmet": "蘑菇矿头盔", "ShroomiteBreastplate": "蘑菇矿胸甲", "Muramasa": "村正", "ShroomiteLeggings": "蘑菇矿护腿", "Autohammer": "自动锤炼机", "ShroomiteBar": "蘑菇矿锭", "SDMG": "太空海豚机枪", "CenxsTiara": "Cenx的头冠", "CenxsBreastplate": "Cenx的胸甲", "CenxsLeggings": "Cenx的护腿", "CrownosMask": "Crowno的面具", "CrownosBreastplate": "Crowno的胸甲", "CrownosLeggings": "Crowno的护腿", "CobaltShield": "钴护盾", "WillsHelmet": "Will的头盔", "WillsBreastplate": "Will的胸甲", "WillsLeggings": "Will的护腿", "JimsHelmet": "<PERSON>的头盔", "JimsBreastplate": "<PERSON>的胸甲", "JimsLeggings": "<PERSON>的护腿", "AaronsHelmet": "<PERSON>的头盔", "AaronsBreastplate": "<PERSON>的胸甲", "AaronsLeggings": "<PERSON>的护腿", "VampireKnives": "吸血鬼刀", "AquaScepter": "海蓝权杖", "BrokenHeroSword": "断裂英雄剑", "ScourgeoftheCorruptor": "腐化者之戟", "StaffoftheFrostHydra": "寒霜九头蛇法杖", "TheCreationoftheGuide": "创造向导", "TheMerchant": "商人", "CrownoDevoursHisLunch": "Crowno吞噬其餐", "RareEnchantment": "罕见魔法", "GloriousNight": "荣耀之夜", "SweetheartNecklace": "甜心项链", "FlurryBoots": "疾风雪靴", "LuckyHorseshoe": "幸运马掌", "DTownsHelmet": "D-Town的头盔", "DTownsBreastplate": "D-Town的胸甲", "DTownsLeggings": "D-Town的护腿", "DTownsWings": "D-Town的翅膀", "WillsWings": "Will的翅膀", "CrownosWings": "Crowno的翅膀", "CenxsWings": "Cenx的翅膀", "CenxsDress": "Cenx的上衣", "CenxsDressPants": "Cenx的裤装", "PalladiumColumn": "钯金柱", "ShinyRedBalloon": "闪亮红气球", "PalladiumColumnWall": "钯金柱墙", "BubblegumBlock": "泡泡糖块", "BubblegumBlockWall": "泡泡糖块墙", "TitanstoneBlock": "钛石块", "TitanstoneBlockWall": "钛石块墙", "MagicCuffs": "魔法手铐", "MusicBoxSnow": "八音盒（雪原）", "MusicBoxSpace": "八音盒（太空夜晚）", "MusicBoxCrimson": "八音盒（猩红之地）", "MusicBoxBoss4": "八音盒（Boss 4）", "SilverWatch": "银表", "Harpoon": "鱼叉枪", "MusicBoxAltOverworldDay": "八音盒（地表世界备选曲）", "MusicBoxRain": "八音盒（雨）", "MusicBoxIce": "八音盒（冰雪）", "MusicBoxDesert": "八音盒（沙漠）", "MusicBoxOcean": "八音盒（海洋白天）", "MusicBoxDungeon": "八音盒（地牢）", "MusicBoxPlantera": "八音盒（世纪之花）", "MusicBoxBoss5": "八音盒（Boss 5）", "MusicBoxTemple": "八音盒（神庙）", "MusicBoxEclipse": "八音盒（日食）", "SpikyBall": "尖球", "MusicBoxMushrooms": "八音盒（蘑菇）", "ButterflyDust": "蝴蝶尘", "AnkhCharm": "十字章护身符", "AnkhShield": "十字章护盾", "BlueFlare": "蓝照明弹", "AnglerFishBanner": "琵琶鱼旗", "AngryNimbusBanner": "愤怒雨云怪旗", "AnomuraFungusBanner": "歪尾真菌旗", "AntlionBanner": "蚁狮旗", "ArapaimaBanner": "巨骨舌鱼旗", "BallOHurt": "链球", "ArmoredSkeletonBanner": "装甲骷髅旗", "BatBanner": "洞穴蝙蝠旗", "BirdBanner": "鸟旗", "BlackRecluseBanner": "黑隐士旗", "BloodFeederBanner": "嗜血怪旗", "BloodJellyBanner": "血水母旗", "BloodCrawlerBanner": "血爬虫旗", "BoneSerpentBanner": "骨蛇旗", "BunnyBanner": "兔兔旗", "ChaosElementalBanner": "混沌精旗", "BlueMoon": "蓝月", "MimicBanner": "宝箱怪旗", "ClownBanner": "小丑旗", "CorruptBunnyBanner": "腐化兔兔旗", "CorruptGoldfishBanner": "腐化金鱼旗", "CrabBanner": "螃蟹旗", "CrimeraBanner": "猩红喀迈拉旗", "CrimsonAxeBanner": "猩红斧旗", "CursedHammerBanner": "诅咒锤旗", "DemonBanner": "恶魔旗", "DemonEyeBanner": "恶魔眼旗", "Handgun": "手枪", "DerplingBanner": "跳跳兽旗", "EaterofSoulsBanner": "噬魂怪旗", "EnchantedSwordBanner": "附魔剑旗", "ZombieEskimoBanner": "冰冻僵尸旗", "FaceMonsterBanner": "脸怪旗", "FloatyGrossBanner": "恶心浮游怪旗", "FlyingFishBanner": "飞鱼旗", "FlyingSnakeBanner": "飞蛇旗", "FrankensteinBanner": "科学怪人旗", "FungiBulbBanner": "真菌球怪旗", "WaterBolt": "水矢", "FungoFishBanner": "蘑菇鱼旗", "GastropodBanner": "腹足怪旗", "GoblinThiefBanner": "哥布林盗贼旗", "GoblinSorcererBanner": "哥布林巫士旗", "GoblinPeonBanner": "哥布林苦力旗", "GoblinScoutBanner": "哥布林侦察兵旗", "GoblinWarriorBanner": "哥布林战士旗", "GoldfishBanner": "金鱼旗", "HarpyBanner": "鸟妖旗", "HellbatBanner": "地狱蝙蝠旗", "Bomb": "炸弹", "HerplingBanner": "蹦蹦兽旗", "HornetBanner": "黄蜂旗", "IceElementalBanner": "冰雪精旗", "IcyMermanBanner": "冰雪人鱼旗", "FireImpBanner": "火焰小鬼旗", "JellyfishBanner": "蓝水母旗", "JungleCreeperBanner": "丛林蜘蛛旗", "LihzahrdBanner": "丛林蜥蜴旗", "ManEaterBanner": "食人怪旗", "MeteorHeadBanner": "流星头旗", "Dynamite": "雷管", "MothBanner": "飞蛾旗", "MummyBanner": "木乃伊旗", "MushiLadybugBanner": "蘑菇瓢虫旗", "ParrotBanner": "鹦鹉旗", "PigronBanner": "猪龙旗", "PiranhaBanner": "食人鱼旗", "PirateBanner": "海盗水手旗", "PixieBanner": "妖精旗", "RaincoatZombieBanner": "雨衣僵尸旗", "ReaperBanner": "死神旗", "Grenade": "手榴弹", "SharkBanner": "鲨鱼旗", "SkeletonBanner": "骷髅旗", "SkeletonMageBanner": "暗黑法师旗", "SlimeBanner": "蓝史莱姆旗", "SnowFlinxBanner": "小雪怪旗", "SpiderBanner": "爬墙蜘蛛旗", "SporeZombieBanner": "孢子僵尸旗", "SwampThingBanner": "沼泽怪旗", "TortoiseBanner": "巨型陆龟旗", "ToxicSludgeBanner": "毒泥旗", "SandBlock": "沙块", "UmbrellaSlimeBanner": "雨伞史莱姆旗", "UnicornBanner": "独角兽旗", "VampireBanner": "吸血鬼旗", "VultureBanner": "秃鹰旗", "NypmhBanner": "宁芙旗", "WerewolfBanner": "狼人旗", "WolfBanner": "狼旗", "WorldFeederBanner": "吞世怪旗", "WormBanner": "蠕虫旗", "WraithBanner": "幻灵旗", "GoldWatch": "金表", "Glass": "玻璃", "WyvernBanner": "飞龙旗", "ZombieBanner": "僵尸旗", "GlassPlatform": "玻璃平台", "GlassChair": "玻璃椅", "GoldenChair": "金椅", "GoldenToilet": "金马桶", "BarStool": "高脚凳", "HoneyChair": "蜂蜜椅", "SteampunkChair": "蒸汽朋克椅", "GlassDoor": "玻璃门", "Sign": "标牌", "GoldenDoor": "金门", "HoneyDoor": "蜂蜜门", "SteampunkDoor": "蒸汽朋克门", "GlassTable": "玻璃桌", "BanquetTable": "宴会桌", "Bar": "吧台", "GoldenTable": "金桌", "HoneyTable": "蜂蜜桌", "SteampunkTable": "蒸汽朋克桌", "GlassBed": "玻璃床", "AshBlock": "灰烬块", "GoldenBed": "金床", "HoneyBed": "蜂蜜床", "SteampunkBed": "蒸汽朋克床", "LivingWoodWall": "生命木墙", "FartinaJar": "罐中臭屁", "Pumpkin": "南瓜", "PumpkinWall": "南瓜墙", "Hay": "干草", "HayWall": "干草墙", "SpookyWood": "阴森木", "Obsidian": "黑曜石", "SpookyWoodWall": "阴森木墙", "PumpkinHelmet": "南瓜头盔", "PumpkinBreastplate": "南瓜胸甲", "PumpkinLeggings": "南瓜护腿", "CandyApple": "焦糖苹果", "SoulCake": "灵魂蛋糕", "NurseHat": "护士帽", "NurseShirt": "护士衣", "NursePants": "护士裤", "WizardsHat": "巫师之帽", "Hellstone": "狱石", "GuyFawkesMask": "盖伊·福克斯面具", "DyeTraderRobe": "染料商长袍", "SteampunkGoggles": "蒸汽朋克护目镜", "CyborgHelmet": "机器侠头盔", "CyborgShirt": "机器侠衣", "CyborgPants": "机器侠裤", "CreeperMask": "苦力怕面具", "CreeperShirt": "苦力怕衣", "CreeperPants": "苦力怕裤", "CatMask": "猫咪面具", "HellstoneBar": "狱石锭", "CatShirt": "猫咪衣", "CatPants": "猫咪裤", "GhostMask": "鬼魂面具", "GhostShirt": "鬼魂衣", "PumpkinMask": "南瓜面具", "PumpkinShirt": "南瓜衣", "PumpkinPants": "南瓜裤", "RobotMask": "机器人面具", "RobotShirt": "机器人衣", "RobotPants": "机器人裤", "MudBlock": "泥块", "UnicornMask": "独角兽面具", "UnicornShirt": "独角兽衣", "UnicornPants": "独角兽裤", "VampireMask": "吸血鬼面具", "VampireShirt": "吸血鬼衣", "VampirePants": "吸血鬼裤", "WitchHat": "女巫帽", "LeprechaunHat": "矮妖帽", "LeprechaunShirt": "矮妖衣", "LeprechaunPants": "矮妖裤", "Sapphire": "蓝玉", "PixieShirt": "妖精衣", "PixiePants": "妖精裤", "PrincessHat": "公主帽", "PrincessDressNew": "公主裙", "GoodieBag": "礼袋", "WitchDress": "女巫服", "WitchBoots": "女巫靴", "BrideofFrankensteinMask": "科学怪人新娘面具", "BrideofFrankensteinDress": "科学怪人新娘服", "KarateTortoiseMask": "空手道陆龟面具", "Ruby": "红玉", "KarateTortoiseShirt": "空手道陆龟衣", "KarateTortoisePants": "空手道陆龟裤", "CandyCornRifle": "玉米糖步枪", "CandyCorn": "玉米糖", "JackOLanternLauncher": "杰克南瓜灯发射器", "ExplosiveJackOLantern": "爆炸杰克南瓜灯", "Sickle": "镰刀", "PumpkinPie": "南瓜派", "ScarecrowHat": "稻草人帽", "ScarecrowShirt": "稻草人衣", "Emerald": "翡翠", "ScarecrowPants": "稻草人裤", "Cauldron": "大锅", "PumpkinChair": "南瓜椅", "PumpkinDoor": "南瓜门", "PumpkinTable": "南瓜桌", "PumpkinWorkBench": "南瓜工作台", "PumpkinPlatform": "南瓜平台", "TatteredFairyWings": "褴褛仙灵之翼", "SpiderEgg": "蜘蛛卵", "MagicalPumpkinSeed": "魔法南瓜子", "DepthMeter": "深度计", "Topaz": "黄玉", "BatHook": "蝙蝠钩", "BatScepter": "蝙蝠权杖", "RavenStaff": "乌鸦法杖", "JungleKeyMold": "丛林钥匙模具", "CorruptionKeyMold": "腐化钥匙模具", "CrimsonKeyMold": "猩红钥匙模具", "HallowedKeyMold": "神圣钥匙模具", "FrozenKeyMold": "冰冻钥匙模具", "HangingJackOLantern": "杰克南瓜挂灯", "RottenEgg": "臭蛋", "Amethyst": "紫晶", "UnluckyYarn": "霉运纱线", "BlackFairyDust": "黑色仙尘", "Jackelier": "南瓜头吊灯", "JackOLantern": "杰克南瓜灯", "SpookyChair": "阴森椅", "SpookyDoor": "阴森门", "SpookyTable": "阴森桌", "SpookyWorkBench": "阴森工作台", "SpookyPlatform": "阴森木平台", "ReaperHood": "死神兜帽", "Diamond": "钻石", "ReaperRobe": "死神长袍", "FoxMask": "狐狸面具", "FoxShirt": "狐狸衣", "FoxPants": "狐狸裤", "CatEars": "猫耳", "BloodyMachete": "血腥砍刀", "TheHorsemansBlade": "无头骑士剑", "BladedGlove": "利刃手套", "PumpkinSeed": "南瓜子", "SpookyHook": "阴森钩", "GlowingMushroom": "发光蘑菇", "SpookyWings": "阴森之翼", "SpookyTwig": "阴森嫩枝", "SpookyHelmet": "阴森头盔", "SpookyBreastplate": "阴森胸甲", "SpookyLeggings": "阴森护腿", "StakeLauncher": "尖桩发射器", "Stake": "尖桩", "CursedSapling": "诅咒树苗", "SpaceCreatureMask": "太空生物面具", "SpaceCreatureShirt": "太空生物衣", "Star": "星星", "SpaceCreaturePants": "太空生物裤", "WolfMask": "狼面具", "WolfShirt": "狼衣", "WolfPants": "狼裤", "PumpkinMoonMedallion": "南瓜月勋章", "NecromanticScroll": "死灵卷轴", "JackingSkeletron": "骷髅夜惊魂", "BitterHarvest": "苦涩收获", "BloodMoonCountess": "血月伯爵夫人", "HallowsEve": "万圣节前夕", "IvyWhip": "常春藤鞭", "MorbidCuriosity": "病态好奇心", "TreasureHunterShirt": "寻宝人衣", "TreasureHunterPants": "寻宝人裤", "DryadCoverings": "树妖遮体服", "DryadLoincloth": "树妖腰布", "MourningWoodTrophy": "哀木纪念章", "PumpkingTrophy": "南瓜王纪念章", "JackOLanternMask": "杰克南瓜灯面具", "SniperScope": "狙击镜", "HeartLantern": "红心灯笼", "BreathingReed": "芦苇呼吸管", "JellyfishDivingGear": "水母潜水装备", "ArcticDivingGear": "北极潜水装备", "FrostsparkBoots": "霜花靴", "FartInABalloon": "臭屁气球", "PapyrusScarab": "甲虫莎草纸", "CelestialStone": "天界石", "Hoverboard": "悬浮板", "CandyCane": "糖棒", "SugarPlum": "蜜糖李", "Present": "礼物", "Flipper": "脚蹼", "RedRyder": "红莱德枪", "FestiveWings": "喜庆之翼", "PineTreeBlock": "松树块", "ChristmasTree": "圣诞树", "StarTopper1": "星星顶饰1", "StarTopper2": "星星顶饰2", "StarTopper3": "星星顶饰3", "BowTopper": "蝴蝶结顶饰", "WhiteGarland": "白花环", "WhiteAndRedGarland": "白红花环", "HealingPotion": "治疗药水", "RedGardland": "红花环", "RedAndGreenGardland": "红绿花环", "GreenGardland": "绿花环", "GreenAndWhiteGarland": "绿白花环", "MulticoloredBulb": "五彩灯泡", "RedBulb": "红灯泡", "YellowBulb": "黄灯泡", "GreenBulb": "绿灯泡", "RedAndGreenBulb": "红绿灯泡", "YellowAndGreenBulb": "黄绿灯泡", "ManaPotion": "魔力药水", "RedAndYellowBulb": "红黄灯泡", "WhiteBulb": "白灯泡", "WhiteAndRedBulb": "白红灯泡", "WhiteAndYellowBulb": "白黄灯泡", "WhiteAndGreenBulb": "白绿灯泡", "MulticoloredLights": "五彩串灯", "RedLights": "红串灯", "GreenLights": "绿串灯", "BlueLights": "蓝串灯", "YellowLights": "黄串灯", "GoldBar": "金锭", "BladeofGrass": "草剑", "RedAndYellowLights": "红黄串灯", "RedAndGreenLights": "红绿串灯", "YellowAndGreenLights": "黄绿串灯", "BlueAndGreenLights": "蓝绿串灯", "RedAndBlueLights": "红蓝串灯", "BlueAndYellowLights": "蓝黄串灯", "GiantBow": "巨型蝴蝶结", "ReindeerAntlers": "驯鹿角", "Holly": "冬青树", "CandyCaneSword": "糖棒剑", "ThornChakram": "荆棘旋刃", "ElfMelter": "精灵熔枪", "ChristmasPudding": "圣诞布丁", "Eggnog": "蛋酒", "StarAnise": "星形茴香", "ReindeerBells": "驯鹿铃铛", "CandyCaneHook": "糖棒钩", "ChristmasHook": "圣诞钩", "CnadyCanePickaxe": "糖棒镐", "FruitcakeChakram": "水果蛋糕旋刃", "SugarCookie": "蜜糖饼干", "ObsidianBrick": "黑曜石砖", "GingerbreadCookie": "姜饼", "HandWarmer": "暖手宝", "Coal": "煤", "Toolbox": "工具箱", "PineDoor": "松树门", "PineChair": "松树椅", "PineTable": "松树桌", "DogWhistle": "狗哨", "ChristmasTreeSword": "圣诞树剑", "ChainGun": "链式机枪", "ObsidianSkull": "黑曜石骷髅头", "Razorpine": "剃刀松", "BlizzardStaff": "暴雪法杖", "MrsClauseHat": "圣诞夫人帽", "MrsClauseShirt": "圣诞夫人衣", "MrsClauseHeels": "圣诞夫人高跟鞋", "ParkaHood": "派克兜帽", "ParkaCoat": "派克大衣", "ParkaPants": "派克裤", "SnowHat": "雪帽", "UglySweater": "丑毛衣", "MushroomGrassSeeds": "蘑菇草种子", "TreeMask": "树面具", "TreeShirt": "树衣", "TreeTrunks": "树干", "ElfHat": "精灵帽", "ElfShirt": "精灵衣", "ElfPants": "精灵裤", "SnowmanCannon": "雪人炮", "NorthPole": "北极", "ChristmasTreeWallpaper": "圣诞树壁纸", "OrnamentWallpaper": "装饰壁纸", "JungleGrassSeeds": "丛林草种子", "CandyCaneWallpaper": "糖棒壁纸", "FestiveWallpaper": "节日壁纸", "StarsWallpaper": "星星壁纸", "SquigglesWallpaper": "波浪条纹壁纸", "SnowflakeWallpaper": "雪花壁纸", "KrampusHornWallpaper": "坎卜斯犄角壁纸", "BluegreenWallpaper": "蓝绿壁纸", "GrinchFingerWallpaper": "格林奇手指壁纸", "NaughtyPresent": "调皮礼物", "BabyGrinchMischiefWhistle": "格林奇宝宝的恶作剧口哨", "WoodenHammer": "木锤", "IceQueenTrophy": "冰雪女王纪念章", "SantaNK1Trophy": "圣诞坦克纪念章", "EverscreamTrophy": "常绿尖叫怪纪念章", "MusicBoxPumpkinMoon": "八音盒（南瓜月）", "MusicBoxAltUnderground": "八音盒（地下备选曲）", "MusicBoxFrostMoon": "八音盒（霜月）", "BrownPaint": "棕漆", "ShadowPaint": "暗影漆", "NegativePaint": "反色漆", "TeamDye": "团队染料", "StarCannon": "星星炮", "AmethystGemsparkBlock": "紫晶晶莹宝石块", "TopazGemsparkBlock": "黄玉晶莹宝石块", "SapphireGemsparkBlock": "蓝玉晶莹宝石块", "EmeraldGemsparkBlock": "翡翠晶莹宝石块", "RubyGemsparkBlock": "红玉晶莹宝石块", "DiamondGemsparkBlock": "钻石晶莹宝石块", "AmberGemsparkBlock": "琥珀晶莹宝石块", "LifeHairDye": "生命染发剂", "ManaHairDye": "魔力染发剂", "DepthHairDye": "深度染发剂", "BluePhaseblade": "蓝陨石光剑", "MoneyHairDye": "钱币染发剂", "TimeHairDye": "时间染发剂", "TeamHairDye": "团队染发剂", "BiomeHairDye": "生物群系染发剂", "PartyHairDye": "派对染发剂", "RainbowHairDye": "彩虹染发剂", "SpeedHairDye": "速度染发剂", "AngelHalo": "天使光环", "Fez": "菲斯帽", "Womannquin": "女性人体模型", "RedPhaseblade": "红陨石光剑", "HairDyeRemover": "染发剂清除剂", "BugNet": "虫网", "Firefly": "萤火虫", "FireflyinaBottle": "萤火虫瓶", "MonarchButterfly": "帝王蝶", "PurpleEmperorButterfly": "紫蛱蝶", "RedAdmiralButterfly": "红蛱蝶", "UlyssesButterfly": "翠凤蝶", "SulphurButterfly": "黄粉蝶", "TreeNymphButterfly": "帛斑蝶", "DirtBlock": "土块", "CopperBar": "铜锭", "GreenPhaseblade": "绿陨石光剑", "ZebraSwallowtailButterfly": "带凤蝶", "JuliaButterfly": "珠袖蝶", "Worm": "蠕虫", "Mouse": "老鼠", "LightningBug": "荧光虫", "LightningBuginaBottle": "荧光虫瓶", "Snail": "蜗牛", "GlowingSnail": "发光蜗牛", "FancyGreyWallpaper": "别致灰壁纸", "IceFloeWallpaper": "浮冰壁纸", "PurplePhaseblade": "紫陨石光剑", "MusicWallpaper": "音符壁纸", "PurpleRainWallpaper": "紫雨壁纸", "RainbowWallpaper": "彩虹壁纸", "SparkleStoneWallpaper": "烁石壁纸", "StarlitHeavenWallpaper": "星光天堂壁纸", "Bird": "鸟", "BlueJay": "冠蓝鸦", "Cardinal": "红雀", "Squirrel": "松鼠", "Bunny": "兔兔", "WhitePhaseblade": "白陨石光剑", "YellowPhaseblade": "黄陨石光剑", "MeteorHamaxe": "流星锤斧", "EmptyBucket": "空桶", "WaterBucket": "水桶", "LavaBucket": "熔岩桶", "JungleRose": "丛林玫瑰", "Stinger": "毒刺", "SilverBar": "银锭", "Vine": "藤蔓", "FeralClaws": "猛爪手套", "BlacksmithRack": "铁匠架", "CarpentryRack": "木工架", "HelmetRack": "头盔架", "SpearRack": "长矛架", "SwordRack": "剑架", "StoneSlab": "石板", "AnkletoftheWind": "疾风脚镯", "SandstoneSlab": "沙岩板", "Frog": "青蛙", "MallardDuck": "野鸭", "Duck": "鸭", "StaffofRegrowth": "再生法杖", "HellstoneBrick": "狱石砖", "WhoopieCushion": "整蛊坐垫", "BlackScorpion": "黑蝎子", "Scorpion": "蝎子", "BubbleWallpaper": "泡泡壁纸", "CopperPipeWallpaper": "铜管壁纸", "Shackle": "镣铐", "DuckyWallpaper": "黄鸭壁纸", "FrostCore": "寒霜核", "BunnyCage": "兔兔笼", "SquirrelCage": "松鼠笼", "MallardDuckCage": "野鸭笼", "DuckCage": "鸭笼", "BirdCage": "鸟笼", "BlueJayCage": "冠蓝鸦笼", "CardinalCage": "红雀笼", "WaterfallWall": "瀑布墙", "MoltenHamaxe": "熔岩锤斧", "LavafallWall": "熔岩瀑布墙", "CrimsonSeeds": "猩红种子", "HeavyWorkBench": "重型工作台", "CopperPlating": "铜护板", "SnailCage": "蜗牛笼", "GlowingSnailCage": "发光蜗牛笼", "ShroomiteDiggingClaw": "蘑菇矿挖爪", "AmmoBox": "弹药箱", "MonarchButterflyJar": "帝王蝶罐", "PurpleEmperorButterflyJar": "紫蛱蝶罐", "Flamelash": "烈焰火鞭", "RedAdmiralButterflyJar": "红蛱蝶罐", "UlyssesButterflyJar": "翠凤蝶罐", "SulphurButterflyJar": "黄粉蝶罐", "TreeNymphButterflyJar": "帛斑蝶罐", "ZebraSwallowtailButterflyJar": "带凤蝶罐", "JuliaButterflyJar": "珠袖蝶罐", "ScorpionCage": "蝎子笼", "BlackScorpionCage": "黑蝎子笼", "VenomStaff": "毒液法杖", "SpectreMask": "幽灵面具", "PhoenixBlaster": "凤凰爆破枪", "FrogCage": "蛙笼", "MouseCage": "老鼠笼", "BoneWelder": "骨头焊机", "FleshCloningVaat": "血肉克隆台", "GlassKiln": "玻璃窑", "LihzahrdFurnace": "丛林蜥蜴熔炉", "LivingLoom": "生命木织机", "SkyMill": "天磨", "IceMachine": "冰雪机", "BeetleHelmet": "甲虫头盔", "IronBar": "铁锭", "Sunfury": "阳炎之怒", "BeetleScaleMail": "甲虫铠甲", "BeetleShell": "甲虫壳", "BeetleLeggings": "甲虫护腿", "SteampunkBoiler": "蒸汽朋克锅炉", "HoneyDispenser": "蜂蜜分配机", "Penguin": "企鹅", "PenguinCage": "企鹅笼", "WormCage": "蠕虫笼", "Terrarium": "泰拉饲养笼", "SuperManaPotion": "超级魔力药水", "Hellforge": "地狱熔炉", "EbonwoodFence": "乌木栅栏", "RichMahoganyFence": "红木栅栏", "PearlwoodFence": "珍珠木栅栏", "ShadewoodFence": "暗影木栅栏", "BrickLayer": "砌砖刀", "ExtendoGrip": "加长握爪", "PaintSprayer": "喷漆器", "PortableCementMixer": "便携式水泥搅拌机", "BeetleHusk": "甲虫外壳", "CelestialMagnet": "天界磁石", "ClayPot": "粘土盆", "CelestialEmblem": "天界徽章", "CelestialCuffs": "天界手铐", "PeddlersHat": "商贩帽", "PulseBow": "脉冲弓", "NaturesGift": "大自然的恩赐", "Bed": "床", "Silk": "丝绸", "DynastyTable": "王朝桌", "LesserRestorationPotion": "弱效恢复药水", "DynastyWood": "王朝木", "RedDynastyShingles": "红王朝瓦", "BlueDynastyShingles": "蓝王朝瓦", "WhiteDynastyWall": "白王朝墙", "BlueDynastyWall": "蓝王朝墙", "DynastyDoor": "王朝门", "Sake": "清酒", "PadThai": "泰式炒面", "Pho": "越南河粉", "Revolver": "左轮手枪", "RestorationPotion": "恢复药水", "Gatligator": "鳄鱼机关枪", "ArcaneRuneWall": "奥术神符墙", "WaterGun": "水枪", "Katana": "武士刀", "UltrabrightTorch": "超亮火把", "MagicHat": "魔法帽", "DiamondRing": "钻石戒指", "Gi": "稽古衣", "Kimono": "和服", "GypsyRobe": "神秘长袍", "JungleHat": "丛林帽", "BeetleWings": "甲虫之翼", "TigerSkin": "虎皮", "LeopardSkin": "豹皮", "ZebraSkin": "斑马皮", "CrimsonCloak": "猩红斗篷", "MysteriousCape": "神秘披风", "RedCape": "红披风", "WinterCape": "冬季披风", "WoodFishingPole": "木钓竿", "JungleShirt": "丛林衣", "Bass": "鲈鱼", "ReinforcedFishingPole": "强化钓竿", "FiberglassFishingPole": "玻璃钢钓竿", "FisherofSouls": "灵魂钓手", "GoldenFishingRod": "金钓竿", "MechanicsRod": "机械师钓竿", "SittingDucksFishingRod": "冤大头钓竿", "Trout": "鳟鱼", "Salmon": "三文鱼", "AtlanticCod": "大西洋鳕鱼", "Gel": "凝胶", "JunglePants": "丛林裤", "Tuna": "金枪鱼", "RedSnapper": "红鲷鱼", "NeonTetra": "霓虹脂鲤", "ArmoredCavefish": "装甲洞穴鱼", "Damselfish": "雀鲷", "CrimsonTigerfish": "猩红虎鱼", "FrostMinnow": "寒霜鲦鱼", "PrincessFish": "公主鱼", "GoldenCarp": "金鲤鱼", "SpecularFish": "镜面鱼", "MoltenHelmet": "熔岩头盔", "Prismite": "七彩矿鱼", "VariegatedLardfish": "斑驳油鱼", "FlarefinKoi": "闪鳍锦鲤", "DoubleCod": "双鳍鳕鱼", "Honeyfin": "蜂蜜鱼", "Obsidifish": "黑曜石鱼", "Shrimp": "虾", "ChaosFish": "混沌鱼", "Ebonkoi": "黑檀锦鲤", "Hemopiranha": "血腥食人鱼", "MoltenBreastplate": "熔岩胸甲", "Rockfish": "岩鱼锤", "Stinkfish": "臭味鱼", "MiningPotion": "挖矿药水", "HeartreachPotion": "拾心药水", "CalmingPotion": "镇静药水", "BuilderPotion": "建筑工药水", "TitanPotion": "泰坦药水", "FlipperPotion": "脚蹼药水", "SummoningPotion": "召唤药水", "TrapsightPotion": "危险感药水", "MoltenGreaves": "熔岩护胫", "PurpleClubberfish": "紫挥棒鱼", "ObsidianSwordfish": "黑曜石剑鱼", "Swordfish": "剑鱼", "IronFence": "铁栅栏", "WoodenCrate": "木匣", "IronCrate": "铁匣", "GoldenCrate": "金匣", "OldShoe": "旧鞋", "FishingSeaweed": "海草", "TinCan": "锡罐", "MeteorShot": "流星弹", "ReaverShark": "掠夺鲨", "SawtoothShark": "锯齿鲨", "Minecart": "矿车", "AmmoReservationPotion": "弹药储备药水", "LifeforcePotion": "生命力药水", "EndurancePotion": "耐力药水", "RagePotion": "暴怒药水", "InfernoPotion": "狱火药水", "WrathPotion": "怒气药水", "StickyBomb": "粘性炸弹", "RecallPotion": "回忆药水", "TeleportationPotion": "传送药水", "LovePotion": "爱情药水", "StinkPotion": "臭味药水", "FishingPotion": "钓鱼药水", "SonarPotion": "声呐药水", "CratePotion": "宝匣药水", "ShiverthornSeeds": "寒颤棘种子", "Shiverthorn": "寒颤棘", "WarmthPotion": "保暖药水", "BlackLens": "黑晶状体", "FishHook": "鱼钩", "BeeHeadgear": "蜜蜂头饰", "BeeBreastplate": "蜜蜂胸甲", "BeeGreaves": "蜜蜂护胫", "HornetStaff": "黄蜂法杖", "ImpStaff": "小鬼法杖", "QueenSpiderStaff": "蜘蛛女王法杖", "AnglerHat": "渔夫帽", "AnglerVest": "渔夫背心", "AnglerPants": "渔夫裤", "Sunglasses": "墨镜", "SpiderMask": "蜘蛛面具", "SpiderBreastplate": "蜘蛛胸甲", "SpiderGreaves": "蜘蛛护胫", "HighTestFishingLine": "优质钓鱼线", "AnglerEarring": "渔夫耳环", "TackleBox": "钓具箱", "BlueDungeonPiano": "蓝地牢钢琴", "GreenDungeonPiano": "绿地牢钢琴", "PinkDungeonPiano": "粉地牢钢琴", "GoldenPiano": "金钢琴", "WizardHat": "巫师帽", "ObsidianPiano": "黑曜石钢琴", "BonePiano": "骨头钢琴", "CactusPiano": "仙人掌钢琴", "SpookyPiano": "阴森钢琴", "SkywarePiano": "天域钢琴", "LihzahrdPiano": "丛林蜥蜴钢琴", "BlueDungeonDresser": "蓝地牢梳妆台", "GreenDungeonDresser": "绿地牢梳妆台", "PinkDungeonDresser": "粉地牢梳妆台", "GoldenDresser": "金梳妆台", "TopHat": "高顶礼帽", "ObsidianDresser": "黑曜石梳妆台", "BoneDresser": "骨头梳妆台", "CactusDresser": "仙人掌梳妆台", "SpookyDresser": "阴森梳妆台", "SkywareDresser": "天域梳妆台", "HoneyDresser": "蜂蜜梳妆台", "LihzahrdDresser": "丛林蜥蜴梳妆台", "Sofa": "沙发", "EbonwoodSofa": "乌木沙发", "RichMahoganySofa": "红木沙发", "WoodenSword": "木剑", "TuxedoShirt": "西装衣", "PearlwoodSofa": "珍珠木沙发", "ShadewoodSofa": "暗影木沙发", "BlueDungeonSofa": "蓝地牢沙发", "GreenDungeonSofa": "绿地牢沙发", "PinkDungeonSofa": "粉地牢沙发", "GoldenSofa": "金沙发", "ObsidianSofa": "黑曜石沙发", "BoneSofa": "骨头沙发", "CactusSofa": "仙人掌沙发", "SpookySofa": "阴森沙发", "TuxedoPants": "西装裤", "SkywareSofa": "天域沙发", "HoneySofa": "蜂蜜沙发", "SteampunkSofa": "蒸汽朋克沙发", "MushroomSofa": "蘑菇沙发", "GlassSofa": "玻璃沙发", "PumpkinSofa": "南瓜沙发", "LihzahrdSofa": "丛林蜥蜴沙发", "SeashellHairpin": "贝壳发夹", "MermaidAdornment": "美人鱼饰品", "MermaidTail": "美人鱼鱼尾裤", "SummerHat": "夏日草帽", "ZephyrFish": "和风鱼", "Fleshcatcher": "捕肉手", "HotlineFishingHook": "熔线钓钩", "FrogLeg": "蛙腿", "Anchor": "锚", "CookedFish": "熟鱼", "CookedShrimp": "熟虾", "Sashimi": "生鱼片", "BunnyHood": "兔兔兜帽", "BeeWax": "蜂蜡", "CopperPlatingWall": "铜护板墙", "StoneSlabWall": "石板墙", "Sail": "船帆", "CoralstoneBlock": "珊瑚石块", "BlueJellyfish": "蓝水母", "GreenJellyfish": "绿水母", "PinkJellyfish": "粉水母", "BlueJellyfishJar": "蓝水母罐", "PlumbersHat": "管道工帽", "GreenJellyfishJar": "绿水母罐", "PinkJellyfishJar": "粉水母罐", "PlumbersShirt": "管道工衣", "Batfish": "蝙蝠鱼", "BumblebeeTuna": "大黄蜂金枪鱼", "Catfish": "猫鱼", "Cloudfish": "云鱼", "Cursedfish": "诅咒鱼", "Dirtfish": "土鱼", "DynamiteFish": "雷管鱼", "EaterofPlankton": "浮游噬鱼", "FallenStarfish": "坠落星鱼", "TheFishofCthulu": "克苏鲁鱼", "PlumbersPants": "管道工背带裤", "Fishotron": "骷髅王鱼", "Harpyfish": "鸟妖鱼", "Hungerfish": "饿鬼鱼", "Ichorfish": "灵液鱼", "Jewelfish": "珠宝鱼", "MirageFish": "幻象鱼", "MutantFlinxfin": "突变雪怪鱼", "Pengfish": "企鹅鱼", "Pixiefish": "妖精鱼", "Spiderfish": "蜘蛛鱼", "HerosHat": "英雄帽", "TundraTrout": "苔原鳟鱼", "UnicornFish": "独角兽鱼", "GuideVoodooFish": "向导巫毒鱼", "Wyverntail": "飞龙尾", "ZombieFish": "僵尸鱼", "AmanitaFungifin": "毒菌鱼", "Angelfish": "天使鱼", "BloodyManowar": "血腥战神", "Bonefish": "骷髅鱼", "Bunnyfish": "兔兔鱼", "HerosShirt": "英雄衣", "CapnTunabeard": "金枪鱼须船长", "Clownfish": "小丑鱼", "DemonicHellfish": "恶魔地狱鱼", "Derpfish": "跳跳鱼", "Fishron": "猪龙鱼", "InfectedScabbardfish": "染病鞘鱼", "Mudfish": "泥鱼", "Slimefish": "史莱姆鱼", "TropicalBarracuda": "热带梭鱼", "KingSlimeTrophy": "史莱姆王纪念章", "HerosPants": "英雄裤", "ShipInABottle": "船舶瓶", "KingSlimeMask": "史莱姆王面具", "FinWings": "鳍翼", "TreasureMap": "宝藏地图", "SeaweedPlanter": "海草花盆", "PillaginMePixels": "海贼像素画", "FishCostumeMask": "鱼装面具", "FishCostumeShirt": "鱼装衣", "WoodenDoor": "木门", "FishBowl": "鱼缸", "FishCostumeFinskirt": "鱼装鳍裙", "GingerBeard": "姜须", "ArchaeologistsHat": "考古帽", "ArchaeologistsJacket": "考古夹克", "ArchaeologistsPants": "考古裤", "OpticStaff": "魔眼法杖", "BlackThread": "黑线", "GreenThread": "绿线", "NinjaHood": "忍者兜帽", "NinjaShirt": "忍者衣", "NinjaPants": "忍者裤", "Leather": "皮革", "StoneWall": "石墙", "RedHat": "红帽", "Goldfish": "金鱼", "Robe": "长袍", "RobotHat": "机器人帽", "GoldCrown": "金冠", "HellfireArrow": "狱炎箭", "Sandgun": "沙枪", "GuideVoodooDoll": "向导巫毒娃娃", "DivingHelmet": "潜水头盔", "FamiliarShirt": "便装衣", "Acorn": "橡实", "FamiliarPants": "便装裤", "FamiliarWig": "便装假发", "DemonScythe": "恶魔之镰", "NightsEdge": "永夜刃", "DarkLance": "暗黑长枪", "Coral": "珊瑚", "Cactus": "仙人掌", "Trident": "三叉戟", "SilverBullet": "银子弹", "ThrowingKnife": "投刀", "LesserHealingPotion": "弱效治疗药水", "Spear": "长矛", "Blowpipe": "吹管", "Glowstick": "荧光棒", "Seed": "种子", "WoodenBoomerang": "木制回旋镖", "Aglet": "鞋带束头", "StickyGlowstick": "粘性荧光棒", "PoisonedKnife": "毒刀", "ObsidianSkinPotion": "黑曜石皮药水", "RegenerationPotion": "再生药水", "AngryTrapperBanner": "愤怒捕手旗", "ArmoredVikingBanner": "装甲维京海盗旗", "BlackSlimeBanner": "黑史莱姆旗", "LifeCrystal": "生命水晶", "SwiftnessPotion": "敏捷药水", "BlueArmoredBonesBanner": "蓝装甲骷髅旗", "BlueCultistArcherBanner": "蓝邪教徒弓箭手旗", "BlueCultistCasterBanner": "拜月教忠教徒旗", "BlueCultistFighterBanner": "蓝邪教徒战士旗", "BoneLeeBanner": "骷髅李旗", "ClingerBanner": "爬藤怪旗", "CochinealBeetleBanner": "胭脂虫旗", "CorruptPenguinBanner": "腐化企鹅旗", "CorruptSlimeBanner": "腐化史莱姆旗", "CorruptorBanner": "腐化者旗", "GillsPotion": "鱼鳃药水", "CrimslimeBanner": "猩红史莱姆旗", "CursedSkullBanner": "诅咒骷髅头旗", "CyanBeetleBanner": "青壳虫旗", "DevourerBanner": "吞噬怪旗", "DiablolistBanner": "魔教徒旗", "DoctorBonesBanner": "骷髅博士旗", "DungeonSlimeBanner": "地牢史莱姆旗", "DungeonSpiritBanner": "地牢幽魂旗", "ElfArcherBanner": "精灵弓箭手旗", "ElfCopterBanner": "精灵直升机旗", "IronskinPotion": "铁皮药水", "EyezorBanner": "眼怪旗", "FlockoBanner": "雪花怪旗", "GhostBanner": "鬼魂旗", "GiantBatBanner": "巨型蝙蝠旗", "GiantCursedSkullBanner": "巨型诅咒骷髅头旗", "GiantFlyingFoxBanner": "巨型飞狐旗", "GingerbreadManBanner": "姜饼人旗", "GoblinArcherBanner": "哥布林弓箭手旗", "GreenSlimeBanner": "绿史莱姆旗", "HeadlessHorsemanBanner": "无头骑士旗", "ManaRegenerationPotion": "魔力再生药水", "HellArmoredBonesBanner": "地狱装甲骷髅旗", "HellhoundBanner": "地狱犬旗", "HoppinJackBanner": "弹跳杰克南瓜灯旗", "IceBatBanner": "冰雪蝙蝠旗", "IceGolemBanner": "冰雪巨人旗", "IceSlimeBanner": "冰雪史莱姆旗", "IchorStickerBanner": "灵液黏黏怪旗", "IlluminantBatBanner": "夜明蝙蝠旗", "IlluminantSlimeBanner": "夜明史莱姆旗", "JungleBatBanner": "丛林蝙蝠旗", "MagicPowerPotion": "魔能药水", "JungleSlimeBanner": "丛林史莱姆旗", "KrampusBanner": "坎卜斯旗", "LacBeetleBanner": "紫胶虫旗", "LavaBatBanner": "熔岩蝙蝠旗", "LavaSlimeBanner": "熔岩史莱姆旗", "MartianBrainscramblerBanner": "火星扰脑怪旗", "MartianDroneBanner": "火星飞船旗", "MartianEngineerBanner": "火星工程师旗", "MartianGigazapperBanner": "火星电击怪旗", "MartianGreyGruntBanner": "火星灰咕噜旗", "FeatherfallPotion": "羽落药水", "MartianOfficerBanner": "火星军官旗", "MartianRaygunnerBanner": "火星激光枪手旗", "MartianScutlixGunnerBanner": "火星鳞甲怪枪手旗", "MartianTeslaTurretBanner": "火星特斯拉炮塔旗", "MisterStabbyBanner": "戳刺先生旗", "MotherSlimeBanner": "史莱姆之母旗", "NecromancerBanner": "死灵法师旗", "NutcrackerBanner": "胡桃夹士旗", "PaladinBanner": "圣骑士旗", "PenguinBanner": "企鹅旗", "SpelunkerPotion": "洞穴探险药水", "PinkyBanner": "粉史莱姆旗", "PoltergeistBanner": "胡闹鬼旗", "PossessedArmorBanner": "装甲幻影魔旗", "PresentMimicBanner": "礼物宝箱怪旗", "PurpleSlimeBanner": "紫史莱姆旗", "RaggedCasterBanner": "褴褛邪教徒法师旗", "RainbowSlimeBanner": "彩虹史莱姆旗", "RavenBanner": "乌鸦旗", "RedSlimeBanner": "红史莱姆旗", "RuneWizardBanner": "符文巫师旗", "InvisibilityPotion": "隐身药水", "RustyArmoredBonesBanner": "生锈装甲骷髅旗", "ScarecrowBanner": "稻草人旗", "ScutlixBanner": "鳞甲怪旗", "SkeletonArcherBanner": "骷髅弓箭手旗", "SkeletonCommandoBanner": "骷髅突击手旗", "SkeletonSniperBanner": "骷髅狙击手旗", "SlimerBanner": "恶翅史莱姆旗", "SnatcherBanner": "抓人草旗", "SnowBallaBanner": "巴拉雪人旗", "SnowmanGangstaBanner": "雪人暴徒旗", "ShinePotion": "光芒药水", "SpikedIceSlimeBanner": "尖刺冰雪史莱姆旗", "SpikedJungleSlimeBanner": "尖刺丛林史莱姆旗", "SplinterlingBanner": "树精旗", "SquidBanner": "乌贼旗", "TacticalSkeletonBanner": "骷髅特警旗", "TheGroomBanner": "僵尸新郎旗", "TimBanner": "蒂姆旗", "UndeadMinerBanner": "不死矿工旗", "UndeadVikingBanner": "亡灵维京海盗旗", "WhiteCultistArcherBanner": "白邪教徒弓箭手旗", "NightOwlPotion": "夜猫子药水", "WhiteCultistCasterBanner": "白邪教徒法师旗", "WhiteCultistFighterBanner": "白邪教徒战士旗", "YellowSlimeBanner": "黄史莱姆旗", "YetiBanner": "雪兽旗", "ZombieElfBanner": "僵尸精灵旗", "StoneBlock": "石块", "DirtWall": "土墙", "BattlePotion": "战斗药水", "ThornsPotion": "荆棘药水", "WaterWalkingPotion": "水上漂药水", "ArcheryPotion": "箭术药水", "HunterPotion": "狩猎药水", "GravitationPotion": "重力药水", "GoldChest": "金箱", "DaybloomSeeds": "太阳花种子", "MoonglowSeeds": "月光草种子", "BlinkrootSeeds": "闪耀根种子", "Bottle": "玻璃瓶", "DeathweedSeeds": "死亡草种子", "WaterleafSeeds": "水叶草种子", "FireblossomSeeds": "火焰花种子", "Daybloom": "太阳花", "Moonglow": "月光草", "Blinkroot": "闪耀根", "Deathweed": "死亡草", "Waterleaf": "水叶草", "Fireblossom": "火焰花", "SharkFin": "鲨鱼鳍", "WoodenTable": "木桌", "Feather": "羽毛", "Tombstone": "墓石", "MimeMask": "丑角面具", "AntlionMandible": "蚁狮上颚", "IllegalGunParts": "非法枪械部件", "TheDoctorsShirt": "博士衣", "TheDoctorsPants": "博士裤", "GoldenKey": "金钥匙", "ShadowChest": "暗影箱", "ShadowKey": "暗影钥匙", "Furnace": "熔炉", "ObsidianBrickWall": "黑曜石砖墙", "JungleSpores": "丛林孢子", "Loom": "织布机", "Piano": "钢琴", "Dresser": "梳妆台", "Bench": "长椅", "Bathtub": "浴缸", "RedBanner": "红旗", "GreenBanner": "绿旗", "BlueBanner": "蓝旗", "WoodenChair": "木椅", "YellowBanner": "黄旗", "LampPost": "灯柱", "TikiTorch": "提基火把", "Barrel": "桶", "ChineseLantern": "中式灯笼", "CookingPot": "烹饪锅", "Safe": "保险箱", "SkullLantern": "骷髅头灯笼", "TrashCan": "垃圾桶", "PlatinumBow": "铂金弓", "PlatinumHammer": "铂金锤", "PlatinumAxe": "铂金斧", "PlatinumShortsword": "铂金短剑", "PlatinumBroadsword": "铂金宽剑", "PlatinumPickaxe": "铂金镐", "TungstenBow": "钨弓", "TungstenHammer": "钨锤", "TungstenAxe": "钨斧", "TungstenShortsword": "钨短剑", "Candelabra": "烛台", "TungstenBroadsword": "钨宽剑", "TungstenPickaxe": "钨镐", "LeadBow": "铅弓", "LeadHammer": "铅锤", "LeadAxe": "铅斧", "LeadShortsword": "铅短剑", "LeadBroadsword": "铅宽剑", "LeadPickaxe": "铅镐", "TinBow": "锡弓", "TinHammer": "锡锤", "IronAnvil": "铁砧", "PinkVase": "粉花瓶", "TinAxe": "锡斧", "TinShortsword": "锡短剑", "TinBroadsword": "锡宽剑", "TinPickaxe": "锡镐", "CopperBow": "铜弓", "CopperHammer": "铜锤", "CopperAxe": "铜斧", "CopperShortsword": "铜短剑", "CopperBroadsword": "铜阔剑", "CopperPickaxe": "铜镐", "Mug": "玻璃杯", "SilverBow": "银弓", "SilverHammer": "银锤", "SilverAxe": "银斧", "SilverShortsword": "银短剑", "SilverBroadsword": "银阔剑", "SilverPickaxe": "银镐", "GoldBow": "金弓", "GoldHammer": "金锤", "GoldAxe": "金斧", "GoldShortsword": "金短剑", "Keg": "酒桶", "GoldBroadsword": "金阔剑", "GoldPickaxe": "金镐", "Ale": "麦芽酒", "Bookcase": "书架", "Throne": "王座", "Bowl": "碗", "BowlofSoup": "鱼菇汤", "Toilet": "马桶", "GrandfatherClock": "落地大摆钟", "WorkBench": "工作台", "ArmorStatue": "盔甲雕像", "GoblinBattleStandard": "哥布林战旗", "TatteredCloth": "破布", "Sawmill": "锯木机", "CobaltOre": "钴矿", "MythrilOre": "秘银矿", "AdamantiteOre": "精金矿", "Pwnhammer": "神锤", "Excalibur": "断钢剑", "HallowedSeeds": "神圣种子", "Goggles": "护目镜", "EbonsandBlock": "黑檀沙块", "CobaltHat": "钴帽", "CobaltHelmet": "钴头盔", "CobaltMask": "钴面具", "CobaltBreastplate": "钴胸甲", "CobaltLeggings": "钴护腿", "MythrilHood": "秘银兜帽", "MythrilHelmet": "秘银头盔", "MythrilHat": "秘银帽", "MythrilChainmail": "秘银链甲", "Lens": "晶状体", "MythrilGreaves": "秘银护胫", "CobaltBar": "钴锭", "MythrilBar": "秘银锭", "CobaltChainsaw": "钴链锯", "MythrilChainsaw": "秘银链锯", "CobaltDrill": "钴钻头", "MythrilDrill": "秘银钻头", "AdamantiteChainsaw": "精金链锯", "AdamantiteDrill": "精金钻头", "DaoofPow": "太极连枷", "WoodenBow": "木弓", "MythrilHalberd": "秘银长戟", "AdamantiteBar": "精金锭", "GlassWall": "玻璃墙", "Compass": "罗盘", "DivingGear": "潜水装备", "GPS": "全球定位系统", "ObsidianHorseshoe": "黑曜石马掌", "ObsidianShield": "黑曜石护盾", "TinkerersWorkshop": "工匠作坊", "CloudinaBalloon": "云朵气球", "IronBroadsword": "铁阔剑", "WoodenArrow": "木箭", "AdamantiteArmor": "精金盔甲", "AdamantiteHeadgear": "精金头饰", "AdamantiteHelmet": "精金头盔", "AdamantiteMask": "精金面具", "AdamantiteBreastplate": "精金胸甲", "AdamantiteLeggings": "精金护腿", "SpectreBoots": "幽灵靴", "AdamantiteGlaive": "精金关刀", "Toolbelt": "工具腰带", "PearlsandBlock": "珍珠沙块", "PearlstoneBlock": "珍珠石块", "FlamingArrow": "烈焰箭", "MiningShirt": "挖矿衣", "MiningPants": "挖矿裤", "PearlstoneBrick": "珍珠石砖", "IridescentBrick": "荧光砖", "MudstoneBlock": "泥石砖", "CobaltBrick": "钴砖", "MythrilBrick": "秘银砖", "PearlstoneBrickWall": "珍珠石砖墙", "IridescentBrickWall": "荧光砖墙", "MudstoneBrickWall": "泥石砖墙", "Shuriken": "手里剑", "CobaltBrickWall": "钴砖墙", "MythrilBrickWall": "秘银砖墙", "HolyWater": "圣水", "UnholyWater": "邪水", "SiltBlock": "泥沙块", "FairyBell": "仙灵铃铛", "BreakerBlade": "毁灭刃", "BlueTorch": "蓝火把", "RedTorch": "红火把", "GreenTorch": "绿火把", "SuspiciousLookingEye": "可疑眼球", "PurpleTorch": "紫火把", "WhiteTorch": "白火把", "YellowTorch": "黄火把", "DemonTorch": "恶魔火把", "ClockworkAssaultRifle": "发条式突击步枪", "CobaltRepeater": "钴连弩", "MythrilRepeater": "秘银连弩", "DualHook": "双钩", "StarStatue": "星星雕像", "SwordStatue": "宝剑雕像", "DemonBow": "恶魔弓", "SlimeStatue": "史莱姆雕像", "GoblinStatue": "哥布林雕像", "ShieldStatue": "护盾雕像", "BatStatue": "蝙蝠雕像", "FishStatue": "金鱼雕像", "BunnyStatue": "兔兔雕像", "SkeletonStatue": "骷髅雕像", "ReaperStatue": "死神雕像", "WomanStatue": "女人雕像", "ImpStatue": "小鬼雕像", "WarAxeoftheNight": "暗夜战斧", "GargoyleStatue": "石像鬼雕像", "GloomStatue": "幽冥雕像", "HornetStatue": "黄蜂雕像", "BombStatue": "炸弹雕像", "CrabStatue": "螃蟹雕像", "HammerStatue": "战锤雕像", "PotionStatue": "药水雕像", "SpearStatue": "长矛雕像", "CrossStatue": "十字架雕像", "JellyfishStatue": "水母雕像", "LightsBane": "魔光剑", "BowStatue": "弓雕像", "BoomerangStatue": "回旋镖雕像", "BootStatue": "靴子雕像", "ChestStatue": "宝箱雕像", "BirdStatue": "鸟雕像", "AxeStatue": "战斧雕像", "CorruptStatue": "腐化雕像", "TreeStatue": "树木雕像", "AnvilStatue": "砧雕像", "PickaxeStatue": "镐雕像", "UnholyArrow": "邪箭", "MushroomStatue": "蘑菇雕像", "EyeballStatue": "魔眼雕像", "PillarStatue": "石柱雕像", "HeartStatue": "心形雕像", "PotStatue": "陶罐雕像", "SunflowerStatue": "向日葵雕像", "KingStatue": "国王雕像", "QueenStatue": "女王雕像", "PiranhaStatue": "食人鱼雕像", "PlankedWall": "板条墙", "Chest": "宝箱", "WoodenBeam": "木梁", "AdamantiteRepeater": "精金连弩", "AdamantiteSword": "精金剑", "CobaltSword": "钴剑", "MythrilSword": "秘银剑", "MoonCharm": "月光护身符", "Ruler": "标尺", "CrystalBall": "水晶球", "DiscoBall": "迪斯科球", "SorcererEmblem": "巫士徽章", "BandofRegeneration": "再生手环", "WarriorEmblem": "战士徽章", "RangerEmblem": "游侠徽章", "DemonWings": "恶魔之翼", "AngelWings": "天使之翼", "MagicalHarp": "魔法竖琴", "RainbowRod": "彩虹魔杖", "IceRod": "冰雪魔杖", "NeptunesShell": "海神贝壳", "Mannequin": "人体模型", "GreaterHealingPotion": "强效治疗药水", "Mushroom": "蘑菇", "MagicMirror": "魔镜", "GreaterManaPotion": "强效魔力药水", "PixieDust": "妖精尘", "CrystalShard": "水晶碎块", "ClownHat": "小丑帽", "ClownShirt": "小丑衣", "ClownPants": "小丑裤", "Flamethrower": "火焰喷射器", "Bell": "铃铛", "Harp": "竖琴", "Wrench": "红扳手", "JestersArrow": "小丑之箭", "WireCutter": "钢丝钳", "ActiveStoneBlock": "通电石块", "InactiveStoneBlock": "未通电石块", "Lever": "控制杆", "LaserRifle": "激光步枪", "CrystalBullet": "水晶子弹", "HolyArrow": "圣箭", "MagicDagger": "魔法飞刀", "CrystalStorm": "水晶风暴", "CursedFlames": "诅咒焰", "AngelStatue": "天使雕像", "SoulofLight": "光明之魂", "SoulofNight": "暗影之魂", "CursedFlame": "诅咒焰", "CursedTorch": "诅咒火把", "AdamantiteForge": "精金熔炉", "MythrilAnvil": "秘银砧", "UnicornHorn": "独角兽角", "DarkShard": "暗黑碎块", "LightShard": "光明碎块", "RedPressurePlate": "红压力板", "CloudinaBottle": "云朵瓶", "Wire": "电线", "SpellTome": "魔法书", "StarCloak": "星星斗篷", "Megashark": "巨兽鲨", "Shotgun": "霰弹枪", "PhilosophersStone": "点金石", "TitanGlove": "泰坦手套", "CobaltNaginata": "钴薙刀", "Switch": "开关", "DartTrap": "飞镖机关", "HermesBoots": "赫尔墨斯靴", "Boulder": "巨石", "GreenPressurePlate": "绿压力板", "GrayPressurePlate": "灰压力板", "BrownPressurePlate": "棕压力板", "MechanicalEye": "机械魔眼", "CursedArrow": "诅咒箭", "CursedBullet": "诅咒弹", "SoulofFright": "恐惧之魂", "SoulofMight": "力量之魂", "SoulofSight": "视域之魂", "EnchantedBoomerang": "附魔回旋镖", "Gungnir": "永恒之枪", "HallowedPlateMail": "神圣板甲", "HallowedGreaves": "神圣护胫", "HallowedHelmet": "神圣头盔", "CrossNecklace": "十字项链", "ManaFlower": "魔力花", "MechanicalWorm": "机械蠕虫", "MechanicalSkull": "机械骷髅头", "HallowedHeadgear": "神圣头饰", "HallowedMask": "神圣面具", "DemoniteOre": "魔矿", "SlimeCrown": "史莱姆王冠", "LightDisc": "光辉飞盘", "MusicBoxOverworldDay": "八音盒（地表世界）", "MusicBoxEerie": "八音盒（恐惧）", "MusicBoxNight": "八音盒（夜间）", "MusicBoxTitle": "八音盒（标题）", "MusicBoxUnderground": "八音盒（地下）", "MusicBoxBoss1": "八音盒（Boss 1）", "MusicBoxJungle": "八音盒（丛林）", "MusicBoxCorruption": "八音盒（腐化之地）", "DemoniteBar": "魔矿锭", "MusicBoxUndergroundCorruption": "八音盒（地下腐化之地）", "MusicBoxTheHallow": "八音盒（神圣之地）", "MusicBoxBoss2": "八音盒（Boss 2）", "MusicBoxUndergroundHallow": "八音盒（地下神圣之地）", "MusicBoxBoss3": "八音盒（Boss 3）", "SoulofFlight": "飞翔之魂", "MusicBox": "八音盒", "DemoniteBrick": "魔矿砖", "HallowedRepeater": "神圣连弩", "Drax": "斧钻", "Heart": "心", "Explosives": "炸药", "InletPump": "入水泵", "OutletPump": "出水泵", "Timer1Second": "1秒计时器", "Timer3Second": "3秒计时器", "Timer5Second": "5秒计时器", "CandyCaneBlock": "糖棒块", "CandyCaneWall": "糖棒墙", "SantaHat": "圣诞帽", "SantaShirt": "圣诞衣", "CorruptSeeds": "腐化种子", "SantaPants": "圣诞裤", "GreenCandyCaneBlock": "绿糖棒块", "GreenCandyCaneWall": "绿糖棒墙", "SnowBlock": "雪块", "SnowBrick": "雪砖", "SnowBrickWall": "雪砖墙", "BlueLight": "蓝灯", "RedLight": "红灯", "GreenLight": "绿灯", "BluePresent": "蓝礼物", "IronShortsword": "铁短剑", "VileMushroom": "魔菇", "GreenPresent": "绿礼物", "YellowPresent": "黄礼物", "SnowGlobe": "水晶雪球", "Carrot": "胡萝卜", "AdamantiteBeam": "精金梁", "AdamantiteBeamWall": "精金梁墙", "DemoniteBrickWall": "魔矿砖墙", "SandstoneBrick": "沙岩砖", "SandstoneBrickWall": "沙岩砖墙", "EbonstoneBrick": "黑檀石砖", "EbonstoneBlock": "黑檀石块", "EbonstoneBrickWall": "黑檀石砖墙", "RedStucco": "红泥灰", "YellowStucco": "黄泥灰", "GreenStucco": "绿泥灰", "GrayStucco": "灰泥灰", "RedStuccoWall": "红泥灰墙", "YellowStuccoWall": "黄泥灰墙", "GreenStuccoWall": "绿泥灰墙", "GrayStuccoWall": "灰泥灰墙", "Ebonwood": "乌木", "GrassSeeds": "草种子", "RichMahogany": "红木", "Pearlwood": "珍珠木", "EbonwoodWall": "乌木墙", "RichMahoganyWall": "红木墙", "PearlwoodWall": "珍珠木墙", "EbonwoodChest": "乌木箱", "RichMahoganyChest": "红木箱", "PearlwoodChest": "珍珠木箱", "EbonwoodChair": "乌木椅", "RichMahoganyChair": "红木椅", "Sunflower": "向日葵", "PearlwoodChair": "珍珠木椅", "EbonwoodPlatform": "乌木平台", "RichMahoganyPlatform": "红木平台", "PearlwoodPlatform": "珍珠木平台", "BonePlatform": "骨头平台", "EbonwoodWorkBench": "乌木工作台", "RichMahoganyWorkBench": "红木工作台", "PearlwoodWorkBench": "珍珠木工作台", "EbonwoodTable": "乌木桌", "RichMahoganyTable": "红木桌", "Vilethorn": "魔刺", "PearlwoodTable": "珍珠木桌", "EbonwoodPiano": "乌木钢琴", "RichMahoganyPiano": "红木钢琴", "PearlwoodPiano": "珍珠木钢琴", "EbonwoodBed": "乌木床", "RichMahoganyBed": "红木床", "PearlwoodBed": "珍珠木床", "EbonwoodDresser": "乌木梳妆台", "RichMahoganyDresser": "红木梳妆台", "PearlwoodDresser": "珍珠木梳妆台", "Starfury": "星怒", "EbonwoodDoor": "乌木门", "RichMahoganyDoor": "红木门", "PearlwoodDoor": "珍珠木门", "EbonwoodSword": "乌木剑", "EbonwoodHammer": "乌木锤", "EbonwoodBow": "乌木弓", "RichMahoganySword": "红木剑", "RichMahoganyHammer": "红木锤", "RichMahoganyBow": "红木弓", "PearlwoodSword": "珍珠木剑", "PurificationPowder": "净化粉", "PearlwoodHammer": "珍珠木锤", "PearlwoodBow": "珍珠木弓", "RainbowBrick": "彩虹砖", "RainbowBrickWall": "彩虹砖墙", "IceBlock": "冰雪块", "RedsWings": "Red的翅膀", "RedsHelmet": "Red的头盔", "RedsBreastplate": "Red的胸甲", "RedsLeggings": "Red的护腿", "Fish": "鱼", "VilePowder": "魔粉", "IceBoomerang": "冰雪回旋镖", "Keybrand": "钥匙剑", "Cutlass": "短弯刀", "TrueExcalibur": "真断钢剑", "TrueNightsEdge": "真永夜刃", "Frostbrand": "寒霜剑", "RedPotion": "红药水", "TacticalShotgun": "战术霰弹枪", "RottenChunk": "腐肉", "IvyChest": "常春藤箱", "IceChest": "冰冻箱", "Marrow": "骸骨弓", "UnholyTrident": "邪恶三叉戟", "FrostHelmet": "寒霜头盔", "FrostBreastplate": "寒霜胸甲", "FrostLeggings": "寒霜护腿", "TinHelmet": "锡头盔", "TinChainmail": "锡链甲", "TinGreaves": "锡护胫", "WormTooth": "蠕虫毒牙", "LeadHelmet": "铅头盔", "LeadChainmail": "铅链甲", "LeadGreaves": "铅护胫", "TungstenHelmet": "钨头盔", "TungstenChainmail": "钨链甲", "TungstenGreaves": "钨护胫", "PlatinumHelmet": "铂金头盔", "PlatinumChainmail": "铂金链甲", "PlatinumGreaves": "铂金护胫", "TinOre": "锡矿", "IronHammer": "铁锤", "WormFood": "蠕虫诱饵", "LeadOre": "铅矿", "TungstenOre": "钨矿", "PlatinumOre": "铂金矿", "TinBar": "锡锭", "LeadBar": "铅锭", "TungstenBar": "钨锭", "PlatinumBar": "铂金锭", "TinWatch": "锡表", "TungstenWatch": "钨表", "PlatinumWatch": "铂金表", "CopperCoin": "铜币", "TinChandelier": "锡吊灯", "TungstenChandelier": "钨吊灯", "PlatinumChandelier": "铂金吊灯", "PlatinumCandle": "铂金蜡烛", "PlatinumCandelabra": "铂金烛台", "PlatinumCrown": "铂金冠", "LeadAnvil": "铅砧", "TinBrick": "锡砖", "TungstenBrick": "钨砖", "PlatinumBrick": "铂金砖", "SilverCoin": "银币", "TinBrickWall": "锡砖墙", "TungstenBrickWall": "钨砖墙", "PlatinumBrickWall": "铂金砖墙", "BeamSword": "光束剑", "IceBlade": "冰雪刃", "IceBow": "冰雪弓", "FrostStaff": "寒霜法杖", "WoodHelmet": "木头盔", "WoodBreastplate": "木胸甲", "WoodGreaves": "木护胫", "GoldCoin": "金币", "EbonwoodHelmet": "乌木头盔", "EbonwoodBreastplate": "乌木胸甲", "EbonwoodGreaves": "乌木护胫", "RichMahoganyHelmet": "红木头盔", "RichMahoganyBreastplate": "红木胸甲", "RichMahoganyGreaves": "红木护胫", "PearlwoodHelmet": "珍珠木头盔", "PearlwoodBreastplate": "珍珠木胸甲", "PearlwoodGreaves": "珍珠木护胫", "AmethystStaff": "紫晶法杖", "PlatinumCoin": "铂金币", "TopazStaff": "黄玉法杖", "SapphireStaff": "蓝玉法杖", "EmeraldStaff": "翡翠法杖", "RubyStaff": "红玉法杖", "DiamondStaff": "钻石法杖", "GrassWall": "草墙", "JungleWall": "丛林墙", "FlowerWall": "花墙", "Jetpack": "喷气背包", "ButterflyWings": "蝴蝶之翼", "FallenStar": "坠落之星", "CactusWall": "仙人掌墙", "Cloud": "云", "CloudWall": "云墙", "Seaweed": "海草", "RuneHat": "符文帽", "RuneRobe": "符文长袍", "MushroomSpear": "蘑菇长矛", "TerraBlade": "泰拉刃", "GrenadeLauncher": "榴弹发射器", "RocketLauncher": "火箭发射器", "CopperGreaves": "铜护胫", "ProximityMineLauncher": "感应雷发射器", "FairyWings": "仙灵之翼", "SlimeBlock": "史莱姆块", "FleshBlock": "血肉块", "MushroomWall": "蘑菇墙", "RainCloud": "雨云", "BoneBlock": "骨块", "FrozenSlimeBlock": "冰冻史莱姆块", "BoneBlockWall": "骨块墙", "SlimeBlockWall": "史莱姆块墙", "IronGreaves": "铁护胫", "FleshBlockWall": "血肉块墙", "RocketI": "火箭一型", "RocketII": "火箭二型", "RocketIII": "火箭三型", "RocketIV": "火箭四型", "AsphaltBlock": "沥青块", "CobaltPickaxe": "钴镐", "MythrilPickaxe": "秘银镐", "AdamantitePickaxe": "精金镐", "Clentaminator": "环境改造枪", "SilverGreaves": "银护胫", "GreenSolution": "绿溶液", "BlueSolution": "蓝溶液", "PurpleSolution": "紫溶液", "DarkBlueSolution": "深蓝溶液", "RedSolution": "红溶液", "HarpyWings": "鸟妖之翼", "BoneWings": "骨之翼", "Hammush": "蘑菇锤", "NettleBurst": "爆裂藤蔓", "AnkhBanner": "十字章旗", "GoldGreaves": "金护胫", "SnakeBanner": "蛇旗", "OmegaBanner": "欧米茄旗", "CrimsonHelmet": "猩红头盔", "CrimsonScalemail": "猩红鳞甲", "CrimsonGreaves": "猩红护胫", "BloodButcherer": "血腥屠刀", "TendonBow": "肌腱弓", "FleshGrinder": "血肉锤", "DeathbringerPickaxe": "死亡使者镐", "BloodLustCluster": "嗜血狂斧", "Torch": "火把", "CopperChainmail": "铜链甲", "TheUndertaker": "夺命枪", "TheMeatball": "血肉之球", "TheRottedFork": "腐叉", "EskimoHood": "防雪兜帽", "EskimoCoat": "防雪大衣", "EskimoPants": "防雪裤", "LivingWoodChair": "生命木椅", "CactusChair": "仙人掌椅", "BoneChair": "骨头椅", "FleshChair": "血肉椅", "IronChainmail": "铁链甲", "MushroomChair": "蘑菇椅", "BoneWorkBench": "骨头工作台", "CactusWorkBench": "仙人掌工作台", "FleshWorkBench": "血肉工作台", "MushroomWorkBench": "蘑菇工作台", "SlimeWorkBench": "史莱姆工作台", "CactusDoor": "仙人掌门", "FleshDoor": "血肉门", "MushroomDoor": "蘑菇门", "LivingWoodDoor": "生命木门", "SilverChainmail": "银链甲", "BoneDoor": "骨头门", "FlameWings": "烈焰之翼", "FrozenWings": "冰冻之翼", "GhostWings": "幽灵之翼", "SunplateBlock": "日盘块", "DiscWall": "飞盘墙", "SkywareChair": "天域椅", "BoneTable": "骨头桌", "FleshTable": "血肉桌", "LivingWoodTable": "生命木桌", "GoldChainmail": "金链甲", "SkywareTable": "天域桌", "LivingWoodChest": "生命木箱", "LivingWoodWand": "生命木魔棒", "PurpleIceBlock": "紫冰雪块", "PinkIceBlock": "粉冰雪块", "RedIceBlock": "红冰雪块", "CrimstoneBlock": "猩红石块", "SkywareDoor": "天域门", "SkywareChest": "天域箱", "SteampunkHat": "蒸汽朋克帽", "GrapplingHook": "抓钩", "SteampunkShirt": "蒸汽朋克衣", "SteampunkPants": "蒸汽朋克裤", "BeeHat": "蜜蜂帽", "BeeShirt": "蜜蜂衣", "BeePants": "蜜蜂裤", "WorldBanner": "世界旗", "SunBanner": "太阳旗", "GravityBanner": "重力旗", "PharaohsMask": "法老面具", "Actuator": "致动器", "Chain": "链条", "BlueWrench": "蓝扳手", "GreenWrench": "绿扳手", "BluePressurePlate": "蓝压力板", "YellowPressurePlate": "黄压力板", "DiscountCard": "优惠卡", "LuckyCoin": "幸运币", "UnicornonaStick": "棒棒独角兽", "SandstorminaBottle": "沙暴瓶", "BeachBall": "沙滩球", "ShadowScale": "暗影鳞片", "CharmofMyths": "神话护身符", "MoonShell": "月亮贝壳", "StarVeil": "星星面纱", "WaterWalkingBoots": "水上漂靴", "Tiara": "头冠", "PrincessDress": "公主裙", "PharaohsRobe": "法老长袍", "GreenCap": "绿帽", "MushroomCap": "蘑菇帽", "TamOShanter": "苏格兰便帽", "PiggyBank": "猪猪存钱罐", "MummyMask": "木乃伊面具", "MummyShirt": "木乃伊衣", "MummyPants": "木乃伊裤", "CowboyHat": "牛仔帽", "CowboyJacket": "牛仔夹克", "CowboyPants": "牛仔裤", "PirateHat": "海盗帽", "PirateShirt": "海盗衣", "PiratePants": "海盗裤", "VikingHelmet": "维京海盗头盔", "MiningHelmet": "挖矿头盔", "CrimtaneOre": "猩红矿", "CactusSword": "仙人掌剑", "CactusPickaxe": "仙人掌镐", "IceBrick": "冰雪砖", "IceBrickWall": "冰雪砖墙", "AdhesiveBandage": "粘性绷带", "ArmorPolish": "盔甲抛光剂", "Bezoar": "牛黄", "Blindfold": "蒙眼布", "FastClock": "快走时钟", "CopperHelmet": "铜头盔", "Megaphone": "扩音器", "Nazar": "邪眼", "Vitamins": "维生素", "TrifoldMap": "三折地图", "CactusHelmet": "仙人掌头盔", "CactusBreastplate": "仙人掌胸甲", "CactusLeggings": "仙人掌护腿", "PowerGlove": "强力手套", "LightningBoots": "闪电靴", "SunStone": "太阳石", "Wood": "木材", "IronHelmet": "铁头盔", "MoonStone": "月亮石", "ArmorBracing": "盔甲背带", "MedicatedBandage": "药用绷带", "ThePlan": "计划书", "CountercurseMantra": "反诅咒咒语", "CoinGun": "钱币枪", "LavaCharm": "熔岩护身符", "ObsidianWaterWalkingBoots": "黑曜石水上漂靴", "LavaWaders": "熔岩靴", "PureWaterFountain": "纯净喷泉", "SilverHelmet": "银头盔", "DesertWaterFountain": "沙漠喷泉", "Shadewood": "暗影木", "ShadewoodDoor": "暗影木门", "ShadewoodPlatform": "暗影木平台", "ShadewoodChest": "暗影木箱", "ShadewoodChair": "暗影木椅", "ShadewoodWorkBench": "暗影木工作台", "ShadewoodTable": "暗影木桌", "ShadewoodDresser": "暗影木梳妆台", "ShadewoodPiano": "暗影木钢琴", "GoldHelmet": "金头盔", "ShadewoodBed": "暗影木床", "ShadewoodSword": "暗影木剑", "ShadewoodHammer": "暗影木锤", "ShadewoodBow": "暗影木弓", "ShadewoodHelmet": "暗影木头盔", "ShadewoodBreastplate": "暗影木胸甲", "ShadewoodGreaves": "暗影木护胫", "ShadewoodWall": "暗影木墙", "Cannon": "大炮", "Cannonball": "炮弹", "WoodWall": "木墙", "FlareGun": "信号枪", "Flare": "照明弹", "BoneWand": "白骨魔棒", "LeafWand": "树叶魔棒", "FlyingCarpet": "飞毯", "AvengerEmblem": "复仇者徽章", "MechanicalGlove": "机械手套", "LandMine": "地雷", "PaladinsShield": "圣骑士护盾", "WebSlinger": "蛛丝吊索", "WoodPlatform": "木平台", "JungleWaterFountain": "丛林喷泉", "IcyWaterFountain": "冰雪喷泉", "CorruptWaterFountain": "腐化喷泉", "CrimsonWaterFountain": "猩红喷泉", "HallowedWaterFountain": "神圣喷泉", "BloodWaterFountain": "血水喷泉", "Umbrella": "伞", "ChlorophyteOre": "叶绿矿", "SteampunkWings": "蒸汽朋克之翼", "Snowball": "雪球", "FlintlockPistol": "燧发枪", "IceSkates": "溜冰鞋", "SnowballLauncher": "雪球发射器", "WebCoveredChest": "蛛丝箱", "ClimbingClaws": "攀爬爪", "AncientIronHelmet": "远古铁头盔", "AncientGoldHelmet": "远古金头盔", "AncientShadowHelmet": "远古暗影头盔", "AncientShadowScalemail": "远古暗影鳞甲", "AncientShadowGreaves": "远古暗影护胫", "AncientNecroHelmet": "远古死灵头盔", "Musket": "火枪", "AncientCobaltHelmet": "远古钴头盔", "AncientCobaltBreastplate": "远古钴胸甲", "AncientCobaltLeggings": "远古钴护腿", "BlackBelt": "黑腰带", "Boomstick": "三发猎枪", "Rope": "绳", "Campfire": "篝火", "Marshmallow": "棉花糖", "MarshmallowonaStick": "棒棒棉花糖", "CookedMarshmallow": "熟棉花糖", "MusketBall": "火枪子弹", "RedRocket": "红火箭", "GreenRocket": "绿火箭", "BlueRocket": "蓝火箭", "YellowRocket": "黄火箭", "IceTorch": "冰雪火把", "ShoeSpikes": "鞋钉", "TigerClimbingGear": "猛虎攀爬装备", "Tabi": "分趾厚底袜", "PinkEskimoHood": "粉色防雪兜帽", "PinkEskimoCoat": "粉色防雪大衣", "Minishark": "迷你鲨", "PinkEskimoPants": "粉色防雪裤", "PinkThread": "粉线", "ManaRegenerationBand": "魔力再生手环", "SandstorminaBalloon": "沙暴气球", "MasterNinjaGear": "忍者大师装备", "RopeCoil": "绳圈", "Blowgun": "吹箭筒", "BlizzardinaBottle": "暴雪瓶", "FrostburnArrow": "霜冻箭", "EnchantedSword": "附魔剑", "IronBow": "铁弓", "PickaxeAxe": "镐斧", "CobaltWaraxe": "钴战斧", "MythrilWaraxe": "秘银战斧", "AdamantiteWaraxe": "精金战斧", "EatersBone": "吞噬怪骨头", "BlendOMatic": "搅拌机", "MeatGrinder": "绞肉机", "Extractinator": "提炼机", "Solidifier": "固化机", "Amber": "琥珀", "AcidDye": "酸性染料", "ActuationAccessory": "自动安放器", "ActuationRod": "致动魔杖", "AlchemyTable": "炼药桌", "AlphabetStatue0": "0字雕像", "AlphabetStatue1": "1字雕像", "AlphabetStatue2": "2字雕像", "AlphabetStatue3": "3字雕像", "AlphabetStatue4": "4字雕像", "AlphabetStatue5": "5字雕像", "AlphabetStatue6": "6字雕像", "AlphabetStatue7": "7字雕像", "AlphabetStatue8": "8字雕像", "AlphabetStatue9": "9字雕像", "AlphabetStatueA": "A字雕像", "AlphabetStatueB": "B字雕像", "AlphabetStatueC": "C字雕像", "AlphabetStatueD": "D字雕像", "AlphabetStatueE": "E字雕像", "AlphabetStatueF": "F字雕像", "AlphabetStatueG": "G字雕像", "AlphabetStatueH": "H字雕像", "AlphabetStatueI": "I字雕像", "AlphabetStatueJ": "J字雕像", "AlphabetStatueK": "K字雕像", "AlphabetStatueL": "L字雕像", "AlphabetStatueM": "M字雕像", "AlphabetStatueN": "N字雕像", "AlphabetStatueO": "O字雕像", "AlphabetStatueP": "P字雕像", "AlphabetStatueQ": "Q字雕像", "AlphabetStatueR": "R字雕像", "AlphabetStatueS": "S字雕像", "AlphabetStatueT": "T字雕像", "AlphabetStatueU": "U字雕像", "AlphabetStatueV": "V字雕像", "AlphabetStatueW": "W字雕像", "AlphabetStatueX": "X字雕像", "AlphabetStatueY": "Y字雕像", "AlphabetStatueZ": "Z字雕像", "Amarok": "冰雪悠悠球", "AmberGemsparkWall": "琥珀晶莹宝石墙", "AmberGemsparkWallOff": "黯淡琥珀晶莹宝石墙", "AmberStaff": "琥珀法杖", "AmethystGemsparkWall": "紫晶晶莹宝石墙", "AmethystGemsparkWallOff": "黯淡紫晶晶莹宝石墙", "AncientArmorHat": "远古头饰", "AncientArmorPants": "远古裤", "AncientArmorShirt": "远古上衣", "AncientBattleArmorHat": "禁戒面具", "AncientBattleArmorMaterial": "禁戒碎片", "AncientBattleArmorPants": "禁戒裤", "AncientBattleArmorShirt": "禁戒长袍", "AncientCloth": "远古布匹", "AncientCultistTrophy": "拜月教邪教徒纪念章", "AncientHorn": "远古号角", "AnglerTackleBag": "渔夫渔具袋", "AngryBonesBanner": "愤怒骷髅怪旗", "AnnouncementBox": "广播盒", "AntiGravityHook": "反重力钩", "AntlionClaw": "颌骨剑", "ApprenticeBait": "学徒诱饵", "ApprenticeHat": "学徒帽", "ApprenticeRobe": "学徒长袍", "ApprenticeScarf": "学徒围巾", "ApprenticeTrousers": "学徒裤", "ArchitectGizmoPack": "建筑师发明背包", "Arkhalis": "<PERSON><PERSON><PERSON>剑", "AviatorSunglasses": "0x33的飞行员风镜", "Bacon": "培根", "BalloonHorseshoeFart": "绿马掌气球", "BalloonHorseshoeHoney": "琥珀马掌气球", "BalloonHorseshoeSharkron": "粉马掌气球", "BalloonPufferfish": "气球河豚鱼", "BeeMask": "蜂王面具", "BeesKnees": "蜂膝弓", "BejeweledValkyrieBody": "Lazure的女武神斗蓬", "BejeweledValkyrieHead": "Lazure的女武神头环", "BejeweledValkyrieWing": "Lazure的屏障台", "BewitchingTable": "施法桌", "BlackAndWhiteDye": "黑白染料", "BlackCounterweight": "黑平衡锤", "BlackString": "黑绳", "Bladetongue": "舌锋剑", "BlessedApple": "恩赐苹果", "BlinkrootPlanterBox": "闪耀根种植盆", "BloodWater": "血水", "BloodZombieBanner": "血腥僵尸旗", "BloodZombieStatue": "血腥僵尸雕像", "BlueAcidDye": "蓝酸性染料", "BlueCounterweight": "蓝平衡锤", "BlueDungeonBathtub": "蓝地牢浴缸", "BlueDungeonCandelabra": "蓝地牢烛台", "BlueDungeonChandelier": "蓝地牢吊灯", "BlueDungeonChest": "蓝地牢箱", "BlueDungeonLamp": "蓝地牢灯", "BlueDungeonSink": "蓝地牢水槽", "BlueFlameAndSilverDye": "蓝焰银染料", "BlueLunaticHood": "月亮邪教徒兜帽", "BlueLunaticRobe": "月亮邪教徒长袍", "BluePhasesaber": "蓝晶光刃", "BlueString": "蓝绳", "BombFish": "炸弹鱼", "BoneArrow": "骨箭", "BoneBathtub": "骨头浴缸", "BoneBed": "骨头床", "BoneBookcase": "骨头书架", "BoneCampfire": "骨头篝火", "BoneCandelabra": "骨头烛台", "BoneChandelier": "骨头吊灯", "BoneChest": "骨箱", "BoneClock": "骨头时钟", "BoneDagger": "骨投刀", "BoneGlove": "骨头手套", "BoneJavelin": "骨头标枪", "BoneLamp": "骨头灯", "BoneLantern": "骨头灯笼", "BoneRattle": "骨头拨浪鼓", "BoneSink": "骨头水槽", "BoneSkeletonStatue": "骨头骷髅雕像", "BoneTorch": "骨头火把", "BoosterTrack": "增速轨道", "BorealWood": "针叶木", "BorealWoodBathtub": "针叶木浴缸", "BorealWoodBed": "针叶木床", "BorealWoodBookcase": "针叶木书架", "BorealWoodBow": "针叶木弓", "BorealWoodBreastplate": "针叶木胸甲", "BorealWoodCandelabra": "针叶木烛台", "BorealWoodCandle": "针叶木蜡烛", "BorealWoodChair": "针叶木椅", "BorealWoodChandelier": "针叶木吊灯", "BorealWoodChest": "针叶木箱", "BorealWoodClock": "针叶木时钟", "BorealWoodDoor": "针叶木门", "BorealWoodDresser": "针叶木梳妆台", "BorealWoodFence": "针叶木栅栏", "BorealWoodGreaves": "针叶木护胫", "BorealWoodHammer": "针叶木锤", "BorealWoodHelmet": "针叶木头盔", "BorealWoodLamp": "针叶木灯", "BorealWoodLantern": "针叶木灯笼", "BorealWoodPiano": "针叶木钢琴", "BorealWoodPlatform": "针叶木平台", "BorealWoodSink": "针叶木水槽", "BorealWoodSofa": "针叶木沙发", "BorealWoodSword": "针叶木剑", "BorealWoodTable": "针叶木桌", "BorealWoodWall": "针叶木墙", "BorealWoodWorkBench": "针叶木工作台", "BossMaskCultist": "拜月教邪教徒面具", "BossMaskMoonlord": "月亮领主面具", "BottomlessBucket": "无底水桶", "BouncyBomb": "弹力炸弹", "BouncyDynamite": "弹力雷管", "BouncyGlowstick": "弹力荧光棒", "BouncyGrenade": "弹力手榴弹", "BrainMask": "克苏鲁之脑面具", "BrainOfConfusion": "混乱之脑", "BrainOfCthulhuBossBag": "宝藏袋（克苏鲁之脑）", "BrainScrambler": "扰脑器", "BrightBrownDye": "淡棕染料", "BrightSilverDye": "淡银染料", "BrownAndBlackDye": "棕黑染料", "BrownAndSilverDye": "棕银染料", "BrownDye": "棕染料", "BrownString": "棕绳", "Bubble": "泡泡", "BubbleGun": "泡泡枪", "BuccaneerBandana": "西域海盗头巾", "BuccaneerPants": "西域海盗马裤", "BuccaneerShirt": "西域海盗上装", "Buggy": "蚜虫", "BuggyStatue": "蚜虫雕像", "BunnyfishTrophy": "兔兔鱼纪念章", "BurningHadesDye": "烈焰冥王染料", "ButcherBanner": "屠夫旗", "ButchersChainsaw": "屠夫链锯", "ButterflyStatue": "蝴蝶雕像", "CactusBathtub": "仙人掌浴缸", "CactusBed": "仙人掌床", "CactusBookcase": "仙人掌书架", "CactusCandelabra": "仙人掌烛台", "CactusCandle": "仙人掌蜡烛", "CactusChandelier": "仙人掌吊灯", "CactusChest": "仙人掌箱", "CactusClock": "仙人掌时钟", "CactusLamp": "仙人掌灯", "CactusLantern": "仙人掌灯笼", "CactusPlatform": "仙人掌平台", "CactusSink": "仙人掌水槽", "CactusTable": "仙人掌桌", "CageBuggy": "蚜虫笼", "CageEnchantedNightcrawler": "附魔夜行者笼", "CageGrubby": "蛆虫笼", "CageSluggy": "鼻涕虫笼", "Cascade": "喷流球", "CelestialShell": "天界贝壳", "CelestialSigil": "天界符", "CellPhone": "手机", "ChainGuillotines": "铁链血滴子", "ChargedBlasterCannon": "充能爆破炮", "Chik": "吉克球", "Chimney": "烟囱", "ChlorophyteBrick": "叶绿砖", "ChlorophyteBrickWall": "叶绿砖墙", "ChlorophyteDye": "叶绿染料", "ClingerStaff": "爬藤怪法杖", "ClothierJacket": "服装商夹克", "ClothierPants": "服装商裤", "Code1": "代码1球", "Code2": "代码2球", "CogWall": "齿轮墙", "CoinRing": "钱币戒指", "CompanionCube": "同伴方块", "CompassRose": "罗盘针", "ConfettiBlock": "彩纸块", "ConfettiBlockBlack": "午夜彩纸块", "ConfettiCannon": "彩纸炮", "ConfettiWall": "彩纸墙", "ConfettiWallBlack": "午夜彩纸墙", "ConveyorBeltLeft": "传送带（顺时针）", "ConveyorBeltRight": "传送带（逆时针）", "CordageGuide": "植物纤维绳索宝典", "CorruptFishingCrate": "腐化匣", "CorruptHardenedSand": "硬化黑檀沙块", "CorruptHardenedSandWall": "硬化黑檀沙墙", "CorruptPlanterBox": "死亡草种植盆", "CorruptSandstone": "黑檀沙岩块", "CorruptSandstoneWall": "黑檀沙岩墙", "CorruptYoyo": "抑郁球", "CosmicCarKey": "宇宙车钥匙", "CrawdadBanner": "龙虾旗", "CreatureFromTheDeepBanner": "水月怪旗", "CrimsonFishingCrate": "猩红匣", "CrimsonHardenedSand": "硬化猩红沙块", "CrimsonHardenedSandWall": "硬化猩红沙墙", "CrimsonHeart": "猩红之心", "CrimsonPlanterBox": "死亡草种植盆", "CrimsonSandstone": "猩红沙岩块", "CrimsonSandstoneWall": "猩红沙岩墙", "CrimsonYoyo": "血脉球", "CrimtaneBrick": "猩红矿砖", "CrimtaneBrickWall": "猩红矿砖墙", "CrystalBlock": "水晶块", "CrystalBlockWall": "水晶块墙", "CrystalDart": "水晶镖", "CrystalSerpent": "水晶蛇", "CrystalVileShard": "魔晶碎块", "CultistBossBag": "宝藏袋（拜月教邪教徒）", "CursedCampfire": "诅咒篝火", "CursedDart": "诅咒镖", "CyanString": "青绳", "DaedalusStormbow": "代达罗斯风暴弓", "DarkMummyBanner": "暗黑木乃伊旗", "DartPistol": "飞镖手枪", "DartRifle": "飞镖步枪", "DayBloomPlanterBox": "太阳花种植盆", "DayBreak": "破晓之光", "DeadlySphereBanner": "致命球旗", "DeadlySphereStaff": "致命球法杖", "DefenderMedal": "护卫奖章", "DefendersForge": "护卫熔炉", "DemonCampfire": "恶魔篝火", "DemonHeart": "恶魔之心", "DesertBasiliskBanner": "蛇蜥怪旗", "DesertDjinnBanner": "沙漠幽魂旗", "DesertFossil": "沙漠化石", "DesertFossilWall": "沙漠化石墙", "DesertGhoulBanner": "食尸鬼旗", "DesertLamiaBanner": "拉弥亚旗", "DestroyerBossBag": "宝藏袋（毁灭者）", "DestroyerMask": "毁灭者面具", "Detonator": "引爆器", "DevDye": "Skiphs的血", "DiamondGemsparkWall": "钻石晶莹宝石墙", "DiamondGemsparkWallOff": "黯淡钻石晶莹宝石墙", "DjinnLamp": "沙漠幽魂灯", "DjinnsCurse": "神灵诅咒", "DPSMeter": "每秒伤害计数器", "DrillContainmentUnit": "钻头控制装置", "DripplerBanner": "滴滴怪旗", "DripplerStatue": "滴滴怪雕像", "DrManFlyBanner": "苍蝇人博士旗", "DuckStatue": "鸭雕像", "DukeFishronMask": "猪龙鱼公爵面具", "DukeFishronTrophy": "猪龙鱼公爵纪念章", "DuneSplicerBanner": "沙虫旗", "DungeonFishingCrate": "地牢匣", "DyeTradersScimitar": "异域弯刀", "DyeTraderTurban": "染料商头巾", "DynastyBathtub": "王朝浴缸", "DynastyBed": "王朝床", "DynastyBookcase": "王朝书架", "DynastyBowl": "王朝碗", "DynastyCandelabra": "大王朝蜡烛", "DynastyCandle": "王朝蜡烛", "DynastyChair": "王朝椅", "DynastyChandelier": "大王朝灯笼", "DynastyChest": "王朝箱", "DynastyClock": "王朝时钟", "DynastyCup": "王朝杯", "DynastyLamp": "王朝灯", "DynastyLantern": "王朝灯笼", "DynastySink": "王朝水槽", "DynastyWorkBench": "王朝工作台", "EaterMask": "世界吞噬怪面具", "EaterOfWorldsBossBag": "宝藏袋（世界吞噬怪）", "EbonwoodBathtub": "乌木浴缸", "EbonwoodBookcase": "乌木书架", "EbonwoodCandelabra": "乌木烛台", "EbonwoodCandle": "乌木蜡烛", "EbonwoodChandelier": "乌木吊灯", "EbonwoodClock": "乌木时钟", "EbonwoodLamp": "乌木灯", "EbonwoodLantern": "乌木灯笼", "EbonwoodSink": "乌木水槽", "ElectrosphereLauncher": "电圈发射器", "EmeraldGemsparkWall": "翡翠晶莹宝石墙", "EmeraldGemsparkWallOff": "黯淡翡翠晶莹宝石墙", "EmptyDropper": "空滴管", "EnchantedNightcrawler": "附魔夜行者", "EndlessMusketPouch": "无尽火枪袋", "EndlessQuiver": "无尽箭袋", "EngineeringHelmet": "工程头盔", "EoCShield": "克苏鲁护盾", "EyeMask": "克苏鲁之眼面具", "EyeOfCthulhuBossBag": "宝藏袋（克苏鲁之眼）", "Fake_BlueDungeonChest": "机关蓝地牢箱", "Fake_BoneChest": "机关骨箱", "Fake_BorealWoodChest": "机关针叶木箱", "Fake_CactusChest": "机关仙人掌箱", "Fake_Chest": "机关宝箱", "Fake_CorruptionChest": "机关腐化箱", "Fake_CrimsonChest": "机关猩红箱", "Fake_DynastyChest": "机关王朝箱", "Fake_EbonwoodChest": "机关乌木箱", "Fake_FleshChest": "机关血肉箱", "Fake_FrozenChest": "机关冰雪箱", "Fake_GlassChest": "机关玻璃箱", "Fake_GoldChest": "机关金箱", "Fake_GraniteChest": "机关花岗岩箱", "Fake_GreenDungeonChest": "机关绿地牢箱", "Fake_HallowedChest": "机关神圣箱", "Fake_HoneyChest": "机关蜂蜜箱", "Fake_IceChest": "机关冰冻箱", "Fake_IvyChest": "机关常春藤箱", "Fake_JungleChest": "机关丛林箱", "Fake_LihzahrdChest": "机关丛林蜥蜴箱", "Fake_LivingWoodChest": "机关生命木箱", "Fake_MarbleChest": "机关大理石箱", "Fake_MartianChest": "机关火星箱", "Fake_MeteoriteChest": "机关陨石箱", "Fake_MushroomChest": "机关蘑菇箱", "Fake_ObsidianChest": "机关黑曜石箱", "Fake_PalmWoodChest": "机关棕榈木箱", "Fake_PearlwoodChest": "机关珍珠木箱", "Fake_PinkDungeonChest": "机关粉地牢箱", "Fake_PumpkinChest": "机关南瓜箱", "Fake_RichMahoganyChest": "机关红木箱", "Fake_ShadewoodChest": "机关暗影木箱", "Fake_ShadowChest": "机关暗影箱", "Fake_SkywareChest": "机关天域箱", "Fake_SlimeChest": "机关史莱姆箱", "Fake_SpookyChest": "机关阴森箱", "Fake_SteampunkChest": "机关蒸汽朋克箱", "Fake_WaterChest": "机关水中箱", "Fake_WebCoveredChest": "机关蛛丝箱", "FalconBlade": "猎鹰刃", "FallenTuxedoPants": "堕落西装裤", "FallenTuxedoShirt": "堕落西装衣", "FancyDishes": "精致餐具", "FetidBaghnakhs": "臭虎爪", "FireBlossomPlanterBox": "火焰花种植盆", "FireflyStatue": "萤火虫雕像", "Fireplace": "壁炉", "FireworkFountain": "烟花喷泉", "FireworksBox": "烟花盒", "FireworksLauncher": "喜庆弹射器", "FishermansGuide": "渔民袖珍宝典", "FishFinder": "探鱼器", "FishronBossBag": "宝藏袋（猪龙鱼公爵）", "FishronWings": "猪龙鱼之翼", "Flairon": "猪鲨链球", "FlameAndSilverDye": "红焰银染料", "FleshBathtub": "血肉浴缸", "FleshBed": "血肉床", "FleshBookcase": "血肉书架", "FleshCandelabra": "血肉烛台", "FleshCandle": "血肉蜡烛", "FleshChandelier": "血肉吊灯", "FleshChest": "血肉箱", "FleshClock": "血肉时钟", "FleshDresser": "血肉梳妆台", "FleshKnuckles": "血肉指虎", "FleshLamp": "血肉灯", "FleshLantern": "血肉灯笼", "FleshMask": "血肉墙面具", "FleshPiano": "血肉钢琴", "FleshSink": "血肉水槽", "FleshSofa": "血肉沙发", "FloatingIslandFishingCrate": "天空匣", "FlowerBoots": "花靴", "FlowerBoyHat": "呆萌向日葵花瓣头盔", "FlowerBoyPants": "呆萌向日葵裤装", "FlowerBoyShirt": "呆萌向日葵上衣", "FlyingAntlionBanner": "蚁狮蜂旗", "FlyingDutchmanTrophy": "荷兰飞盗船纪念章", "FlyingKnife": "飞刀", "FormatC": "好胜球", "FossilHelm": "化石头盔", "FossilOre": "坚固化石", "FossilPants": "化石护胫", "FossilShirt": "化石板甲", "FragmentNebula": "星云碎片", "FragmentSolar": "日耀碎片", "FragmentStardust": "星尘碎片", "FragmentVortex": "星旋碎片", "FritzBanner": "弗里茨旗", "FrogStatue": "青蛙雕像", "FrostDaggerfish": "寒霜飞鱼", "FrozenBathtub": "冰冻浴缸", "FrozenBed": "冰冻床", "FrozenBookcase": "冰冻书架", "FrozenCampfire": "冰冻篝火", "FrozenCandelabra": "冰冻烛台", "FrozenCandle": "冰冻蜡烛", "FrozenChair": "冰冻椅", "FrozenChandelier": "冰冻吊灯", "FrozenClock": "冰冻时钟", "FrozenDoor": "冰冻门", "FrozenLamp": "冰冻灯", "FrozenLantern": "冰冻灯笼", "FrozenPiano": "冰冻钢琴", "FrozenSink": "冰冻水槽", "FrozenSofa": "冰冻沙发", "FrozenTable": "冰冻桌", "FrozenWorkBench": "冰冻工作台", "FuzzyCarrot": "绒毛胡萝卜", "GelDye": "凝胶染料", "GemLockAmber": "琥珀宝石锁", "GemLockAmethyst": "紫晶宝石锁", "GemLockDiamond": "钻石宝石锁", "GemLockEmerald": "翡翠宝石锁", "GemLockRuby": "红玉宝石锁", "GemLockSapphire": "蓝玉宝石锁", "GemLockTopaz": "黄玉宝石锁", "GenderChangePotion": "变性药水", "GeyserTrap": "热喷泉", "GiantShellyBanner": "巨型卷壳怪旗", "GladiatorBreastplate": "角斗士胸甲", "GladiatorHelmet": "角斗士头盔", "GladiatorLeggings": "角斗士护腿", "GlassBathtub": "玻璃浴缸", "GlassBookcase": "玻璃书架", "GlassBowl": "玻璃碗", "GlassCandelabra": "玻璃烛台", "GlassCandle": "玻璃蜡烛", "GlassChandelier": "玻璃吊灯", "GlassChest": "玻璃箱", "GlassClock": "玻璃时钟", "GlassDresser": "玻璃梳妆台", "GlassLamp": "玻璃灯", "GlassLantern": "玻璃灯笼", "GlassPiano": "玻璃钢琴", "GlassSink": "玻璃水槽", "GlassWorkBench": "玻璃工作台", "GoblinSummonerBanner": "哥布林术士旗", "GoblinTech": "哥布林数据仪", "GoldBird": "金鸟", "GoldBirdCage": "金鸟笼", "GoldBunny": "金兔", "GoldBunnyCage": "金兔笼", "GoldButterfly": "金蝴蝶", "GoldButterflyCage": "金蝴蝶罐", "GoldenBathtub": "金浴缸", "GoldenBookcase": "金书架", "GoldenBugNet": "金虫网", "GoldenCandelabra": "金烛台", "GoldenCandle": "金蜡烛", "GoldenChandelier": "金制吊灯", "GoldenClock": "金时钟", "GoldenLamp": "金灯", "GoldenLantern": "金灯笼", "GoldenSink": "金水槽", "GoldfishTrophy": "金鱼纪念章", "GoldFrog": "金蛙", "GoldFrogCage": "金蛙笼", "GoldGrasshopper": "金蚱蜢", "GoldGrasshopperCage": "金蚱蜢笼", "GoldMouse": "金老鼠", "GoldMouseCage": "金老鼠笼", "GoldRing": "金戒指", "GoldWorm": "金蠕虫", "GoldWormCage": "金蠕虫笼", "GolemBossBag": "宝藏袋（石巨人）", "GolemMask": "石巨人面具", "Gradient": "渐变球", "Granite": "花岗岩块", "GraniteBathtub": "花岗岩浴缸", "GraniteBed": "花岗岩床", "GraniteBlock": "光面花岗岩块", "GraniteBlockWall": "光面花岗岩墙", "GraniteBookcase": "花岗岩书架", "GraniteCandelabra": "花岗岩烛台", "GraniteCandle": "花岗岩蜡烛", "GraniteChair": "花岗岩椅", "GraniteChandelier": "花岗岩吊灯", "GraniteChest": "花岗岩箱", "GraniteClock": "花岗岩时钟", "GraniteDoor": "花岗岩门", "GraniteDresser": "花岗岩梳妆台", "GraniteFlyerBanner": "花岗精旗", "GraniteGolemBanner": "花岗岩巨人旗", "GraniteGolemStatue": "花岗岩巨人雕像", "GraniteLamp": "花岗岩灯", "GraniteLantern": "花岗岩灯笼", "GranitePiano": "花岗岩钢琴", "GranitePlatform": "花岗岩平台", "GraniteSink": "花岗岩水槽", "GraniteSofa": "花岗岩沙发", "GraniteTable": "花岗岩桌", "GraniteWall": "花岗岩墙", "GraniteWorkBench": "花岗岩工作台", "Grasshopper": "蚱蜢", "GrasshopperCage": "蚱蜢笼", "GrasshopperStatue": "蚱蜢雕像", "GreedyRing": "贪婪戒指", "GreekSkeletonBanner": "装甲步兵旗", "GreenCounterweight": "绿平衡锤", "GreenDungeonBathtub": "绿地牢浴缸", "GreenDungeonCandelabra": "绿地牢烛台", "GreenDungeonChandelier": "绿地牢吊灯", "GreenDungeonChest": "绿地牢箱", "GreenDungeonLamp": "绿地牢灯", "GreenDungeonSink": "绿地牢水槽", "GreenFlameAndSilverDye": "绿焰银染料", "GreenJellyfishBanner": "绿水母旗", "GreenPhasesaber": "绿晶光刃", "GreenString": "绿绳", "GrimDye": "恐怖染料", "Grubby": "蛆虫", "GrubSoup": "蛆虫汤", "HadesDye": "冥王染料", "HallowedFishingCrate": "神圣匣", "HallowHardenedSand": "硬化珍珠沙块", "HallowHardenedSandWall": "硬化珍珠沙墙", "HallowSandstone": "珍珠沙岩块", "HallowSandstoneWall": "珍珠沙岩墙", "HardenedSand": "硬化沙块", "HardenedSandWall": "硬化沙墙", "HardySaddle": "硬鞍", "HarpyStatue": "鸟妖雕像", "HelFire": "狱火球", "HellstoneBrickWall": "狱石砖墙", "HellwingBow": "地狱之翼弓", "HerbBag": "草药袋", "HiTekSunglasses": "高科技墨镜", "HiveBackpack": "蜂巢背包", "HoneyBathtub": "蜂蜜浴缸", "HoneyBookcase": "蜂蜜书架", "HoneyCandelabra": "蜂蜜烛台", "HoneyCandle": "蜂蜜蜡烛", "HoneyChandelier": "蜂蜜吊灯", "HoneyChest": "蜂蜜箱", "HoneyClock": "蜂蜜时钟", "HoneyCup": "蜂蜜杯", "HoneyedGoggles": "涂蜜护目镜", "HoneyfallBlock": "蜂蜜瀑布块", "HoneyfallWall": "蜂蜜瀑布墙", "HoneyLamp": "蜂蜜灯", "HoneyLantern": "蜂蜜灯笼", "HoneyPiano": "蜂蜜钢琴", "HoneyPlatform": "蜂蜜平台", "HoneySink": "蜂蜜水槽", "HoneyWorkBench": "蜂蜜工作台", "HopliteStatue": "装甲步兵雕像", "HuntressBuckler": "女猎人圆盾", "HuntressJerkin": "女猎人上衣", "HuntressPants": "女猎人裤", "HuntressWig": "女猎人假发", "IceMirror": "冰雪镜", "IceTortoiseBanner": "冰雪陆龟旗", "IchorCampfire": "灵液篝火", "IchorDart": "灵液镖", "IlluminantHook": "荧光钩", "InfernalWispDye": "地狱妖灵染料", "InfluxWaver": "波涌之刃", "ItemFrame": "物品框", "Javelin": "标枪", "JimsWings": "<PERSON>的翅膀", "JourneymanBait": "熟手诱饵", "JungleFishingCrate": "丛林匣", "JungleYoyo": "亚马逊球", "KingSlimeBossBag": "宝藏袋（史莱姆王）", "Kraken": "克拉肯球", "LamiaHat": "拉弥亚面具", "LamiaPants": "拉弥亚蛇尾裤", "LamiaShirt": "拉弥亚披肩", "LargeAmber": "大琥珀", "LaserDrill": "激光钻头", "LaserMachinegun": "激光机枪", "LaserRuler": "机械标尺", "LastPrism": "终极棱镜", "LavafallBlock": "熔岩瀑布块", "LavaLamp": "熔岩灯", "LifeformAnalyzer": "生命体分析机", "LifePreserver": "救生圈", "LightKey": "光明钥匙", "LightMummyBanner": "光明木乃伊旗", "LihzahrdBathtub": "丛林蜥蜴浴缸", "LihzahrdBed": "丛林蜥蜴床", "LihzahrdBookcase": "丛林蜥蜴书架", "LihzahrdCandelabra": "丛林蜥蜴烛台", "LihzahrdCandle": "丛林蜥蜴蜡烛", "LihzahrdChandelier": "丛林蜥蜴吊灯", "LihzahrdClock": "丛林蜥蜴时钟", "LihzahrdLamp": "丛林蜥蜴灯", "LihzahrdLantern": "丛林蜥蜴灯笼", "LihzahrdSink": "丛林蜥蜴水槽", "LimeString": "橙绿绳", "LivingCursedFireBlock": "诅咒活火块", "LivingDemonFireBlock": "恶魔活火块", "LivingFireBlock": "活火块", "LivingFlameDye": "鲜艳红焰染料", "LivingFrostFireBlock": "寒霜活火块", "LivingIchorBlock": "灵液活火块", "LivingLeafWall": "生命树叶墙", "LivingMahoganyLeafWand": "红木树叶魔棒", "LivingMahoganyWand": "生命红木魔棒", "LivingOceanDye": "鲜艳海蓝染料", "LivingRainbowDye": "鲜艳彩虹染料", "LivingUltrabrightFireBlock": "超亮活火块", "LivingWoodBathtub": "生命木浴缸", "LivingWoodBed": "生命木床", "LivingWoodBookcase": "生命木书架", "LivingWoodCandelabra": "生命木烛台", "LivingWoodCandle": "生命木蜡烛", "LivingWoodChandelier": "生命木吊灯", "LivingWoodClock": "生命木时钟", "LivingWoodLamp": "生命木灯", "LivingWoodLantern": "生命木灯笼", "LivingWoodPiano": "生命木钢琴", "LivingWoodPlatform": "生命木平台", "LivingWoodSink": "生命木水槽", "LivingWoodSofa": "生命木沙发", "LivingWoodWorkBench": "生命木工作台", "LockBox": "金锁盒", "LogicGateLamp_Faulty": "逻辑门灯（故障）", "LogicGateLamp_Off": "逻辑门灯（关）", "LogicGateLamp_On": "逻辑门灯（开）", "LogicGate_AND": "逻辑门（与）", "LogicGate_NAND": "逻辑门（与非）", "LogicGate_NOR": "逻辑门（或非）", "LogicGate_NXOR": "逻辑门（同或）", "LogicGate_OR": "逻辑门（或）", "LogicGate_XOR": "逻辑门（异或）", "LogicSensor_Above": "逻辑感应器（玩家出入上方）", "LogicSensor_Honey": "液体感应器（蜂蜜）", "LogicSensor_Lava": "液体感应器（熔岩）", "LogicSensor_Liquid": "液体感应器（任何）", "LogicSensor_Moon": "逻辑感应器（夜）", "LogicSensor_Sun": "逻辑感应器（昼）", "LogicSensor_Water": "液体感应器（水）", "LokisDye": "<PERSON>的染料", "LokisHelm": "<PERSON>的头盔", "LokisPants": "<PERSON>的护胫", "LokisShirt": "<PERSON>的胸甲", "LokisWings": "<PERSON>的翅膀", "LunarBar": "夜明锭", "LunarBlockNebula": "星云碎片块", "LunarBlockSolar": "日耀碎片块", "LunarBlockStardust": "星尘碎片块", "LunarBlockVortex": "星旋碎片块", "LunarBrick": "夜明砖", "LunarBrickWall": "夜明砖墙", "LunarCraftingStation": "远古操纵机", "LunarFlareBook": "月耀", "LunarHamaxeNebula": "星云锤斧", "LunarHamaxeSolar": "耀斑锤斧", "LunarHamaxeStardust": "星尘锤斧", "LunarHamaxeVortex": "星旋锤斧", "LunarHook": "月钩", "LunarOre": "夜明矿", "LunarTabletFragment": "日耀碑牌碎片", "MagicHoneyDropper": "魔法蜂蜜滴管", "MagicLantern": "魔法灯笼", "MagicLavaDropper": "魔法熔岩滴管", "MagicSandDropper": "魔法沙粒滴管", "MagicWaterDropper": "魔法水滴管", "Marble": "大理石块", "MarbleBathtub": "大理石浴缸", "MarbleBed": "大理石床", "MarbleBlock": "光面大理石块", "MarbleBlockWall": "光面大理石墙", "MarbleBookcase": "大理石书架", "MarbleCandelabra": "大理石烛台", "MarbleCandle": "大理石蜡烛", "MarbleChair": "大理石椅", "MarbleChandelier": "大理石吊灯", "MarbleChest": "大理石箱", "MarbleClock": "大理石时钟", "MarbleDoor": "大理石门", "MarbleDresser": "大理石梳妆台", "MarbleLamp": "大理石灯", "MarbleLantern": "大理石灯笼", "MarblePiano": "大理石钢琴", "MarblePlatform": "大理石平台", "MarbleSink": "大理石水槽", "MarbleSofa": "大理石沙发", "MarbleTable": "大理石桌", "MarbleWall": "大理石墙", "MarbleWorkBench": "大理石工作台", "MartianArmorDye": "火星染料", "MartianAstroClock": "火星占星钟", "MartianBathtub": "火星浴缸", "MartianBed": "火星床", "MartianChandelier": "火星吊灯", "MartianChest": "火星箱", "MartianConduitPlating": "火星管道护板", "MartianConduitWall": "火星管道墙", "MartianCostumeMask": "火星装面具", "MartianCostumePants": "火星装裤", "MartianCostumeShirt": "火星装衣", "MartianDoor": "火星门", "MartianDresser": "火星梳妆台", "MartianHairDye": "火星染发剂", "MartianHolobookcase": "火星整体书架", "MartianHoverCandle": "火星摇摆蜡烛", "MartianHoverChair": "火星摇摆椅", "MartianLamppost": "火星灯柱", "MartianLantern": "火星灯笼", "MartianPiano": "火星钢琴", "MartianPlatform": "火星平台", "MartianSaucerTrophy": "火星飞碟纪念章", "MartianSink": "火星水槽", "MartianSofa": "火星沙发", "MartianTable": "火星桌", "MartianTableLamp": "火星桌灯", "MartianUniformHelmet": "火星制服头盔", "MartianUniformPants": "火星制服裤", "MartianUniformTorso": "火星制服上衣", "MartianWalkerBanner": "火星走妖旗", "MartianWorkBench": "火星工作台", "MasterBait": "大师诱饵", "MechanicalBatteryPiece": "机械电池片", "MechanicalLens": "机械透镜", "MechanicalWagonPiece": "机械车体片", "MechanicalWheelPiece": "机械车轮片", "MedusaBanner": "蛇发女妖旗", "MedusaHead": "蛇发女妖头", "MedusaStatue": "蛇发女妖雕像", "Meowmere": "彩虹猫之刃", "MetalDetector": "金属探测器", "MetalSink": "金属水槽", "MeteoriteBathtub": "陨石浴缸", "MeteoriteBed": "陨石床", "MeteoriteBookcase": "陨石书架", "MeteoriteBrick": "陨石砖", "MeteoriteBrickWall": "陨石砖墙", "MeteoriteCandelabra": "陨石烛台", "MeteoriteCandle": "陨石蜡烛", "MeteoriteChair": "陨石椅", "MeteoriteChandelier": "陨石吊灯", "MeteoriteChest": "陨石箱", "MeteoriteClock": "陨石钟", "MeteoriteDoor": "陨石门", "MeteoriteDresser": "陨石梳妆台", "MeteoriteLamp": "陨石灯", "MeteoriteLantern": "陨石灯笼", "MeteoritePiano": "陨石钢琴", "MeteoritePlatform": "陨石平台", "MeteoriteSink": "陨石水槽", "MeteoriteSofa": "陨石沙发", "MeteoriteTable": "陨石桌", "MeteoriteWorkBench": "陨石工作台", "MeteorStaff": "流星法杖", "MidnightRainbowDye": "午夜彩虹染料", "MinecartMech": "机械矿车", "MinecartTrack": "矿车轨道", "MirageDye": "幻象染料", "MolotovCocktail": "莫洛托夫鸡尾酒", "MoneyTrough": "钱币槽", "MonkBelt": "武僧腰带", "MonkBrows": "武僧浓眉秃头帽", "MonkPants": "武僧裤", "MonkShirt": "武僧衣", "MoonglowPlanterBox": "月光草种植盆", "MoonlordArrow": "夜明箭", "MoonLordBossBag": "宝藏袋（月亮领主）", "MoonlordBullet": "夜明弹", "MoonLordPainting": "不是小孩，也不是乌贼", "MoonLordTrophy": "月亮领主纪念章", "MoonlordTurretStaff": "月亮传送门法杖", "MoonMask": "月亮面具", "MothronBanner": "蛾怪旗", "MothronWings": "蛾怪之翼", "MouseStatue": "老鼠雕像", "MulticolorWrench": "五彩扳手", "MushroomBathtub": "蘑菇浴缸", "MushroomBed": "蘑菇床", "MushroomBench": "蘑菇长椅", "MushroomBookcase": "蘑菇书架", "MushroomCandelabra": "蘑菇烛台", "MushroomCandle": "蘑菇蜡烛", "MushroomChandelier": "蘑菇吊灯", "MushroomChest": "蘑菇箱", "MushroomClock": "蘑菇时钟", "MushroomDresser": "蘑菇梳妆台", "MushroomDye": "发光蘑菇染料", "MushroomLamp": "蘑菇灯", "MushroomLantern": "蘑菇灯笼", "MushroomPiano": "蘑菇钢琴", "MushroomPlatform": "蘑菇平台", "MushroomSink": "蘑菇水槽", "MushroomTable": "蘑菇桌", "MusicBoxGoblins": "八音盒（哥布林入侵）", "MusicBoxHell": "八音盒（地狱）", "MusicBoxLunarBoss": "八音盒（月亮Boss）", "MusicBoxMartians": "八音盒（火星暴乱）", "MusicBoxPirates": "八音盒（海盗入侵）", "MusicBoxSandstorm": "八音盒（沙尘暴）", "MusicBoxTowers": "八音盒（天界柱）", "MusicBoxUndergroundCrimson": "八音盒（地下猩红之地）", "Nail": "钉子", "NailGun": "钉枪", "NailheadBanner": "钉头旗", "NebulaArcanum": "星云奥秘", "NebulaAxe": "星云斧", "NebulaBeastBanner": "进化兽旗", "NebulaBlaze": "星云烈焰", "NebulaBrainBanner": "星云浮怪旗", "NebulaBreastplate": "星云胸甲", "NebulaChainsaw": "星云链锯", "NebulaDrill": "星云钻头", "NebulaDye": "星云染料", "NebulaHammer": "星云锤", "NebulaHeadcrabBanner": "吮脑怪旗", "NebulaHelmet": "星云头盔", "NebulaLeggings": "星云护腿", "NebulaMonolith": "星云天塔柱", "NebulaPickaxe": "星云镐", "NebulaPickup1": "伤害强化焰", "NebulaPickup2": "生命强化焰", "NebulaPickup3": "魔力强化焰", "NebulaSoldierBanner": "预言怪旗", "NegativeDye": "阴暗染料", "NightKey": "夜光钥匙", "NightVisionHelmet": "夜视头盔", "ObsidianBathtub": "黑曜石浴缸", "ObsidianCandelabra": "黑曜石烛台", "ObsidianCandle": "黑曜石蜡烛", "ObsidianChandelier": "黑曜石吊灯", "ObsidianChest": "黑曜石箱", "ObsidianClock": "黑曜石时钟", "ObsidianHelm": "黑曜石逃犯帽", "ObsidianLamp": "黑曜石灯", "ObsidianLantern": "黑曜石灯笼", "ObsidianPants": "黑曜石裤", "ObsidianShirt": "黑曜石风衣", "ObsidianSink": "黑曜石水槽", "OnyxBlaster": "玛瑙爆破枪", "OrangeString": "橙绳", "PainterPaintballGun": "彩弹枪", "PaintingAcorns": "橡实", "PaintingCastleMarsberg": "火星贝格城堡", "PaintingColdSnap": "寒流", "PaintingCursedSaint": "被诅咒的圣诞骷髅王", "PaintingMartiaLisa": "火娜丽莎", "PaintingSnowfellas": "吉祥三雪宝", "PaintingTheSeason": "圣诞雪季", "PaintingTheTruthIsUpThere": "真理就在火星", "PalmWood": "棕榈木", "PalmWoodBathtub": "棕榈木浴缸", "PalmWoodBed": "棕榈木床", "PalmWoodBench": "棕榈木长椅", "PalmWoodBookcase": "棕榈木书架", "PalmWoodBow": "棕榈木弓", "PalmWoodBreastplate": "棕榈木胸甲", "PalmWoodCandelabra": "棕榈木烛台", "PalmWoodCandle": "棕榈木蜡烛", "PalmWoodChair": "棕榈木椅", "PalmWoodChandelier": "棕榈木吊灯", "PalmWoodChest": "棕榈木箱", "PalmWoodClock": "棕榈木时钟", "PalmWoodDoor": "棕榈木门", "PalmWoodDresser": "棕榈木梳妆台", "PalmWoodFence": "棕榈木栅栏", "PalmWoodGreaves": "棕榈木护胫", "PalmWoodHammer": "棕榈木锤", "PalmWoodHelmet": "棕榈木头盔", "PalmWoodLamp": "棕榈木灯", "PalmWoodLantern": "棕榈木灯笼", "PalmWoodPiano": "棕榈木钢琴", "PalmWoodPlatform": "棕榈木平台", "PalmWoodSink": "棕榈木水槽", "PalmWoodSofa": "棕榈木沙发", "PalmWoodSword": "棕榈木剑", "PalmWoodTable": "棕榈木桌", "PalmWoodWall": "棕榈木墙", "PalmWoodWorkBench": "棕榈木工作台", "PartyBalloonAnimal": "气球兔兔", "PartyBundleOfBalloonsAccessory": "派对气球束", "PartyBundleOfBalloonTile": "呆萌丝带派对气球束", "PartyGirlGrenade": "快乐手榴弹", "PartyHat": "派对帽", "PartyMonolith": "派对中心", "PartyPresent": "派对礼物", "PDA": "个人数字助手", "PeaceCandle": "和平蜡烛", "PearlwoodBathtub": "珍珠木浴缸", "PearlwoodBookcase": "珍珠木书架", "PearlwoodCandelabra": "珍珠木烛台", "PearlwoodCandle": "珍珠木蜡烛", "PearlwoodChandelier": "珍珠木吊灯", "PearlwoodClock": "珍珠木时钟", "PearlwoodLamp": "珍珠木灯", "PearlwoodLantern": "珍珠木灯笼", "PearlwoodSink": "珍珠木水槽", "PedguinHat": "Pedguin的兜帽", "PedguinPants": "Pedguin的裤子", "PedguinShirt": "Pedguin的夹克", "PenguinStatue": "企鹅雕像", "Phantasm": "幻影弓", "PhaseDye": "相位染料", "PhasicWarpEjector": "相位扭曲弹射器", "Pigronata": "猪龙彩罐", "PigronStatue": "猪龙雕像", "PinkDungeonBathtub": "粉地牢浴缸", "PinkDungeonCandelabra": "粉地牢烛台", "PinkDungeonChandelier": "粉地牢吊灯", "PinkDungeonChest": "粉地牢箱", "PinkDungeonLamp": "粉地牢灯", "PinkDungeonSink": "粉地牢水槽", "PinkGel": "粉凝胶", "PinkGelDye": "粉凝胶染料", "PinkJellyfishBanner": "粉水母旗", "PinkSlimeBlock": "粉史莱姆块", "PinkString": "粉绳", "PinkTorch": "粉火把", "PirateCaptainBanner": "海盗船长旗", "PirateCorsairBanner": "私船海盗旗", "PirateCrossbowerBanner": "海盗弩手旗", "PirateDeadeyeBanner": "海盗神射手旗", "PirateStaff": "海盗法杖", "PixelBox": "像素盒", "PixieDye": "妖精染料", "PlanteraBossBag": "宝藏袋（世纪之花）", "PlanteraMask": "世纪之花面具", "PocketMirror": "袖珍镜", "PoisonousSporeBanner": "毒孢旗", "PortalGun": "传送枪", "PortalGunStation": "传送枪站", "PressureTrack": "压力板轨道", "ProjectilePressurePad": "青绿压力垫板", "PsychoBanner": "变态人旗", "PsychoKnife": "变态人的刀", "PumpkinBathtub": "南瓜浴缸", "PumpkinBed": "南瓜床", "PumpkinBookcase": "南瓜书架", "PumpkinCandelabra": "南瓜烛台", "PumpkinCandle": "南瓜蜡烛", "PumpkinChandelier": "南瓜吊灯", "PumpkinChest": "南瓜箱", "PumpkinClock": "南瓜时钟", "PumpkinDresser": "南瓜梳妆台", "PumpkinLamp": "南瓜灯", "PumpkinLantern": "南瓜灯笼", "PumpkinPiano": "南瓜钢琴", "PumpkinSink": "南瓜水槽", "PurpleCounterweight": "紫平衡锤", "PurpleOozeDye": "紫泥染料", "PurplePhasesaber": "紫晶光刃", "PurpleString": "紫绳", "PutridScent": "腐香囊", "QueenBeeBossBag": "宝藏袋（蜂王）", "Radar": "雷达", "RainbowCampfire": "彩虹篝火", "RainbowCrystalStaff": "七彩水晶法杖", "RainbowString": "彩虹绳", "RainbowTorch": "彩虹火把", "Rally": "对打球", "RavagerScorpionBanner": "沙贼旗", "RazorbladeTyphoon": "利刃台风", "RedAcidDye": "红酸性染料", "RedCounterweight": "红平衡锤", "RedDevilBanner": "红魔鬼旗", "RedPhasesaber": "红晶光刃", "RedString": "红绳", "RedsYoyo": "Red的抛球", "ReflectiveCopperDye": "反光铜染料", "ReflectiveDye": "反光染料", "ReflectiveGoldDye": "反光金染料", "ReflectiveMetalDye": "反光金属染料", "ReflectiveObsidianDye": "反光黑曜石染料", "ReflectiveSilverDye": "反光银染料", "REK": "R.E.K.3000", "RichGravestone1": "金十字墓石碑", "RichGravestone2": "金墓石", "RichGravestone3": "金墓石碑", "RichGravestone4": "金墓碑", "RichGravestone5": "金碑石", "RichMahoganyBathtub": "红木浴缸", "RichMahoganyBookcase": "红木书架", "RichMahoganyCandelabra": "红木大烛台", "RichMahoganyCandle": "红木蜡烛", "RichMahoganyChandelier": "红木吊灯", "RichMahoganyClock": "红木时钟", "RichMahoganyLamp": "红木灯", "RichMahoganyLantern": "红木灯笼", "RichMahoganySink": "红木水槽", "RoyalGel": "皇家凝胶", "RubyGemsparkWall": "红玉晶莹宝石墙", "RubyGemsparkWallOff": "黯淡红玉晶莹宝石墙", "SailfishBoots": "旗鱼靴", "SalamanderBanner": "蝾螈旗", "SandElementalBanner": "沙尘精旗", "SandFallBlock": "落沙块", "SandFallWall": "落沙墙", "SandsharkBanner": "沙鲨旗", "SandsharkCorruptBanner": "噬骨沙鲨旗", "SandsharkCrimsonBanner": "戮血沙鲨旗", "SandsharkHallowedBanner": "水晶沙鲨旗", "SandSlimeBanner": "沙史莱姆旗", "Sandstone": "沙岩块", "SandstoneWall": "沙岩墙", "SapphireGemsparkWall": "蓝玉晶莹宝石墙", "SapphireGemsparkWallOff": "黯淡蓝玉晶莹宝石墙", "ScalyTruffle": "带鳞松露", "ScorpionStatue": "蝎子雕像", "Seashell": "贝壳", "SeaSnailBanner": "海蜗牛旗", "Seedler": "种子弯刀", "SeveredHandBanner": "残手旗", "Sextant": "六分仪", "ShadewoodBathtub": "暗影木浴缸", "ShadewoodBookcase": "暗影木书架", "ShadewoodCandelabra": "暗影木烛台", "ShadewoodCandle": "暗影木蜡烛", "ShadewoodChandelier": "暗影木吊灯", "ShadewoodClock": "暗影木时钟", "ShadewoodLamp": "暗影木灯", "ShadewoodLantern": "暗影木灯笼", "ShadewoodSink": "暗影木水槽", "ShadowDye": "暗影染料", "ShadowFlameBow": "暗影焰弓", "ShadowflameHadesDye": "暗影焰冥王染料", "ShadowFlameHexDoll": "暗影焰妖娃", "ShadowFlameKnife": "暗影焰刀", "SharkronBalloon": "鲨鱼龙气球", "SharkStatue": "鲨鱼雕像", "SharkteethTrophy": "鲨牙纪念章", "SharkToothNecklace": "鲨牙项链", "SharpeningStation": "利器站", "ShiftingPearlSandsDye": "珍珠流沙染料", "ShiftingSandsDye": "流沙染料", "ShinyStone": "闪亮石", "ShipsWheel": "舵轮", "ShiverthornPlanterBox": "寒颤棘种植盆", "ShrimpyTruffle": "虾松露", "ShroomitePlating": "蘑菇矿护板", "ShroomitePlatingWall": "蘑菇矿护板墙", "SilkRope": "丝绸绳", "SilkRopeCoil": "丝绸绳圈", "SillyBalloonGreen": "呆萌绿气球", "SillyBalloonGreenWall": "呆萌绿气球墙", "SillyBalloonMachine": "呆萌气球机", "SillyBalloonPink": "呆萌粉气球", "SillyBalloonPinkWall": "呆萌粉气球墙", "SillyBalloonPurple": "呆萌紫气球", "SillyBalloonPurpleWall": "呆萌紫气球墙", "SillyBalloonTiedGreen": "呆萌丝带气球（绿）", "SillyBalloonTiedPink": "呆萌丝带气球（粉）", "SillyBalloonTiedPurple": "呆萌丝带气球（紫）", "SillyStreamerBlue": "蓝饰带", "SillyStreamerGreen": "绿饰带", "SillyStreamerPink": "粉饰带", "SilverAndBlackDye": "银黑染料", "SkeletronBossBag": "宝藏袋（骷髅王）", "SkeletronPrimeBossBag": "宝藏袋（机械骷髅王）", "SkeletronPrimeMask": "机械骷髅王面具", "SkiphsHelm": "Skiphs的面具", "SkiphsPants": "Skiphs的熊裤", "SkiphsShirt": "Skiphs的皮肤", "SkiphsWings": "Ski<PERSON>的爪子", "SkyBlueString": "天蓝绳", "SkyFracture": "裂天剑", "SkywareBathtub": "天域浴缸", "SkywareBed": "天域床", "SkywareBookcase": "天域书架", "SkywareCandelabra": "天域烛台", "SkywareCandle": "天域蜡烛", "SkywareChandelier": "天域吊灯", "SkywareClock": "天域时钟", "SkywareLamp": "天域灯", "SkywareLantern": "天域灯笼", "SkywarePlatform": "天域平台", "SkywareSink": "天域水槽", "SkywareWorkbench": "天域工作台", "SlapHand": "拍拍手", "SliceOfCake": "蛋糕块", "SlimeBathtub": "史莱姆浴缸", "SlimeBed": "史莱姆床", "SlimeBookcase": "史莱姆书架", "SlimeCandelabra": "史莱姆烛台", "SlimeCandle": "史莱姆蜡烛", "SlimeChair": "史莱姆椅", "SlimeChandelier": "史莱姆吊灯", "SlimeChest": "史莱姆箱", "SlimeClock": "史莱姆时钟", "SlimeDoor": "史莱姆门", "SlimeDresser": "史莱姆梳妆台", "SlimeGun": "史莱姆枪", "SlimeHook": "史莱姆钩", "SlimeLamp": "史莱姆灯", "SlimeLantern": "史莱姆灯笼", "SlimePiano": "史莱姆钢琴", "SlimePlatform": "史莱姆平台", "SlimeSink": "史莱姆水槽", "SlimeSofa": "史莱姆沙发", "SlimeTable": "史莱姆桌", "SlimySaddle": "粘鞍", "Sluggy": "鼻涕虫", "SmokeBlock": "烟雾块", "SnailStatue": "蜗牛雕像", "SnowCloudBlock": "雪云", "SnowFallBlock": "降雪块", "SnowFallWall": "降雪墙", "SolarCoriteBanner": "流星火怪旗", "SolarCrawltipedeBanner": "千足蜈蚣旗", "SolarDrakomireBanner": "火龙怪旗", "SolarDrakomireRiderBanner": "火龙怪骑士旗", "SolarDye": "日耀染料", "SolarEruption": "日耀喷发剑", "SolarFlareAxe": "耀斑斧", "SolarFlareBreastplate": "耀斑胸甲", "SolarFlareChainsaw": "耀斑链锯", "SolarFlareDrill": "耀斑钻头", "SolarFlareHammer": "耀斑锤", "SolarFlareHelmet": "耀斑头盔", "SolarFlareLeggings": "耀斑护腿", "SolarFlarePickaxe": "耀斑镐", "SolarMonolith": "日耀天塔柱", "SolarSolenianBanner": "火月怪旗", "SolarSrollerBanner": "火滚怪旗", "SolarTablet": "日耀碑牌", "SoulDrain": "夺命杖", "SparkyPainting": "斯派基", "SpectreBar": "幽灵锭", "SpelunkerGlowstick": "洞穴探险荧光棒", "SpiderFang": "蜘蛛牙", "SpiderStaff": "蜘蛛法杖", "SpiritFlame": "神灯烈焰", "SpookyBathtub": "阴森浴缸", "SpookyBed": "阴森床", "SpookyBookcase": "阴森书架", "SpookyCandelabra": "阴森大烛台", "SpookyCandle": "阴森蜡烛", "SpookyChandelier": "阴森吊灯", "SpookyChest": "阴森箱", "SpookyClock": "阴森时钟", "SpookyLamp": "阴森灯", "SpookyLantern": "阴森灯笼", "SpookySink": "阴森水槽", "SporeSac": "孢子囊", "SquireGreatHelm": "侍卫大头盔", "SquireGreaves": "侍卫护胫", "SquirePlating": "侍卫板甲", "SquireShield": "侍卫护盾", "SquirrelGold": "金松鼠", "SquirrelGoldCage": "金松鼠笼", "SquirrelOrangeCage": "红松鼠笼", "SquirrelRed": "红松鼠", "SquirrelStatue": "松鼠雕像", "StardustAxe": "星尘斧", "StardustBreastplate": "星尘板甲", "StardustCellStaff": "星尘细胞法杖", "StardustChainsaw": "星尘链锯", "StardustDragonStaff": "星尘之龙法杖", "StardustDrill": "星尘钻头", "StardustDye": "星尘染料", "StardustHammer": "星尘锤", "StardustHelmet": "星尘头盔", "StardustJellyfishBanner": "流体入侵怪旗", "StardustLargeCellBanner": "星细胞旗", "StardustLeggings": "星尘护腿", "StardustMonolith": "星尘天塔柱", "StardustPickaxe": "星尘镐", "StardustSmallCellBanner": "迷你星细胞旗", "StardustSoldierBanner": "观星怪旗", "StardustSpiderBanner": "闪耀炮手旗", "StardustWormBanner": "银河织妖旗", "Starfish": "海星", "StarWrath": "狂星之怒", "StaticHook": "静止钩", "SteampunkBathtub": "蒸汽朋克浴缸", "SteampunkBookcase": "蒸汽朋克书架", "SteampunkCandelabra": "蒸汽朋克烛台", "SteampunkCandle": "蒸汽朋克蜡烛", "SteampunkChandelier": "蒸汽朋克吊灯", "SteampunkChest": "蒸汽朋克箱", "SteampunkClock": "蒸汽朋克时钟", "SteampunkCup": "圣餐杯", "SteampunkDresser": "蒸汽朋克梳妆台", "SteampunkLamp": "蒸汽朋克灯", "SteampunkLantern": "蒸汽朋克灯笼", "SteampunkPiano": "蒸汽朋克钢琴", "SteampunkPlatform": "蒸汽朋克平台", "SteampunkSink": "蒸汽朋克水槽", "SteampunkWorkBench": "蒸汽朋克工作台", "StickyDynamite": "粘性雷管", "StickyGrenade": "粘性手榴弹", "Stopwatch": "秒表", "StrangeBrew": "诡药", "StrangePlant1": "奇异植物", "StrangePlant2": "奇异植物", "StrangePlant3": "奇异植物", "StrangePlant4": "奇异植物", "StylistKilLaKillScissorsIWish": "时尚剪刀", "SummonerEmblem": "召唤师徽章", "Sundial": "附魔日晷", "SunMask": "太阳面具", "SuperAbsorbantSponge": "超级吸收绵", "SuperHealingPotion": "超级治疗药水", "SuspiciousLookingTentacle": "可疑触手", "SwordfishTrophy": "剑鱼纪念章", "TallGate": "高门", "TallyCounter": "杀怪计数器", "TargetDummy": "训练假人", "TartarSauce": "塔塔酱", "TaxCollectorHat": "税收官帽", "TaxCollectorPants": "税收官裤", "TaxCollectorsStickOfDoom": "精致手杖", "TaxCollectorSuit": "税收官衣服", "TealString": "青绿绳", "TeamBlockBlue": "蓝团队块", "TeamBlockBluePlatform": "蓝团队平台", "TeamBlockGreen": "绿团队块", "TeamBlockGreenPlatform": "绿团队平台", "TeamBlockPink": "粉团队块", "TeamBlockPinkPlatform": "粉团队平台", "TeamBlockRed": "红团队块", "TeamBlockRedPlatform": "红团队平台", "TeamBlockWhite": "白团队块", "TeamBlockWhitePlatform": "白团队平台", "TeamBlockYellow": "黄团队块", "TeamBlockYellowPlatform": "黄团队平台", "TempestStaff": "暴风雨法杖", "TendonHook": "肌腱钩", "Terrarian": "泰拉悠悠球", "TheBrideDress": "婚裙", "TheBrideHat": "面纱", "TheEyeOfCthulhu": "克苏鲁之眼", "ThePossessedBanner": "攀爬魔旗", "ThornHook": "刺钩", "TinPlating": "锡护板", "TinPlatingWall": "锡护板墙", "TombCrawlerBanner": "墓穴爬虫旗", "TopazGemsparkWall": "黄玉晶莹宝石墙", "TopazGemsparkWallOff": "黯淡黄玉晶莹宝石墙", "ToxicFlask": "毒气瓶", "Toxikarp": "毒弹枪", "Trapdoor": "机关门", "TruffleWorm": "松露虫", "Tsunami": "海啸", "TsunamiInABottle": "海啸瓶", "TumbleweedBanner": "愤怒翻滚怪旗", "TwilightDye": "暮光染料", "TwilightHairDye": "暮光染发剂", "TwinMask": "双子魔眼面具", "TwinsBossBag": "宝藏袋（双子魔眼）", "UltraBrightCampfire": "超亮篝火", "UndeadVikingStatue": "亡灵维京海盗雕像", "UnicornStatue": "独角兽雕像", "UnicornWispDye": "独角妖灵染料", "ValkyrieYoyo": "女武神悠悠球", "Valor": "英勇球", "ViciousMushroom": "毒蘑菇", "ViciousPowder": "毒粉", "VineRope": "藤蔓绳", "VineRopeCoil": "藤蔓绳圈", "VioletString": "蓝紫绳", "VoidDye": "虚空染料", "VortexAxe": "星旋斧", "VortexBeater": "星旋机枪", "VortexBreastplate": "星旋胸甲", "VortexChainsaw": "星旋链锯", "VortexDrill": "星旋钻头", "VortexDye": "星旋染料", "VortexHammer": "星旋锤", "VortexHelmet": "星旋头盔", "VortexHornetBanner": "异星黄蜂旗", "VortexHornetQueenBanner": "异星蜂王旗", "VortexLarvaBanner": "异星幼虫旗", "VortexLeggings": "星旋护腿", "VortexMonolith": "星旋天塔柱", "VortexPickaxe": "星旋镐", "VortexRiflemanBanner": "漩泥怪旗", "VortexSoldierBanner": "星旋怪旗", "WalkingAntlionBanner": "蚁狮马旗", "WallAnchor": "墙锚", "WallCreeperStatue": "爬墙蜘蛛雕像", "WallOfFleshBossBag": "宝藏袋（血肉墙）", "WandofSparking": "火花魔棒", "WarTable": "战争桌", "WarTableBanner": "战争桌旗", "WaterfallBlock": "瀑布块", "WaterleafPlanterBox": "水叶草种植盆", "WeaponRack": "武器架", "WeatherRadio": "天气收音机", "WebRope": "蛛丝绳", "WebRopeCoil": "蛛丝绳圈", "WeightedPressurePlateCyan": "青色测重压力板", "WeightedPressurePlateOrange": "橙色测重压力板", "WeightedPressurePlatePink": "粉色测重压力板", "WeightedPressurePlatePurple": "紫色测重压力板", "WhiteLunaticHood": "日耀邪教徒兜帽", "WhiteLunaticRobe": "日耀邪教徒长袍", "WhitePhasesaber": "白晶光刃", "WhiteString": "白绳", "WineGlass": "葡萄酒杯", "WingsNebula": "星云斗篷", "WingsSolar": "日耀之翼", "WingsStardust": "星尘之翼", "WingsVortex": "星旋强化翼", "WireBulb": "彩线灯泡", "WireKite": "宏伟蓝图", "WirePipe": "分线盒", "WispDye": "妖灵染料", "WoodenSink": "木水槽", "WoodYoyo": "木悠悠球", "WormholePotion": "虫洞药水", "WormHook": "蠕虫钩", "WormScarf": "蠕虫围巾", "WormStatue": "蠕虫雕像", "WraithStatue": "幻灵雕像", "Xenopopper": "外星霰弹枪", "XenoStaff": "外星法杖", "Yelets": "叶列茨球", "YellowCounterweight": "黄平衡锤", "YellowPhasesaber": "黄晶光刃", "YellowString": "黄绳", "YellowWrench": "黄扳手", "Yoraiz0rDarkness": "Yoraiz0r的怒容", "Yoraiz0rHead": "Yoraiz0r的染色护目镜", "Yoraiz0rPants": "Yoraiz0r的裙", "Yoraiz0rShirt": "Yoraiz0r的制服", "Yoraiz0rWings": "Yoraiz0r的魔法", "YoyoBag": "悠悠球袋", "YoYoGlove": "悠悠球手套", "ZombieArmStatue": "武装僵尸雕像", "DD2FlameburstTowerT1Popper": "爆炸烈焰魔杖", "DD2FlameburstTowerT2Popper": "爆炸烈焰手杖", "DD2FlameburstTowerT3Popper": "爆炸烈焰法杖", "AleThrowingGlove": "麦芽酒投掷器", "DD2EnergyCrystal": "埃特尼亚魔力", "DD2SquireDemonSword": "地狱之剑", "DD2BallistraTowerT1Popper": "弩车魔杖", "DD2BallistraTowerT2Popper": "弩车手杖", "DD2BallistraTowerT3Popper": "弩车法杖", "DD2SquireBetsySword": "飞龙", "DD2ElderCrystal": "永恒水晶", "DD2LightningAuraT1Popper": "闪电光环魔杖", "DD2LightningAuraT2Popper": "闪电光环手杖", "DD2LightningAuraT3Popper": "闪电光环法杖", "DD2ExplosiveTrapT1Popper": "爆炸机关魔杖", "DD2ExplosiveTrapT2Popper": "爆炸机关手杖", "DD2ExplosiveTrapT3Popper": "爆炸机关法杖", "MonkStaffT1": "瞌睡章鱼", "MonkStaffT2": "恐怖关刀", "DD2GoblinBomberBanner": "埃特尼亚哥布林投弹手旗", "DD2GoblinBanner": "埃特尼亚哥布林旗", "DD2SkeletonBanner": "撒旦骷髅旗", "DD2DrakinBanner": "德拉克龙旗", "DD2KoboldFlyerBanner": "小妖魔滑翔怪旗", "DD2KoboldBanner": "小妖魔旗", "DD2WitherBeastBanner": "枯萎兽旗", "DD2WyvernBanner": "埃特尼亚飞龙旗", "DD2JavelinThrowerBanner": "埃特尼亚标枪投掷怪旗", "DD2LightningBugBanner": "埃特尼亚荧光虫旗", "DD2PetGato": "Gato蛋", "DD2PetGhost": "飞眼怪蛋", "DD2PetDragon": "龙蛋", "BookStaff": "无限智慧巨著", "DD2PhoenixBow": "幽灵凤凰", "MonkStaffT3": "天龙之怒", "DD2BetsyBow": "空中祸害", "BossMaskBetsy": "双足翼龙面具", "BossMaskOgre": "食人魔面具", "BossMaskDarkMage": "暗黑魔法师面具", "BossTrophyBetsy": "双足翼龙纪念章", "BossTrophyDarkmage": "暗黑魔法师纪念章", "BossTrophyOgre": "食人魔纪念章", "ApprenticeStaffT3": "双足翼龙怒气", "SquireAltHead": "英灵殿骑士头盔", "SquireAltShirt": "英灵殿骑士胸甲", "SquireAltPants": "英灵殿骑士护胫", "ApprenticeAltHead": "暗黑艺术家帽子", "ApprenticeAltShirt": "暗黑艺术家长袍", "ApprenticeAltPants": "暗黑艺术家护腿", "HuntressAltHead": "红色骑术兜帽", "HuntressAltShirt": "红色骑术服", "HuntressAltPants": "红色骑术护腿", "MonkAltHead": "渗透忍者头盔", "MonkAltShirt": "渗透忍者上衣", "MonkAltPants": "渗透忍者裤装", "DD2ElderCrystalStand": "永恒水晶座", "BetsyWings": "双足翼龙之翼", "CrystalChest": "水晶箱", "GoldenChest": "金宝箱", "Fake_CrystalChest": "机关水晶箱", "Fake_GoldenChest": "机关金宝箱", "CrystalDoor": "水晶门", "CrystalChair": "水晶椅", "CrystalCandle": "水晶蜡烛", "CrystalLantern": "水晶灯笼", "CrystalLamp": "水晶灯", "CrystalCandelabra": "水晶烛台", "CrystalChandelier": "水晶吊灯", "CrystalBathtub": "水晶浴缸", "CrystalSink": "水晶水槽", "CrystalBed": "水晶床", "CrystalClock": "水晶时钟", "SkywareClock2": "日盘时钟", "DungeonClockBlue": "蓝地牢时钟", "DungeonClockGreen": "绿地牢时钟", "DungeonClockPink": "粉地牢时钟", "CrystalPlatform": "水晶平台", "GoldenPlatform": "金平台", "DynastyPlatform": "王朝木平台", "LihzahrdPlatform": "丛林蜥蜴平台", "FleshPlatform": "血肉平台", "FrozenPlatform": "冰冻平台", "CrystalWorkbench": "水晶工作台", "GoldenWorkbench": "金工作台", "CrystalDresser": "水晶梳妆台", "DynastyDresser": "王朝梳妆台", "FrozenDresser": "冰冻梳妆台", "LivingWoodDresser": "生命木梳妆台", "CrystalPiano": "水晶钢琴", "DynastyPiano": "王朝钢琴", "CrystalBookCase": "水晶书架", "CrystalSofaHowDoesThatEvenWork": "水晶沙发", "DynastySofa": "王朝沙发", "CrystalTable": "水晶桌", "ArkhalisHat": "<PERSON><PERSON><PERSON>的兜帽", "ArkhalisShirt": "<PERSON><PERSON><PERSON>的紧身衣", "ArkhalisPants": "<PERSON><PERSON><PERSON>的紧身服", "ArkhalisWings": "<PERSON><PERSON><PERSON>的飞翼", "LeinforsHat": "Leinfors的护发器", "LeinforsShirt": "Leinfors的奇异风", "LeinforsPants": "Leinfors的潮裤", "LeinforsWings": "Leinfors的卷缠斗篷", "LeinforsAccessory": "Leinfors的奢华洗发液", "MusicBoxDD2": "音乐盒（撒旦军队）", "BossBagBetsy": "宝藏袋（双足翼龙）", "Celeb2": "喜庆弹射器Mk2", "SpiderBathtub": "蜘蛛浴缸", "SpiderBed": "蜘蛛床", "SpiderBookcase": "蜘蛛书架", "SpiderDresser": "蜘蛛梳妆台", "SpiderCandelabra": "蜘蛛烛台", "SpiderCandle": "蜘蛛蜡烛", "SpiderChair": "蜘蛛椅", "SpiderChandelier": "蜘蛛吊灯", "SpiderChest": "蜘蛛箱", "SpiderClock": "蜘蛛时钟", "SpiderDoor": "蜘蛛门", "SpiderLamp": "蜘蛛灯", "SpiderLantern": "蜘蛛灯笼", "SpiderPiano": "蜘蛛钢琴", "SpiderPlatform": "蜘蛛平台", "SpiderSinkSpiderSinkDoesWhateverASpiderSinkDoes": "蜘蛛水槽", "SpiderSofa": "蜘蛛沙发", "SpiderTable": "蜘蛛桌", "SpiderWorkbench": "蜘蛛工作台", "Fake_SpiderChest": "机关蜘蛛箱", "IronBrick": "铁砖", "IronBrickWall": "铁砖墙", "LeadBrick": "铅砖", "LeadBrickWall": "铅砖墙", "LesionBlock": "病变块", "LesionBlockWall": "病变块墙", "LesionPlatform": "病变平台", "LesionBathtub": "病变浴缸", "LesionBed": "病变床", "LesionBookcase": "病变书架", "LesionCandelabra": "病变烛台", "LesionCandle": "病变蜡烛", "LesionChair": "病变椅", "LesionChandelier": "病变吊灯", "LesionChest": "病变宝箱", "LesionClock": "病变时钟", "LesionDoor": "病变门", "LesionDresser": "病变梳妆台", "LesionLamp": "病变灯", "LesionLantern": "病变灯笼", "LesionPiano": "病变钢琴", "LesionSink": "病变水槽", "LesionSofa": "病变沙发", "LesionTable": "病变桌", "LesionWorkbench": "病变工作台", "Fake_LesionChest": "机关病变箱", "HatRack": "帽架", "WoodenCrateHard": "珍珠木匣", "IronCrateHard": "秘银匣", "GoldenCrateHard": "钛金匣", "CorruptFishingCrateHard": "污损匣", "CrimsonFishingCrateHard": "血匣", "DungeonFishingCrateHard": "围栏匣", "FloatingIslandFishingCrateHard": "天蓝匣", "HallowedFishingCrateHard": "天赐匣", "JungleFishingCrateHard": "荆棘匣", "DeadMansChest": "死人宝箱", "AmphibianBoots": "水陆两用靴", "ArcaneFlower": "奥术花", "BerserkerGlove": "狂战士手套", "FairyBoots": "仙灵靴", "FrogFlipper": "青蛙脚蹼", "FrogGear": "青蛙装备", "FrogWebbing": "青蛙蹼", "FrozenShield": "冰冻护盾", "HeroShield": "英雄护盾", "LavaSkull": "岩浆骷髅头", "MagnetFlower": "磁花", "ManaCloak": "魔力斗篷", "MoltenQuiver": "熔火箭袋", "MoltenSkullRose": "熔火骷髅头玫瑰", "ObsidianSkullRose": "黑曜石骷髅头玫瑰", "ReconScope": "侦察镜", "StalkersQuiver": "潜行者箭袋", "StingerNecklace": "毒刺项链", "UltrabrightHelmet": "超亮头盔", "Apple": "苹果", "ApplePieSlice": "苹果派切片", "ApplePie": "苹果派", "BananaSplit": "香蕉船", "BBQRibs": "烧烤肋排", "BunnyStew": "炖兔兔", "Burger": "汉堡", "ChickenNugget": "鸡块", "ChocolateChipCookie": "巧克力曲奇饼干", "CreamSoda": "奶油苏打水", "Escargot": "食用蜗牛", "FriedEgg": "煎蛋", "Fries": "炸薯条", "GoldenDelight": "金美味", "Grapes": "葡萄", "GrilledSquirrel": "烤松鼠", "Hotdog": "热狗", "IceCream": "冰淇淋", "Milkshake": "奶昔", "Nachos": "玉米片", "Pizza": "披萨", "PotatoChips": "薯片", "RoastedBird": "烤鸟", "RoastedDuck": "烤鸭", "SauteedFrogLegs": "炒蛙腿", "SeafoodDinner": "海鲜大餐", "ShrimpPoBoy": "鲜虾三明治", "Spaghetti": "意大利面", "Steak": "牛排", "MoltenCharm": "熔火护身符", "GolfCup": "高尔夫球洞", "FlowerPacketBlue": "蓝花种子", "FlowerPacketMagenta": "洋红花种子", "FlowerPacketPink": "粉花种子", "FlowerPacketRed": "红花种子", "FlowerPacketYellow": "黄花种子", "FlowerPacketViolet": "蓝紫花种子", "FlowerPacketWhite": "白花种子", "FlowerPacketTallGrass": "高茎草种子", "SandBoots": "沙丘行者靴", "AncientChisel": "远古凿子", "CarbonGuitar": "雨歌", "SkeletonBow": "骷髅头弓", "FossilPickaxe": "化石镐", "SuperStarCannon": "超级星星炮", "ThunderSpear": "风暴长矛", "ThunderStaff": "霹雳法杖", "DrumSet": "鼓组", "PicnicTable": "野餐桌", "PicnicTableWithCloth": "精致野餐桌", "DesertMinecart": "沙漠矿车", "FishMinecart": "鲤鱼矿车", "GolfClubIron": "高尔夫球杆（铁杆）", "GolfClubPutter": "高尔夫球杆（推杆）", "GolfClubWedge": "高尔夫球杆（挖起杆）", "GolfClubDriver": "高尔夫球杆（木杆）", "GolfWhistle": "高尔夫口哨", "GolfTee": "高尔夫球座", "FairyCritterPink": "粉仙灵", "FairyCritterGreen": "绿仙灵", "FairyCritterBlue": "蓝仙灵", "JunoniaShell": "涡螺壳", "LightningWhelkShell": "香螺壳", "TulipShell": "旋螺壳", "PinWheel": "风车", "WeatherVane": "风向标", "VoidVault": "虚空保险库", "MusicBoxOceanAlt": "八音盒（海洋夜晚）", "MusicBoxSlimeRain": "八音盒（史莱姆雨）", "MusicBoxSpaceAlt": "八音盒（太空白天）", "MusicBoxTownDay": "八音盒（城镇白天）", "MusicBoxTownNight": "八音盒（城镇夜晚）", "MusicBoxWindyDay": "八音盒（大风天）", "GolfCupFlagWhite": "白三角旗", "GolfCupFlagRed": "红三角旗", "GolfCupFlagGreen": "绿三角旗", "GolfCupFlagBlue": "蓝三角旗", "GolfCupFlagYellow": "黄三角旗", "GolfCupFlagPurple": "紫三角旗", "ShellPileBlock": "贝壳堆", "AntiPortalBlock": "反传送门块", "LawnMower": "割草机", "GolfBall": "高尔夫球", "ToiletEbonyWood": "乌木马桶", "ToiletRichMahogany": "红木马桶", "ToiletPearlwood": "珍珠木马桶", "ToiletLivingWood": "生命木马桶", "ToiletCactus": "仙人掌马桶", "ToiletBone": "骨头马桶", "ToiletFlesh": "血肉马桶", "ToiletMushroom": "蘑菇马桶", "ToiletSunplate": "天域马桶", "ToiletShadewood": "暗影木马桶", "ToiletLihzhard": "丛林蜥蜴马桶", "ToiletDungeonBlue": "蓝地牢马桶", "ToiletDungeonGreen": "绿地牢马桶", "ToiletDungeonPink": "粉地牢马桶", "ToiletObsidian": "黑曜石马桶", "ToiletFrozen": "冰冻马桶", "ToiletGlass": "玻璃马桶", "ToiletHoney": "蜂蜜马桶", "ToiletSteampunk": "蒸汽朋克马桶", "ToiletPumpkin": "南瓜马桶", "ToiletSpooky": "阴森马桶", "ToiletDynasty": "王朝马桶", "ToiletPalm": "棕榈木马桶", "ToiletBoreal": "针叶木马桶", "ToiletSlime": "史莱姆马桶", "ToiletMartian": "火星马桶", "ToiletGranite": "花岗岩马桶", "ToiletMarble": "大理石马桶", "ToiletCrystal": "水晶马桶", "ToiletSpider": "蜘蛛马桶", "ToiletLesion": "病变马桶", "ToiletDiamond": "钻石马桶", "MaidHead": "女仆帽", "MaidShirt": "女仆裙", "MaidPants": "女仆鞋", "VoidLens": "虚空袋", "MaidHead2": "粉女仆帽", "MaidShirt2": "粉女仆裙", "MaidPants2": "粉女仆鞋", "GolfHat": "乡村俱乐部帽", "GolfShirt": "乡村俱乐部背心", "GolfPants": "乡村俱乐部长裤", "GolfVisor": "乡村俱乐部帽舌", "SpiderBlock": "蜘蛛窝块", "SpiderWall": "蜘蛛窝墙", "ToiletMeteor": "流星马桶", "LesionStation": "腐变室", "ManaCloakStar": "", "SolarBathtub": "日耀浴缸", "SolarBed": "日耀床", "SolarBookcase": "日耀书架", "SolarDresser": "日耀梳妆台", "SolarCandelabra": "日耀烛台", "SolarCandle": "日耀蜡烛", "SolarChair": "日耀椅", "SolarChandelier": "日耀吊灯", "SolarChest": "日耀箱", "SolarClock": "日耀时钟", "SolarDoor": "日耀门", "SolarLamp": "日耀灯", "SolarLantern": "日耀灯笼", "SolarPiano": "日耀钢琴", "SolarPlatform": "日耀平台", "SolarSink": "日耀水槽", "SolarSofa": "日耀沙发", "SolarTable": "日耀桌", "SolarWorkbench": "日耀工作台", "Fake_SolarChest": "机关日耀箱", "SolarToilet": "日耀马桶", "VortexBathtub": "星旋浴缸", "VortexBed": "星旋床", "VortexBookcase": "星旋书架", "VortexDresser": "星旋梳妆台", "VortexCandelabra": "星旋烛台", "VortexCandle": "星旋蜡烛", "VortexChair": "星旋椅", "VortexChandelier": "星旋吊灯", "VortexChest": "星旋箱", "VortexClock": "星旋时钟", "VortexDoor": "星旋门", "VortexLamp": "星旋灯", "VortexLantern": "星旋灯笼", "VortexPiano": "星旋钢琴", "VortexPlatform": "星旋平台", "VortexSink": "星旋水槽", "VortexSofa": "星旋沙发", "VortexTable": "星旋桌", "VortexWorkbench": "星旋工作台", "Fake_VortexChest": "机关星旋箱", "VortexToilet": "星旋马桶", "NebulaBathtub": "星云浴缸", "NebulaBed": "星云床", "NebulaBookcase": "星云书架", "NebulaDresser": "星云梳妆台", "NebulaCandelabra": "星云烛台", "NebulaCandle": "星云蜡烛", "NebulaChair": "星云椅", "NebulaChandelier": "星云吊灯", "NebulaChest": "星云箱", "NebulaClock": "星云时钟", "NebulaDoor": "星云门", "NebulaLamp": "星云灯", "NebulaLantern": "星云灯笼", "NebulaPiano": "星云钢琴", "NebulaPlatform": "星云平台", "NebulaSink": "星云水槽", "NebulaSofa": "星云沙发", "NebulaTable": "星云桌", "NebulaWorkbench": "星云工作台", "Fake_NebulaChest": "机关星云箱", "NebulaToilet": "星云马桶", "StardustBathtub": "星尘浴缸", "StardustBed": "星尘床", "StardustBookcase": "星尘书架", "StardustDresser": "星尘梳妆台", "StardustCandelabra": "星尘烛台", "StardustCandle": "星尘蜡烛", "StardustChair": "星尘椅", "StardustChandelier": "星尘吊灯", "StardustChest": "星尘箱", "StardustClock": "星尘时钟", "StardustDoor": "星尘门", "StardustLamp": "星尘灯", "StardustLantern": "星尘灯笼", "StardustPiano": "星尘钢琴", "StardustPlatform": "星尘平台", "StardustSink": "星尘水槽", "StardustSofa": "星尘沙发", "StardustTable": "星尘桌", "StardustWorkbench": "星尘工作台", "Fake_StardustChest": "机关星尘箱", "StardustToilet": "星尘马桶", "SolarBrick": "日耀砖", "SolarBrickWall": "日耀砖墙", "VortexBrick": "星旋砖", "VortexBrickWall": "星旋砖墙", "NebulaBrick": "星云砖", "NebulaBrickWall": "星云砖墙", "StardustBrick": "星尘砖", "StardustBrickWall": "星尘砖墙", "CrackedBlueBrick": "破裂蓝砖", "CrackedGreenBrick": "破裂绿砖", "CrackedPinkBrick": "破裂粉砖", "FlowerPacketWild": "野花种子", "MusicBoxDayRemix": "八音盒（白天混音）", "GolfBallDyedBlack": "黑高尔夫球", "GolfBallDyedBlue": "蓝高尔夫球", "GolfBallDyedBrown": "棕高尔夫球", "GolfBallDyedCyan": "青高尔夫球", "GolfBallDyedGreen": "绿高尔夫球", "GolfBallDyedLimeGreen": "橙绿高尔夫球", "GolfBallDyedOrange": "橙高尔夫球", "GolfBallDyedPink": "粉高尔夫球", "GolfBallDyedPurple": "紫高尔夫球", "GolfBallDyedRed": "红高尔夫球", "GolfBallDyedSkyBlue": "天蓝高尔夫球", "GolfBallDyedTeal": "青绿高尔夫球", "GolfBallDyedViolet": "蓝紫高尔夫球", "GolfBallDyedYellow": "黄高尔夫球", "AmberHook": "琥珀钩", "OrangePhaseblade": "橙陨石光剑", "OrangePhasesaber": "橙晶光刃", "AmberRobe": "琥珀长袍", "OrangeStainedGlass": "橙花窗玻璃", "OrangePressurePlate": "橙压力板", "MysticCoilSnake": "耍蛇者长笛", "MagicConch": "魔法海螺", "GolfCart": "高尔夫球车钥匙", "GolfChest": "高尔夫箱", "Fake_GolfChest": "机关高尔夫箱", "DesertChest": "沙岩箱", "Fake_DesertChest": "机关沙岩箱", "SharpTears": "血荆棘", "VampireFrogStaff": "吸血鬼青蛙法杖", "BloodMoonStarter": "血泪", "DripplerFlail": "滴滴怪致残者", "GoldGoldfish": "金金鱼", "GoldGoldfishBowl": "金鱼缸", "GoldStarryGlassBlock": "金星块", "BlueStarryGlassBlock": "蓝星块", "GoldStarryGlassWall": "金星墙", "BlueStarryGlassWall": "蓝星墙", "Apricot": "杏", "Banana": "香蕉", "BlackCurrant": "黑醋栗", "BloodOrange": "血橙", "Cherry": "樱桃", "Coconut": "椰子", "Dragonfruit": "火龙果", "Elderberry": "接骨木果", "Grapefruit": "葡萄柚", "Lemon": "柠檬", "Mango": "芒果", "Peach": "桃子", "Pineapple": "菠萝", "Plum": "李子", "Rambutan": "红毛丹", "Starfruit": "杨桃", "SandstoneBathtub": "沙岩浴缸", "SandstoneBed": "沙岩床", "SandstoneBookcase": "沙岩书架", "SandstoneDresser": "沙岩梳妆台", "SandstoneCandelabra": "沙岩烛台", "SandstoneCandle": "沙岩蜡烛", "SandstoneChair": "沙岩椅", "SandstoneChandelier": "沙岩吊灯", "SandstoneClock": "沙岩时钟", "SandstoneDoor": "沙岩门", "SandstoneLamp": "沙岩灯", "SandstoneLantern": "沙岩灯笼", "SandstonePiano": "沙岩钢琴", "SandstonePlatform": "沙岩平台", "SandstoneSink": "沙岩水槽", "SandstoneSofa": "沙岩沙发", "SandstoneTable": "沙岩桌", "SandstoneWorkbench": "沙岩工作台", "SandstoneToilet": "沙岩马桶", "BloodHamaxe": "血锤斧", "BloodFishingRod": "鱼饵投掷者", "BabyBirdStaff": "雀杖", "VoidMonolith": "虚空天塔柱", "ArrowSign": "箭头标牌", "PaintedArrowSign": "彩色箭头标牌", "GameMasterShirt": "大师级玩家夹克", "GameMasterPants": "大师级玩家裤", "StarPrincessCrown": "星星公主王冠", "StarPrincessDress": "星星公主裙", "SanguineStaff": "血红法杖", "CatBast": "巴斯特雕像", "FoodPlatter": "盘子", "BlackDragonflyJar": "黑蜻蜓罐", "BlueDragonflyJar": "蓝蜻蜓罐", "GreenDragonflyJar": "绿蜻蜓罐", "OrangeDragonflyJar": "橙蜻蜓罐", "RedDragonflyJar": "红蜻蜓罐", "YellowDragonflyJar": "黄蜻蜓罐", "GoldDragonflyJar": "金蜻蜓罐", "BlackDragonfly": "黑蜻蜓", "BlueDragonfly": "蓝蜻蜓", "GreenDragonfly": "绿蜻蜓", "OrangeDragonfly": "橙蜻蜓", "RedDragonfly": "红蜻蜓", "YellowDragonfly": "黄蜻蜓", "GoldDragonfly": "金蜻蜓", "DragonflyStatue": "蜻蜓雕像", "CanOfWorms": "蠕虫罐头", "EncumberingStone": "负重石", "GreenMoss": "绿苔藓", "RedMoss": "红苔藓", "BrownMoss": "棕苔藓", "BlueMoss": "蓝苔藓", "PurpleMoss": "紫苔藓", "LavaMoss": "熔岩苔藓", "BoulderStatue": "巨石雕像", "MusicBoxTitleAlt": "八音盒（旅程开始）", "MusicBoxStorm": "八音盒（暴风雨）", "MusicBoxGraveyard": "八音盒（墓地）", "Seagull": "海鸥", "SeagullStatue": "海鸥雕像", "LadyBug": "瓢虫", "GoldLadyBug": "金瓢虫", "Maggot": "蝇蛆", "MaggotCage": "蝇蛆笼", "CelestialWand": "天界魔棒", "EucaluptusSap": "桉树汁", "KiteBlue": "蓝风筝", "KiteBlueAndYellow": "蓝黄风筝", "KiteRed": "红风筝", "KiteRedAndYellow": "红黄风筝", "KiteYellow": "黄风筝", "Pupfish": "鳉鱼", "Grebe": "䴙䴘", "Rat": "大鼠", "RatCage": "大鼠笼", "PaperAirplaneA": "纸飞机", "PaperAirplaneB": "白纸飞机", "LadybugCage": "瓢虫笼", "BloodRainBow": "血雨弓", "CombatBook": "先进战斗技术", "PortableStool": "梯凳", "DesertTorch": "沙漠火把", "CoralTorch": "珊瑚火把", "CorruptTorch": "腐化火把", "CrimsonTorch": "猩红火把", "HallowedTorch": "神圣火把", "JungleTorch": "丛林火把", "KryptonMoss": "氪苔藓", "XenonMoss": "氙苔藓", "ArgonMoss": "氩苔藓", "RollingCactus": "仙人球", "ScarabFish": "甲虫鱼", "ScorpioFish": "蝎子鱼", "Owl": "猫头鹰", "OwlCage": "猫头鹰笼", "OwlStatue": "猫头鹰雕像", "PupfishBowl": "鳉鱼缸", "GoldLadybugCage": "金瓢虫笼", "Flounder": "偏口鱼", "RockLobster": "岩石龙虾", "LobsterTail": "龙虾尾", "SpectreGoggles": "幽灵护目镜", "Oyster": "牡蛎", "ShuckedOyster": "去壳牡蛎", "WhitePearl": "白珍珠", "BlackPearl": "黑珍珠", "PinkPearl": "粉珍珠", "StoneDoor": "石门", "StonePlatform": "石平台", "OasisFountain": "绿洲喷泉", "Geode": "晶洞", "FloatingTube": "浮游圈", "FrozenCrate": "冰冻匣", "FrozenCrateHard": "针叶木匣", "OasisCrate": "绿洲匣", "OasisCrateHard": "幻象匣", "WaterStrider": "水黾", "GoldWaterStrider": "金水黾", "KiteWyvern": "飞龙风筝", "EchoBlock": "回声块", "LawnFlamingo": "草坪火烈鸟", "MusicBoxUndergroundJungle": "八音盒（地下丛林）", "ScarabBomb": "甲虫炸弹", "WroughtIronFence": "铁艺栅栏", "BeeMinecart": "蜜蜂矿车", "LadybugMinecart": "瓢虫矿车", "PigronMinecart": "猪龙矿车", "SunflowerMinecart": "向日葵矿车", "ScarabFishingRod": "甲虫钓竿", "HellMinecart": "恶魔地狱矿车", "ClusterRocketI": "集束火箭一型", "ClusterRocketII": "集束火箭二型", "WetRocket": "湿火箭", "LavaRocket": "熔岩火箭", "HoneyRocket": "蜂蜜火箭", "ShroomMinecart": "蘑菇矿车", "AmethystMinecart": "紫晶矿车", "TopazMinecart": "黄玉矿车", "SapphireMinecart": "蓝玉矿车", "EmeraldMinecart": "翡翠矿车", "RubyMinecart": "红玉矿车", "DiamondMinecart": "钻石矿车", "MiniNukeI": "迷你核弹一型", "MiniNukeII": "迷你核弹二型", "DryRocket": "干火箭", "SandcastleBucket": "沙堡桶", "Turtle": "龟", "TurtleJungle": "丛林龟", "TurtleStatue": "龟雕像", "TurtleCage": "龟笼", "TurtleJungleCage": "丛林龟笼", "PottedForestCedar": "盆栽森林雪松", "PottedJungleCedar": "盆栽丛林雪松", "PottedHallowCedar": "盆栽神圣雪松", "PottedForestTree": "盆栽森林树", "PottedJungleTree": "盆栽丛林树", "PottedHallowTree": "盆栽神圣树", "PottedForestPalm": "盆栽森林棕榈", "PottedJunglePalm": "盆栽丛林棕榈", "PottedHallowPalm": "盆栽神圣棕榈", "PottedForestBamboo": "盆栽森林竹", "PottedJungleBamboo": "盆栽丛林竹", "PottedHallowBamboo": "盆栽神圣竹", "AmberMinecart": "琥珀矿车", "GrebeCage": "䴙䴘笼", "SeagullCage": "海鸥笼", "WaterStriderCage": "水黾笼", "GoldWaterStriderCage": "金水黾笼", "BeetleMinecart": "甲虫矿车", "MeowmereMinecart": "彩虹猫矿车", "PartyMinecart": "派对矿车", "PirateMinecart": "荷兰人矿车", "SteampunkMinecart": "蒸汽朋克矿车", "Grate": "格栅", "LuckPotionLesser": "弱效幸运药水", "LuckPotion": "幸运药水", "LuckPotionGreater": "强效幸运药水", "Terragrim": "泰拉魔刃", "Seahorse": "海马", "SeahorseCage": "海马笼", "GoldSeahorse": "金海马", "GoldSeahorseCage": "金海马笼", "TimerOneHalfSecond": "1/2秒计时器", "TimerOneFourthSecond": "1/4秒计时器", "WitchBroom": "女巫扫帚", "TheBrideBanner": "僵尸新娘旗", "ZombieMermanBanner": "僵尸人鱼旗", "EyeballFlyingFishBanner": "游荡眼球怪鱼旗", "BloodSquidBanner": "血乌贼旗", "BloodEelBanner": "血鳗鱼旗", "GoblinSharkBanner": "血浆哥布林鲨鱼旗", "EbonstoneEcho": "黑檀石墙", "MudWallEcho": "泥墙", "PearlstoneEcho": "珍珠石墙", "SnowWallEcho": "雪墙", "AmethystEcho": "紫晶石墙", "TopazEcho": "黄玉石墙", "SapphireEcho": "蓝玉石墙", "EmeraldEcho": "翡翠石墙", "RubyEcho": "红玉石墙", "DiamondEcho": "钻石石墙", "Cave1Echo": "绿苔藓墙", "Cave2Echo": "棕苔藓墙", "Cave3Echo": "红苔藓墙", "Cave4Echo": "蓝苔藓墙", "Cave5Echo": "紫苔藓墙", "Cave6Echo": "岩石土墙", "Cave7Echo": "古老石墙", "SpiderEcho": "蜘蛛墙", "CorruptGrassEcho": "腐化草墙", "HallowedGrassEcho": "神圣草墙", "IceEcho": "冰雪墙", "ObsidianBackEcho": "黑曜石墙", "CrimsonGrassEcho": "猩红草墙", "CrimstoneEcho": "猩红石墙", "CaveWall1Echo": "洞穴土墙", "CaveWall2Echo": "粗糙土墙", "Cave8Echo": "崎岖石墙", "Corruption1Echo": "腐化赘生墙", "Corruption2Echo": "腐化团块墙", "Corruption3Echo": "腐化脓疱墙", "Corruption4Echo": "腐化卷须墙", "Crimson1Echo": "猩红硬皮墙", "Crimson2Echo": "猩红疮痂墙", "Crimson3Echo": "猩红尖牙墙", "Crimson4Echo": "猩红水疱墙", "Dirt1Echo": "分层土墙", "Dirt2Echo": "剥落土墙", "Dirt3Echo": "破裂土墙", "Dirt4Echo": "皱曲土墙", "Hallow1Echo": "神圣棱柱墙", "Hallow2Echo": "神圣洞穴墙", "Hallow3Echo": "神圣碎块墙", "Hallow4Echo": "神圣结晶墙", "Jungle1Echo": "地衣石墙", "Jungle2Echo": "多叶丛林墙", "Jungle3Echo": "常春藤石墙", "Jungle4Echo": "丛林藤蔓墙", "Lava1Echo": "余烬墙", "Lava2Echo": "灰渣墙", "Lava3Echo": "岩浆墙", "Lava4Echo": "阴燃石墙", "Rocks1Echo": "破旧石墙", "Rocks2Echo": "钟乳石墙", "Rocks3Echo": "斑驳石墙", "Rocks4Echo": "断裂石墙", "GolfTrophyBronze": "高尔夫铜奖杯", "GolfTrophySilver": "高尔夫银奖杯", "GolfTrophyGold": "高尔夫金奖杯", "GolfClubStoneIron": "破旧高尔夫球杆（铁杆）", "GolfClubRustyPutter": "破旧高尔夫球杆（推杆）", "GolfClubBronzeWedge": "破旧高尔夫球杆（挖起杆）", "GolfClubWoodDriver": "破旧高尔夫球杆（木杆）", "GolfClubMythrilIron": "精致高尔夫球杆（铁杆）", "GolfClubLeadPutter": "精致高尔夫球杆（推杆）", "GolfClubGoldWedge": "精致高尔夫球杆（挖起杆）", "GolfClubPearlwoodDriver": "精致高尔夫球杆（木杆）", "GolfClubTitaniumIron": "高级高尔夫球杆（铁杆）", "GolfClubShroomitePutter": "高级高尔夫球杆（推杆）", "GolfClubDiamondWedge": "高级高尔夫球杆（挖起杆）", "GolfClubChlorophyteDriver": "高级高尔夫球杆（木杆）", "BloodNautilusBanner": "恐惧鹦鹉螺旗", "MusicBoxJungleNight": "八音盒（丛林夜晚）", "BirdieRattle": "小鸟拨浪鼓", "ExoticEasternChewToy": "奇异咀嚼玩具", "BedazzledNectar": "眩晕花蜜", "BambooBlock": "竹子", "BambooBlockWall": "竹墙", "LargeBambooBlock": "大竹子", "LargeBambooBlockWall": "大竹墙", "BambooBathtub": "竹浴缸", "BambooBed": "竹床", "BambooBookcase": "竹书架", "BambooDresser": "竹梳妆台", "BambooCandelabra": "竹烛台", "BambooCandle": "竹蜡烛", "BambooChair": "竹椅", "BambooChandelier": "竹吊灯", "BambooChest": "竹箱", "BambooClock": "竹时钟", "BambooDoor": "竹门", "BambooLamp": "竹灯", "BambooLantern": "竹灯笼", "BambooPiano": "竹钢琴", "BambooPlatform": "竹平台", "BambooSink": "竹水槽", "BambooSofa": "竹沙发", "BambooTable": "竹桌", "BambooWorkbench": "竹工作台", "Fake_BambooChest": "机关竹箱", "BambooToilet": "竹马桶", "DemonHorns": "恶魔角", "BunnyEars": "兔耳朵", "DevilHorns": "魔鬼角", "Fedora": "软呢帽", "ChefHat": "厨师帽", "ChefShirt": "厨师服", "ChefPants": "厨师裤", "MarbleColumn": "大理石柱", "PlasmaLamp": "等离子灯", "HellCake": "地狱蛋糕块", "ChumBucket": "鱼饵桶", "GardenGnome": "花园侏儒", "KiteBoneSerpent": "骨蛇风筝", "KiteWorldFeeder": "吞世怪风筝", "KiteBunny": "兔兔风筝", "StarHairpin": "星星发夹", "HeartHairpin": "心形发夹", "BambooLeaf": "竹叶", "KitePigron": "猪龙风筝", "AppleJuice": "苹果汁", "GrapeJuice": "葡萄汁", "Lemonade": "柠檬水", "BananaDaiquiri": "冰冻香蕉代基里", "PeachSangria": "桃子果酒", "PinaColada": "椰林飘香", "TropicalSmoothie": "热带奶昔", "BloodyMoscato": "血腥麝香葡萄", "SmoothieofDarkness": "暗黑奶昔", "PrismaticPunch": "七彩潘趣酒", "FruitJuice": "果汁", "FruitSalad": "水果沙拉", "SharkBait": "鲨鱼鱼饵", "AndrewSphinx": "<PERSON>狮身人面像", "WatchfulAntlion": "警惕的蚁狮", "BurningSpirit": "燃烧幽魂", "JawsOfDeath": "鬼门关", "TheSandsOfSlime": "史莱姆之沙", "SnakesIHateSnakes": "蛇，我讨厌蛇", "LifeAboveTheSand": "沙上的生活", "Oasis": "绿洲", "PrehistoryPreserved": "史前留存", "AncientTablet": "远古碑牌", "Uluru": "乌鲁鲁巨岩", "VisitingThePyramids": "参观金字塔", "BandageBoy": "绷带男孩", "DivineEye": "神眼", "AmethystStoneBlock": "紫晶石块", "TopazStoneBlock": "黄玉石块", "SapphireStoneBlock": "蓝玉石块", "EmeraldStoneBlock": "翡翠石块", "RubyStoneBlock": "红玉石块", "DiamondStoneBlock": "钻石石块", "AmberStoneBlock": "琥珀石块", "AmberStoneWallEcho": "琥珀石墙", "KiteManEater": "食人怪风筝", "KiteJellyfishBlue": "蓝水母风筝", "KiteJellyfishPink": "粉水母风筝", "KiteShark": "鲨鱼风筝", "SuperHeroMask": "超级英雄面具", "SuperHeroCostume": "超级英雄服装", "SuperHeroTights": "超级英雄紧身裤", "PinkFairyJar": "粉仙灵罐", "GreenFairyJar": "绿仙灵罐", "BlueFairyJar": "蓝仙灵罐", "FogMachine": "造雾机", "GolfPainting1": "起伏的果岭", "GolfPainting2": "静物球习作", "GolfPainting3": "球来了！", "GolfPainting4": "映像的二重性", "Gladius": "罗马短剑", "IvyGuitar": "常春藤", "PrettyPinkDressSkirt": "漂亮粉连衣裙", "PrettyPinkDressPants": "漂亮粉长袜", "UnicornHornHat": "假独角兽角", "PrettyPinkRibbon": "漂亮粉丝带", "BambooFence": "竹栅栏", "KiteSandShark": "沙鲨风筝", "ThinIce": "薄冰", "KiteBunnyCorrupt": "腐化兔兔风筝", "KiteBunnyCrimson": "猩红兔兔风筝", "StormTigerStaff": "沙漠虎杖", "DrumStick": "鼓槌", "KiteGoldfish": "金鱼风筝", "FogboundDye": "灰雾染料", "BloodbathDye": "大屠杀染料", "GlowPaint": "夜明涂料", "KiteAngryTrapper": "愤怒捕手风筝", "KiteKoi": "锦鲤风筝", "KiteCrawltipede": "千足蜈蚣风筝", "KiteSpectrum": "光谱风筝", "KiteWanderingEye": "游荡眼球怪风筝", "KiteUnicorn": "独角兽风筝", "DandelionBanner": "愤怒蒲公英旗", "GnomeBanner": "侏儒旗", "UndertakerHat": "掘墓者帽", "UndertakerCoat": "掘墓者外套", "DesertCampfire": "沙漠篝火", "CoralCampfire": "珊瑚篝火", "CorruptCampfire": "腐化篝火", "CrimsonCampfire": "猩红篝火", "HallowedCampfire": "神圣篝火", "JungleCampfire": "丛林篝火", "SoulBottleLight": "光明之魂瓶", "SoulBottleNight": "暗影之魂瓶", "SoulBottleFlight": "飞翔之魂瓶", "SoulBottleSight": "视域之魂瓶", "SoulBottleMight": "力量之魂瓶", "SoulBottleFright": "恐惧之魂瓶", "MudBud": "泥芽", "ReleaseDoves": "放飞鸽子", "ReleaseLantern": "放飞灯笼", "QuadBarrelShotgun": "四管霰弹枪", "FuneralHat": "葬礼帽", "FuneralCoat": "葬礼外套", "FuneralPants": "葬礼裤", "TragicUmbrella": "悲剧雨伞", "VictorianGothHat": "维多利亚哥特帽", "VictorianGothDress": "维多利亚哥特裙", "TatteredWoodSign": "破旧木制标牌", "GravediggerShovel": "掘墓者铲子", "DungeonDesertChest": "沙漠箱", "Fake_DungeonDesertChest": "机关沙漠箱", "DungeonDesertKey": "沙漠钥匙", "MolluskWhistle": "贝壳哨", "BorealBeam": "针叶木梁", "RichMahoganyBeam": "红木梁", "GraniteColumn": "花岗岩柱", "SandstoneColumn": "沙岩柱", "MushroomBeam": "蘑菇梁", "Nevermore": "永不复焉", "Reborn": "重生", "Graveyard": "墓地", "GhostManifestation": "鬼魂显现", "WickedUndead": "邪恶亡灵", "BloodyGoblet": "血腥酒杯", "StillLife": "静物", "TerraToilet": "泰拉马桶", "GhostarsWings": "Ghostar的无极翼", "GhostarSkullPin": "Ghostar的魂罐", "GhostarShirt": "Ghostar的上衣", "GhostarPants": "Ghostar的紧身裤", "BallOfFuseWire": "保险丝球", "FullMoonSqueakyToy": "满月尖叫玩具", "OrnateShadowKey": "华丽暗影钥匙", "DrManFlyMask": "苍蝇人博士面具", "DrManFlyLabCoat": "苍蝇人博士实验服", "ButcherMask": "屠夫面具", "ButcherApron": "屠夫的染血围裙", "ButcherPants": "屠夫的染血裤", "Football": "橄榄球", "CoffinMinecart": "棺材矿车", "FoodBarbarianWings": "FoodBarbarian的褴褛龙之翼", "FoodBarbarianHelm": "FoodBarbarian的角盔", "FoodBarbarianArmor": "FoodBarbarian的野狼护肩", "FoodBarbarianGreaves": "FoodBarbarian的野猪护胫", "SafemanWings": "Safeman的毛毯斗篷", "SafemanSunHair": "Safeman的晴天", "SafemanSunDress": "Safeman的太阳裙", "SafemanDressLeggings": "Safeman的粉色护腿", "GroxTheGreatWings": "Grox The Great的翅膀", "GroxTheGreatHelm": "Grox The Great的有角风帽", "GroxTheGreatArmor": "Grox The Great的护胸", "GroxTheGreatGreaves": "Grox The Great的护胫", "SparkleGuitar": "星星吉他", "SquirrelHook": "松鼠钩", "RockGolemHead": "岩石巨人头", "CritterShampoo": "小动物香波", "Shroomerang": "蘑菇回旋镖", "DontHurtCrittersBook": "小动物友谊指南", "DogEars": "狗耳朵", "DogTail": "狗尾巴", "FoxEars": "狐狸耳朵", "FoxTail": "狐狸尾巴", "LizardEars": "蜥蜴耳朵", "LizardTail": "蜥蜴尾巴", "PandaEars": "熊猫耳朵", "BunnyTail": "兔尾巴", "FairyGlowstick": "仙灵荧光棒", "LightningCarrot": "闪电胡萝卜", "MushroomHat": "蘑菇帽", "MushroomVest": "蘑菇背心", "MushroomPants": "蘑菇裤", "HunterCloak": "猎人斗篷", "ZapinatorGray": "灰冲击枪", "ZapinatorOrange": "橙冲击枪", "TreeGlobe": "树球", "WorldGlobe": "世界球", "CombatWrench": "战斗扳手", "PaintedHorseSaddle": "蒙尘牛皮鞍", "MajesticHorseSaddle": "皇家鎏金鞍", "DarkHorseSaddle": "黑铆钉鞍", "JoustingLance": "骑枪", "ShadowJoustingLance": "暗影骑枪", "HallowJoustingLance": "神圣骑枪", "DemonConch": "恶魔海螺", "BottomlessLavaBucket": "无底熔岩桶", "FireproofBugNet": "防熔岩虫网", "FlameWakerBoots": "烈焰靴", "WetBomb": "湿炸弹", "LavaBomb": "熔岩炸弹", "HoneyBomb": "蜂蜜炸弹", "DryBomb": "干炸弹", "LicenseCat": "猫咪许可证", "LicenseDog": "狗狗许可证", "GemSquirrelAmethyst": "紫晶松鼠", "GemSquirrelTopaz": "黄玉松鼠", "GemSquirrelSapphire": "蓝玉松鼠", "GemSquirrelEmerald": "翡翠松鼠", "GemSquirrelRuby": "红玉松鼠", "GemSquirrelDiamond": "钻石松鼠", "GemSquirrelAmber": "琥珀松鼠", "GemBunnyAmethyst": "紫晶兔兔", "GemBunnyTopaz": "黄玉兔兔", "GemBunnySapphire": "蓝玉兔兔", "GemBunnyEmerald": "翡翠兔兔", "GemBunnyRuby": "红玉兔兔", "GemBunnyDiamond": "钻石兔兔", "GemBunnyAmber": "琥珀兔兔", "HellButterfly": "地狱蝴蝶", "HellButterflyJar": "地狱蝴蝶罐", "Lavafly": "熔岩萤火虫", "LavaflyinaBottle": "熔岩萤火虫瓶", "MagmaSnail": "岩浆蜗牛", "MagmaSnailCage": "岩浆蜗牛笼", "GemTreeTopazSeed": "黄玉宝石果", "GemTreeAmethystSeed": "紫晶宝石果", "GemTreeSapphireSeed": "蓝玉宝石果", "GemTreeEmeraldSeed": "翡翠宝石果", "GemTreeRubySeed": "红玉宝石果", "GemTreeDiamondSeed": "钻石宝石果", "GemTreeAmberSeed": "琥珀宝石果", "LavaAbsorbantSponge": "熔岩吸收绵", "HallowedHood": "神圣兜帽", "LavaCrate": "黑曜石匣", "LavaCrateHard": "狱石匣", "PotSuspended": "吊挂盆", "PotSuspendedDaybloom": "吊挂太阳花", "PotSuspendedMoonglow": "吊挂月光草", "PotSuspendedWaterleaf": "吊挂水叶草", "PotSuspendedShiverthorn": "吊挂寒颤棘", "PotSuspendedBlinkroot": "吊挂闪耀根", "PotSuspendedDeathweedCorrupt": "吊挂腐化死亡草", "PotSuspendedDeathweedCrimson": "吊挂猩红死亡草", "PotSuspendedFireblossom": "吊挂火焰花", "BrazierSuspended": "吊挂火盆", "ObsidianLockbox": "黑曜石锁盒", "SuperheatedBlood": "过热的血", "VolcanoSmall": "小火山", "VolcanoLarge": "大火山", "LavaFishingHook": "防熔岩钓钩", "PotionOfReturn": "返回药水", "AmethystBunnyCage": "紫晶兔兔笼", "TopazBunnyCage": "黄玉兔兔笼", "SapphireBunnyCage": "蓝玉兔兔笼", "EmeraldBunnyCage": "翡翠兔兔笼", "RubyBunnyCage": "红玉兔兔笼", "DiamondBunnyCage": "钻石兔兔笼", "AmberBunnyCage": "琥珀兔兔笼", "AmethystSquirrelCage": "紫晶松鼠笼", "TopazSquirrelCage": "黄玉松鼠笼", "SapphireSquirrelCage": "蓝玉松鼠笼", "EmeraldSquirrelCage": "翡翠松鼠笼", "RubySquirrelCage": "红玉松鼠笼", "DiamondSquirrelCage": "钻石松鼠笼", "AmberSquirrelCage": "琥珀松鼠笼", "AncientHallowedMask": "远古神圣面具", "AncientHallowedHelmet": "远古神圣头盔", "AncientHallowedHeadgear": "远古神圣头饰", "AncientHallowedHood": "远古神圣兜帽", "AncientHallowedPlateMail": "远古神圣板甲", "AncientHallowedGreaves": "远古神圣护胫", "VanityTreeSakuraSeed": "樱花树苗", "PottedLavaPlantPalm": "盆栽岩浆棕榈", "PottedLavaPlantBush": "盆栽硫磺灌木", "PottedLavaPlantBramble": "盆栽火荆棘", "PottedLavaPlantBulb": "盆栽熔岩球茎", "PottedLavaPlantTendrils": "盆栽余烬卷须", "HellfireTreads": "狱炎战靴", "LavaFishbowl": "熔岩蛇缸", "VanityTreeYellowWillowSeed": "黄柳树苗", "PirateShipMountItem": "黑斑", "SpookyWoodMountItem": "魔法树枝", "SantankMountItem": "玩具坦克", "WallOfFleshGoatMountItem": "山羊骷髅头", "DarkMageBookMountItem": "暗黑魔法师巨著", "KingSlimePetItem": "皇家美味", "EyeOfCthulhuPetItem": "可疑咧嘴眼球", "EaterOfWorldsPetItem": "蠕动残骸", "BrainOfCthulhuPetItem": "罐中脑", "SkeletronPetItem": "疯狂骷髅头", "QueenBeePetItem": "闪耀蜂蜜", "DestroyerPetItem": "失效探测器", "TwinsPetItem": "微型双子魔眼", "SkeletronPrimePetItem": "机器人骷髅头", "PlanteraPetItem": "世纪之花幼苗", "GolemPetItem": "石巨人守卫", "DukeFishronPetItem": "海猪肉", "LunaticCultistPetItem": "碑牌碎片", "MoonLordPetItem": "月亮乌贼", "FairyQueenPetItem": "光之珠宝", "PumpkingPetItem": "南瓜香薰蜡烛", "EverscreamPetItem": "灌木星星", "IceQueenPetItem": "冰冻王冠", "MartianPetItem": "宇宙滑板", "DD2OgrePetItem": "食人魔棍棒", "DD2BetsyPetItem": "双足翼龙蛋", "PogoStick": "蹦蹦跷", "LicenseBunny": "兔兔许可证", "TungstenBullet": "钨子弹", "TeleportationPylonJungle": "丛林晶塔", "TeleportationPylonPurity": "森林晶塔", "TeleportationPylonHallow": "神圣晶塔", "TeleportationPylonUnderground": "洞穴晶塔", "TeleportationPylonOcean": "海洋晶塔", "TeleportationPylonDesert": "沙漠晶塔", "TeleportationPylonSnow": "雪原晶塔", "TeleportationPylonMushroom": "蘑菇晶塔", "CavernFountain": "洞穴喷泉", "FairyQueenBossBag": "宝藏袋（光之女皇）", "FairyQueenTrophy": "光之女皇纪念章", "FairyQueenMask": "光之女皇面具", "EyeofCthulhuMasterTrophy": "克苏鲁之眼圣物", "EaterofWorldsMasterTrophy": "世界吞噬怪圣物", "BrainofCthulhuMasterTrophy": "克苏鲁之脑圣物", "SkeletronMasterTrophy": "骷髅王圣物", "QueenBeeMasterTrophy": "蜂王圣物", "KingSlimeMasterTrophy": "史莱姆王圣物", "WallofFleshMasterTrophy": "血肉墙圣物", "TwinsMasterTrophy": "双子魔眼圣物", "DestroyerMasterTrophy": "毁灭者圣物", "SkeletronPrimeMasterTrophy": "机械骷髅王圣物", "PlanteraMasterTrophy": "世纪之花圣物", "GolemMasterTrophy": "石巨人圣物", "DukeFishronMasterTrophy": "猪龙鱼公爵圣物", "LunaticCultistMasterTrophy": "拜月教邪教徒圣物", "MoonLordMasterTrophy": "月亮领主圣物", "UFOMasterTrophy": "火星飞碟圣物", "FlyingDutchmanMasterTrophy": "荷兰飞盗船圣物", "MourningWoodMasterTrophy": "哀木圣物", "PumpkingMasterTrophy": "南瓜王圣物", "IceQueenMasterTrophy": "冰雪女王圣物", "EverscreamMasterTrophy": "常绿尖叫怪圣物", "SantankMasterTrophy": "圣诞坦克圣物", "DarkMageMasterTrophy": "暗黑魔法师圣物", "OgreMasterTrophy": "食人魔圣物", "BetsyMasterTrophy": "双足翼龙圣物", "FairyQueenMasterTrophy": "光之女皇圣物", "QueenSlimeMasterTrophy": "史莱姆皇后圣物", "HallowBossDye": "七彩染料", "RainbowWings": "女皇之翼", "DirtBomb": "土炸弹", "DirtStickyBomb": "粘性土炸弹", "QueenSlimeBossBag": "宝藏袋（史莱姆皇后）", "QueenSlimeTrophy": "史莱姆皇后纪念章", "QueenSlimeMask": "史莱姆皇后面具", "QueenSlimePetItem": "王室美食", "AccentSlab": "石材板", "EmpressButterfly": "七彩草蛉", "TruffleWormCage": "松露虫笼", "EmpressButterflyJar": "七彩草蛉罐", "RockGolemBanner": "岩石巨人旗", "BloodMummyBanner": "血木乃伊旗", "SporeSkeletonBanner": "孢子骷髅旗", "SporeBatBanner": "孢子蝙蝠旗", "LarvaeAntlionBanner": "蚁狮幼虫旗", "CrimsonBunnyBanner": "毒兔兔旗", "CrimsonGoldfishBanner": "毒金鱼旗", "CrimsonPenguinBanner": "毒企鹅旗", "BigMimicCorruptionBanner": "腐化宝箱怪旗", "BigMimicCrimsonBanner": "猩红宝箱怪旗", "BigMimicHallowBanner": "神圣宝箱怪旗", "MossHornetBanner": "苔藓黄蜂旗", "WanderingEyeBanner": "游荡眼球怪旗", "CreativeWings": "雏翼", "MusicBoxQueenSlime": "八音盒（史莱姆皇后）", "BouncingShield": "中士联盾", "DiggingMoleMinecart": "挖掘鼹鼠矿车", "BlandWhip": "皮鞭", "MaceWhip": "晨星", "ScytheWhip": "暗黑收割", "SwordWhip": "迪朗达尔", "ThornWhip": "荆鞭", "FireWhip": "鞭炮", "CoolWhip": "冷鞭", "RainbowWhip": "万花筒", "MusicBoxEmpressOfLight": "八音盒（光之女皇）", "CrystalNinjaHelmet": "水晶刺客兜帽", "CrystalNinjaChestplate": "水晶刺客上衣", "CrystalNinjaLeggings": "水晶刺客裤", "QueenSlimeMountSaddle": "明胶女式鞍", "QueenSlimeHook": "失谐钩爪", "Smolstar": "刃杖", "PiercingStarlight": "星光", "FairyQueenMagicItem": "夜光", "FairyQueenRangedItem": "日暮", "RabbitOrder": "兔子头盔", "TeleportationPylonVictory": "万能晶塔", "QueenSlimeCrystal": "明胶水晶", "EmpressFlightBooster": "翱翔徽章", "VolatileGelatin": "挥发明胶", "GelBalloon": "闪耀史莱姆气球", "MusicBoxDukeFishron": "八音盒（猪龙鱼公爵）", "MusicBoxMorningRain": "八音盒（晨雨）", "MusicBoxConsoleTitle": "八音盒（标题备选曲）", "Zenith": "天顶剑", "ChippysCouch": "Chippy的沙发", "GraduationCapBlue": "蓝毕业帽", "GraduationCapMaroon": "褐红毕业帽", "GraduationCapBlack": "黑毕业帽", "GraduationGownBlue": "蓝毕业礼服", "GraduationGownMaroon": "褐红毕业礼服", "GraduationGownBlack": "黑毕业礼服", "LongRainbowTrailWings": "天界星盘", "TerrasparkBoots": "泰拉闪耀靴", "OceanCrate": "海洋匣", "OceanCrateHard": "海边匣", "MusicBoxUndergroundDesert": "八音盒（地下沙漠）", "BadgersHat": "Badger的帽子", "DeadMansSweater": "死人毛衣", "TeaKettle": "茶壶", "Teacup": "一杯茶", "EmpressBlade": "泰拉棱镜", "MoonLordLegs": "月亮领主腿", "TreasureMagnet": "宝藏磁石", "Mace": "链锤", "FlamingMace": "烈焰链锤", "MusicBoxOWRain": "异界八音盒（雨）", "MusicBoxOWDay": "异界八音盒（地表世界）", "MusicBoxOWNight": "异界八音盒（夜晚）", "MusicBoxOWUnderground": "异界八音盒（地下）", "MusicBoxOWDesert": "异界八音盒（沙漠）", "MusicBoxOWOcean": "异界八音盒（海洋）", "MusicBoxOWMushroom": "异界八音盒（蘑菇）", "MusicBoxOWDungeon": "异界八音盒（地牢）", "MusicBoxOWSpace": "异界八音盒（太空）", "MusicBoxOWUnderworld": "异界八音盒（地狱）", "MusicBoxOWSnow": "异界八音盒（雪原）", "MusicBoxOWCorruption": "异界八音盒（腐化之地）", "MusicBoxOWUndergroundCorruption": "异界八音盒（地下腐化之地）", "MusicBoxOWCrimson": "异界八音盒（猩红之地）", "MusicBoxOWUndergroundCrimson": "异界八音盒（地下猩红之地）", "MusicBoxOWUndergroundSnow": "异界八音盒（冰雪）", "MusicBoxOWUndergroundHallow": "异界八音盒（地下神圣之地）", "MusicBoxOWBloodMoon": "异界八音盒（恐惧）", "MusicBoxOWBoss2": "异界八音盒（Boss 2）", "MusicBoxOWBoss1": "异界八音盒（Boss 1）", "MusicBoxOWInvasion": "异界八音盒（入侵）", "MusicBoxOWTowers": "异界八音盒（天界柱）", "MusicBoxOWMoonLord": "异界八音盒（月亮Boss）", "MusicBoxOWPlantera": "异界八音盒（世纪之花）", "MusicBoxOWJungle": "异界八音盒（丛林）", "MusicBoxOWWallOfFlesh": "异界八音盒（血肉墙）", "MusicBoxOWHallow": "异界八音盒（神圣之地）", "MilkCarton": "盒装牛奶", "CoffeeCup": "咖啡", "TorchGodsFavor": "火把神的恩宠", "MusicBoxCredits": "八音盒（旅程结束）", "LavaproofTackleBag": "防熔岩渔具袋", "BeeHive": "蜂巢", "AntlionEggs": "蚁狮卵", "TimelessTravelerHood": "永恒旅人兜帽", "TimelessTravelerRobe": "永恒旅人斗篷", "TimelessTravelerBottom": "永恒旅人鞋履", "FloretProtectorHelmet": "护花者头盔", "FloretProtectorChestplate": "护花者衬衫", "FloretProtectorLegs": "护花者裤子", "CapricornMask": "摩羯座头盔", "CapricornChestplate": "摩羯座护胸", "CapricornLegs": "摩羯座兽蹄", "CapricornTail": "摩羯座尾巴", "TVHeadMask": "视频脸", "TVHeadSuit": "激光外套", "TVHeadPants": "细条纹裤", "PrincessWeapon": "共鸣权杖", "FlinxFurCoat": "小雪怪皮毛外套", "FlinxStaff": "小雪怪法杖", "FlinxFur": "小雪怪皮毛", "RoyalTiara": "皇家头冠", "RoyalDressTop": "皇家女衫", "RoyalDressBottom": "皇家礼服裙", "PlaguebringerHelmet": "瘟疫使者骷髅头", "PlaguebringerChestplate": "瘟疫使者斗篷", "PlaguebringerGreaves": "瘟疫使者便鞋", "RoninHat": "浪人阵笠", "RoninShirt": "浪人浴衣", "RoninPants": "浪人木屐", "RainbowCursor": "彩虹光标", "BoneWhip": "脊柱骨鞭", "RoyalScepter": "皇家权杖", "GlassSlipper": "水晶鞋", "PrinceUniform": "王子制服", "PrincePants": "王子裤", "PrinceCape": "王子披风", "PottedCrystalPlantFern": "盆栽水晶蕨", "PottedCrystalPlantSpiral": "盆栽水晶螺旋", "PottedCrystalPlantTeardrop": "盆栽水晶泪滴", "PottedCrystalPlantTree": "盆栽水晶树", "Princess64": "公主64", "PaintingOfALass": "少女画像", "DarkSideHallow": "神圣之地的黑暗面", "BerniePetItem": "伯尼的钮扣", "DeerclopsPetItem": "独眼巨鹿眼球", "PigPetItem": "怪物肉", "MonsterLasagna": "怪物千层面", "FroggleBunwich": "蛙腿三明治", "TentacleSpike": "触手钉锤", "LucyTheAxe": "露西斧", "HamBat": "火腿棍", "BatBat": "蝙蝠棍", "ChesterPetItem": "眼骨", "GarlandHat": "花环", "BoneHelm": "骸骨头盔", "Eyebrella": "眼球伞", "WilsonShirt": "绅士马甲", "WilsonPants": "绅士裤子", "WilsonBeardShort": "绅士胡子", "WilsonBeardLong": "绅士长胡子", "WilsonBeardMagnificent": "绅士大胡子", "Magiluminescence": "魔光护符", "DeerclopsTrophy": "独眼巨鹿纪念章", "DeerclopsMask": "独眼巨鹿面具", "DeerclopsBossBag": "宝藏袋（独眼巨鹿）", "DeerclopsMasterTrophy": "独眼巨鹿圣物", "MusicBoxDeerclops": "八音盒（独眼巨鹿）", "GlommerPetItem": "咕噜咪的花", "AbigailsFlower": "阿比盖尔的花", "WillowShirt": "纵火者毛衣", "WillowSkirt": "纵火者裙", "PewMaticHorn": "气喇叭", "WeatherPain": "天候棒", "HoundiusShootius": "眼球激光塔", "DontStarveShaderItem": "收音机", "DeerThing": "鹿华", "PaintingWilson": "绅士科学家", "PaintingWillow": "纵火者", "PaintingWendy": "丧亲者", "PaintingWolfgang": "大力士", "FartMinecart": "放屁车", "HandOfCreation": "创造之手", "ResplendentDessert": "华美甜点", "VioletMoss": "氖苔藓", "RainbowMoss": "氦苔藓", "Stinkbug": "臭虫", "StinkbugCage": "臭虫笼", "WandofFrosting": "结霜魔棒", "CoralBathtub": "珊瑚礁浴缸", "CoralBed": "珊瑚礁床", "CoralBookcase": "珊瑚礁书架", "CoralDresser": "珊瑚礁梳妆台", "CoralCandelabra": "珊瑚礁烛台", "CoralCandle": "珊瑚礁蜡烛", "CoralChair": "珊瑚礁椅", "CoralChandelier": "珊瑚礁吊灯", "CoralChest": "珊瑚礁箱", "CoralClock": "珊瑚礁时钟", "CoralDoor": "珊瑚礁门", "CoralLamp": "珊瑚礁灯", "CoralLantern": "珊瑚礁灯笼", "CoralPiano": "珊瑚礁钢琴", "CoralPlatform": "珊瑚礁平台", "CoralSink": "珊瑚礁水槽", "CoralSofa": "珊瑚礁沙发", "CoralTable": "珊瑚礁桌", "CoralWorkbench": "珊瑚礁工作台", "Fake_CoralChest": "机关珊瑚礁箱", "CoralToilet": "珊瑚礁马桶", "BalloonBathtub": "气球浴缸", "BalloonBed": "气球床", "BalloonBookcase": "气球书架", "BalloonDresser": "气球梳妆台", "BalloonCandelabra": "气球烛台", "BalloonCandle": "气球蜡烛", "BalloonChair": "气球椅", "BalloonChandelier": "气球吊灯", "BalloonChest": "气球箱", "BalloonClock": "气球时钟", "BalloonDoor": "气球门", "BalloonLamp": "气球灯", "BalloonLantern": "气球灯笼", "BalloonPiano": "气球钢琴", "BalloonPlatform": "气球平台", "BalloonSink": "气球水槽", "BalloonSofa": "气球沙发", "BalloonTable": "气球桌", "BalloonWorkbench": "气球工作台", "Fake_BalloonChest": "机关气球箱", "BalloonToilet": "气球马桶", "AshWoodBathtub": "灰烬木浴缸", "AshWoodBed": "灰烬木床", "AshWoodBookcase": "灰烬木书架", "AshWoodDresser": "灰烬木梳妆台", "AshWoodCandelabra": "灰烬木烛台", "AshWoodCandle": "灰烬木蜡烛", "AshWoodChair": "灰烬木椅", "AshWoodChandelier": "灰烬木吊灯", "AshWoodChest": "灰烬木箱", "AshWoodClock": "灰烬木时钟", "AshWoodDoor": "灰烬木门", "AshWoodLamp": "灰烬木灯", "AshWoodLantern": "灰烬木灯笼", "AshWoodPiano": "灰烬木钢琴", "AshWoodPlatform": "灰烬木平台", "AshWoodSink": "灰烬木水槽", "AshWoodSofa": "灰烬木沙发", "AshWoodTable": "灰烬木桌", "AshWoodWorkbench": "灰烬木工作台", "Fake_AshWoodChest": "机关灰烬木箱", "AshWoodToilet": "灰烬木马桶", "LicenseSlime": "史莱姆许可证", "AshGrassSeeds": "灰烬草种子", "AshWood": "灰烬木", "AshWoodWall": "灰烬木墙", "AshWoodFence": "灰烬木栅栏", "Outcast": "被驱逐之人", "FairyGuides": "仙灵指引", "AHorribleNightforAlchemy": "恐怖炼药之夜", "MorningHunt": "晨猎", "SuspiciouslySparkly": "可疑闪光", "Requiem": "安魂", "CatSword": "猫剑", "KargohsSummon": "Kargoh的召唤", "HighPitch": "高音", "AMachineforTerrarians": "给泰拉瑞亚人的机器", "TerraBladeChronicles": "泰拉刃纪事", "BennyWarhol": "本尼·沃霍尔", "LizardKing": "蜥蜴王", "MySon": "我的儿子", "Duality": "二元性", "ParsecPals": "秒差距伙伴", "RemnantsofDevotion": "虔诚的余孽", "NotSoLostInParadise": "并未如此迷失在乐园", "OcularResonance": "视觉共振", "WingsofEvil": "邪恶之翼", "Constellation": "星座", "Eyezorhead": "眼怪头", "DreadoftheRedSea": "红海恐惧", "DoNotEattheVileMushroom": "不要吃魔菇！", "YuumaTheBlueTiger": "蓝虎Yuuma", "MoonmanandCompany": "月亮人公司", "SunshineofIsrapony": "Israpony的阳光", "Purity": "纯净", "SufficientlyAdvanced": "极其先进", "StrangeGrowth": "奇异植株", "HappyLittleTree": "快乐小树", "StrangeDeadFellows": "奇异死者", "Secrets": "秘密", "Thunderbolt": "雷电", "TheWerewolf": "狼人", "BlessingfromTheHeavens": "天堂的祝福", "LoveisintheTrashSlot": "爱在垃圾桶里", "Crustography": "甲壳摄影", "Fangs": "獠牙", "HailtotheKing": "国王万岁", "StinkbugHousingBlocker": "臭虫拦截器", "StinkbugHousingBlockerEcho": "幽灵臭虫拦截器", "SeeTheWorldForWhatItIs": "看清世界的真谛", "WhatLurksBelow": "下面潜伏着什么", "ThisIsGettingOutOfHand": "局面失控", "Buddies": "铁哥们", "MidnightSun": "午夜太阳", "CouchGag": "沙发笑话", "Bioluminescence": "生物发光", "Wildflowers": "野花", "VikingVoyage": "维京之旅", "SilentFish": "沉默的鱼", "RoyalRomance": "皇家浪漫", "TheDuke": "公爵", "Flymeal": "臭虫剑", "LadyOfTheLake": "湖中女士", "Bifrost": "彩虹桥", "Heartlands": "心脏地带", "ForestTroll": "森林巨魔", "AuroraBorealis": "北极光", "JojaCola": "<PERSON><PERSON>可乐", "JunimoPetItem": "星之果实", "SpicyPepper": "辣椒", "Pomegranate": "石榴", "AshWoodHelmet": "灰烬木头盔", "AshWoodBreastplate": "灰烬木胸甲", "AshWoodGreaves": "灰烬木护胫", "AshWoodBow": "灰烬木弓", "AshWoodHammer": "灰烬木锤", "AshWoodSword": "灰烬木剑", "MoonGlobe": "月亮球", "VenomDartTrap": "毒液飞镖机关", "BiomeSightPotion": "生物群系视觉药水", "FishingBobber": "钓鱼浮标", "FishingBobberGlowingStar": "发光钓鱼浮标", "FishingBobberGlowingLava": "熔岩苔藓钓鱼浮标", "FishingBobberGlowingKrypton": "氪苔藓钓鱼浮标", "FishingBobberGlowingXenon": "氙苔藓钓鱼浮标", "FishingBobberGlowingArgon": "氩苔藓钓鱼浮标", "FishingBobberGlowingViolet": "氖苔藓钓鱼浮标", "FishingBobberGlowingRainbow": "氦苔藓钓鱼浮标", "VulkelfEar": "瓦尔克精灵耳朵", "RepairedLifeCrystal": "修复的生命水晶", "RepairedManaCrystal": "修复的魔力水晶", "TerraFartMinecart": "泰拉放屁车", "JimsCap": "<PERSON>的帽子", "HiveFive": "蜂巢球", "Trimarang": "三尖回旋镖", "MushroomTorch": "蘑菇火把", "MushroomCampfire": "蘑菇篝火", "ScarletMacaw": "绯红金刚鹦鹉", "ScarletMacawCage": "绯红金刚鹦鹉笼", "BlueMacaw": "蓝金刚鹦鹉", "BlueMacawCage": "蓝金刚鹦鹉笼", "EchoWall": "回声墙", "EchoPlatform": "回声平台", "HoneyAbsorbantSponge": "蜂蜜吸收绵", "UltraAbsorbantSponge": "超强吸收绵", "BottomlessHoneyBucket": "无底蜂蜜桶", "ChlorophyteExtractinator": "叶绿提炼机", "BlueEgg": "蓝鸡蛋", "GoblorcEar": "哥布魔耳朵", "ReefBlock": "珊瑚礁块", "ReefWall": "珊瑚礁墙", "DontHurtNatureBook": "环境保护指南", "MinecartPowerup": "矿车升级包", "WolfMountItem": "Lilith的项链", "PrincessStyle": "公主风", "PlacePainting": "r/Terraria", "Toucan": "巨嘴鸟", "ToucanCage": "巨嘴鸟笼", "YellowCockatiel": "黄玄凤鹦鹉", "YellowCockatielCage": "黄玄凤鹦鹉笼", "GrayCockatiel": "灰玄凤鹦鹉", "GrayCockatielCage": "灰玄凤鹦鹉笼", "MacawStatue": "金刚鹦鹉雕像", "ToucanStatue": "巨嘴鸟雕像", "CockatielStatue": "玄凤鹦鹉雕像", "PlaceableHealingPotion": "装饰治疗药水", "PlaceableManaPotion": "装饰魔力药水", "ShadowCandle": "暗影蜡烛", "DontHurtComboBook": "和平共处指南", "AcornAxe": "再生之斧", "ClosedVoidBag": "闭合的虚空袋", "ArtisanLoaf": "工匠面包", "TNTBarrel": "TNT桶", "ChestLock": "宝箱锁", "HorseshoeBundle": "马掌气球束", "SpiffoPlush": "毛绒Spiffo", "GlowTulip": "发光郁金香", "RubblemakerSmall": "堆石器（小）", "RubblemakerMedium": "堆石器（中）", "RubblemakerLarge": "堆石器（大）", "MechdusaSummon": "奥库瑞姆剃刀", "RodOfHarmony": "和谐传送杖", "CombatBookVolumeTwo": "先进战斗技术：卷二", "PeddlersSatchel": "商贩背包", "AegisCrystal": "活力水晶", "AegisFruit": "神盾果", "ArcaneCrystal": "奥术水晶", "GalaxyPearl": "星系珍珠", "GummyWorm": "黏性蠕虫", "Ambrosia": "仙馔密酒", "EchoCoating": "回声涂料", "GasTrap": "毒气机关", "UsedGasTrap": "用过的毒气机关", "EchoMonolith": "回声腔", "ShimmerMonolith": "以太天塔柱", "ShimmerArrow": "微光箭", "ShimmerCloak": "炫彩斗篷", "ShimmerTorch": "以太火把", "ShimmerCampfire": "以太篝火", "Shimmerfly": "飞灵", "ShimmerflyinaBottle": "飞灵瓶", "Shellphone": "贝壳电话（家）", "ShellphoneSpawn": "贝壳电话（出生点）", "ShellphoneOcean": "贝壳电话（海洋）", "ShellphoneHell": "贝壳电话（地狱）", "MusicBoxShimmer": "八音盒（以太）", "ShimmerBlock": "以太块", "ReflectiveShades": "反光墨镜", "SpiderWallUnsafe": "蜘蛛栖息墙", "BottomlessShimmerBucket": "无底微光桶", "BlueBrickWallUnsafe": "诅咒蓝砖墙", "BlueSlabWallUnsafe": "诅咒蓝板墙", "BlueTiledWallUnsafe": "诅咒蓝瓷砖墙", "PinkBrickWallUnsafe": "诅咒粉砖墙", "PinkSlabWallUnsafe": "诅咒粉板墙", "PinkTiledWallUnsafe": "诅咒粉瓷砖墙", "GreenBrickWallUnsafe": "诅咒绿砖墙", "GreenSlabWallUnsafe": "诅咒绿板墙", "GreenTiledWallUnsafe": "诅咒绿瓷砖墙", "Clentaminator2": "泰拉改造枪", "SandstoneWallUnsafe": "危险沙岩墙", "HardenedSandWallUnsafe": "危险硬化沙墙", "LihzahrdWallUnsafe": "禁戒丛林蜥蜴砖墙", "CursedFlare": "诅咒照明弹", "RainbowFlare": "彩虹照明弹", "SpelunkerFlare": "洞穴探险照明弹", "ShimmerFlare": "微光照明弹", "Moondial": "附魔月晷", "ShimmerSlimeBanner": "微光史莱姆旗", "WaffleIron": "华夫饼烘烤模", "BouncyBoulder": "弹力巨石", "LifeCrystalBoulder": "生命水晶巨石", "DizzyHat": "Dizzy的稀有壁虎帽", "UncumberingStone": "非负重石", "ShimmerBrick": "以太砖", "ShimmerWall": "以太墙", "ShimmerBrickWall": "以太砖墙", "LincolnsHoodie": "Raynebro的连帽衫", "LincolnsHood": "Raynebro的兜帽", "LincolnsPants": "Raynebro的裤子", "SandSolution": "黄溶液", "SnowSolution": "白溶液", "DirtSolution": "棕溶液", "LunarRustBrick": "月锈砖", "DarkCelestialBrick": "暗黑天界砖", "AstraBrick": "星芒砖", "CosmicEmberBrick": "宇宙余烬砖", "CryocoreBrick": "冷核砖", "MercuryBrick": "水星砖", "StarRoyaleBrick": "星星皇家砖", "HeavenforgeBrick": "天熔砖", "LunarRustBrickWall": "月锈砖墙", "DarkCelestialBrickWall": "暗黑天界砖墙", "AstraBrickWall": "星芒砖墙", "CosmicEmberBrickWall": "宇宙余烬砖墙", "CryocoreBrickWall": "冷核砖墙", "MercuryBrickWall": "水星砖墙", "StarRoyaleBrickWall": "星星皇家砖墙", "HeavenforgeBrickWall": "天熔砖墙", "DirtiestBlock": "最土的块", "AncientBlueDungeonBrick": "远古蓝砖", "AncientBlueDungeonBrickWall": "远古蓝砖墙", "AncientGreenDungeonBrick": "远古绿砖", "AncientGreenDungeonBrickWall": "远古绿砖墙", "AncientPinkDungeonBrick": "远古粉砖", "AncientPinkDungeonBrickWall": "远古粉砖墙", "AncientGoldBrick": "远古金砖", "AncientGoldBrickWall": "远古金砖墙", "AncientSilverBrick": "远古银砖", "AncientSilverBrickWall": "远古银砖墙", "AncientCopperBrick": "远古铜砖", "AncientCopperBrickWall": "远古铜砖墙", "AncientCobaltBrick": "远古钴砖", "AncientCobaltBrickWall": "远古钴砖墙", "AncientMythrilBrick": "远古秘银砖", "AncientMythrilBrickWall": "远古秘银砖墙", "AncientObsidianBrick": "远古黑曜石砖", "AncientObsidianBrickWall": "远古黑曜石砖墙", "AncientHellstoneBrick": "远古狱石砖", "AncientHellstoneBrickWall": "远古狱石砖墙", "PoopBlock": "臭臭", "PoopWall": "臭臭墙", "ShellphoneDummy": "贝壳电话", "Fertilizer": "肥料", "LavaMossBlock": "熔岩苔藓砖", "ArgonMossBlock": "氩苔藓砖", "KryptonMossBlock": "氪苔藓砖", "XenonMossBlock": "氙苔藓砖", "VioletMossBlock": "氖苔藓砖", "RainbowMossBlock": "氦苔藓砖", "LavaMossBlockWall": "熔岩苔藓砖墙", "ArgonMossBlockWall": "氩苔藓砖墙", "KryptonMossBlockWall": "氪苔藓砖墙", "XenonMossBlockWall": "氙苔藓砖墙", "VioletMossBlockWall": "氖苔藓砖墙", "RainbowMossBlockWall": "氦苔藓砖墙", "SunOrnament": "太阳之眼", "HoplitePizza": "奶酪比萨海报", "JimsDrone": "四轴竞速无人机", "JimsDroneVisor": "FPV飞行眼镜", "DontHurtCrittersBookInactive": "小动物友谊指南（停用）", "DontHurtNatureBookInactive": "环境保护指南（停用）", "DontHurtComboBookInactive": "和平共处指南（停用）"}, "ItemTooltip": {"ShadowGreaves": "暴击率提高5%", "ConfettiGun": "到处喷射彩纸！", "ChlorophyteMask": "近战伤害提高16%\n近战暴击率提高6%", "ChlorophyteHelmet": "远程伤害提高16%\n20%几率省下弹药", "ChlorophyteHeadgear": "最大魔力增加80、魔力消耗降低17%\n魔法伤害提高16%", "ChlorophytePlateMail": "伤害提高5%\n暴击率提高7%", "ChlorophyteGreaves": "暴击率提高8%\n移动速度提高5%", "ChlorophyteBar": "对光有反应", "ShadowScalemail": "暴击率提高5%", "ShadowHelmet": "暴击率提高5%", "NightmarePickaxe": "能够开采狱石", "Paintbrush": "与油漆一起用于给物块涂色\n也可以涂覆涂料", "PaintRoller": "与油漆一起用于给墙涂色\n也可以涂覆涂料", "ManaCrystal": "最大魔力永久增加20", "PaintScraper": "用于去除油漆或涂料\n有时可以收集苔藓", "TealMushroom": "用于制作青绿染料", "GreenMushroom": "用于制作绿染料", "SkyBlueFlower": "用于制作天蓝染料", "BandofStarpower": "最大魔力增加20", "YellowMarigold": "用于制作黄染料", "BlueBerries": "用于制作蓝染料", "LimeKelp": "用于制作橙绿染料", "PinkPricklyPear": "用于制作粉染料", "OrangeBloodroot": "用于制作橙染料", "RedHusk": "用于制作红染料", "CyanHusk": "用于制作青染料", "VioletHusk": "用于制作蓝紫染料", "PurpleMucos": "用于制作紫染料", "BlackInk": "用于制作黑染料", "FlowerofFire": "投掷火球", "DyeVat": "用于制作染料", "BeeGun": "射出会追杀敌人的蜜蜂", "PossessedHatchet": "追杀敌人", "BeeKeeper": "攻击敌人后召唤杀人蜂\n有较小的几率造成困惑效果", "HiveWand": "放置蜂巢", "MagicMissile": "发射一枚可控的飞弹", "Beenade": "爆炸成一大群蜜蜂", "GravityGlobe": "可让持有者反转重力\n按向上键可改变重力", "HoneyComb": "受到伤害后释放蜜蜂并将使用者浸入蜂蜜中", "Abeemination": "召唤蜂王", "DirtRod": "用魔法移动土", "TempleKey": "打开丛林神庙的大门", "LihzahrdWorkBench": "用于基础制作", "ShadowOrb": "创造一颗魔法暗影珠", "LihzahrdPressurePlate": "玩家踩上时激活", "PiranhaGun": "咬定敌人来持续造成伤害", "PygmyStaff": "召唤矮人来为你战斗", "PygmyNecklace": "仆从数量上限增加1", "TikiMask": "仆从数量上限增加1\n召唤伤害提高10%\n鞭子攻击范围扩大10%", "TikiShirt": "仆从数量上限增加1\n召唤伤害提高10%", "TikiPants": "仆从数量上限增加1\n召唤伤害提高10%", "LeafWings": "{$CommonItemTooltip.FlightAndSlowfall}", "BetsyWings": "{$CommonItemTooltip.FlightAndSlowfall}\n{$CommonItemTooltip.PressDownToHover}", "BlizzardinaBalloon": "可让持有者二连跳\n增加跳跃高度", "BundleofBalloons": "可让持有者四连跳\n增加跳跃高度", "BatWings": "{$CommonItemTooltip.FlightAndSlowfall}", "HerculesBeetle": "召唤伤害提高15%\n提高仆从的击退力", "BoneKey": "召唤骷髅王头宝宝", "MeteoriteBar": "“摸起来很温暖”", "Nectar": "召唤黄蜂宝宝", "TikiTotem": "召唤提基幽魂", "LizardEgg": "召唤宠物蜥蜴", "LeafBlower": "快速射出锋利的树叶", "ChlorophyteBullet": "追杀敌人", "Hook": "有时会从骷髅和食人鱼身上掉落", "ParrotCracker": "召唤宠物鹦鹉", "StrangeGlowingMushroom": "召唤松露人宝宝", "Seedling": "召唤宠物树苗", "WispinaBottle": "召唤妖灵来提供照明", "PalladiumPickaxe": "可开采秘银和山铜", "PalladiumDrill": "可开采秘银和山铜", "OrichalcumPickaxe": "可开采精金和钛金", "OrichalcumDrill": "可开采精金和钛金", "MoltenFury": "点燃木箭，火光熊熊", "PalladiumMask": "近战伤害提高12%\n近战速度提高12%", "PalladiumHelmet": "远程伤害提高9%\n远程暴击率提高9%", "PalladiumHeadgear": "魔法伤害和魔法暴击率各提高9%\n最大魔力增加60", "PalladiumBreastplate": "伤害提高3%\n暴击率提高2%", "PalladiumLeggings": "伤害提高2%\n暴击率提高1%", "FieryGreatsword": "“它是火焰做的！”", "OrichalcumMask": "近战伤害和近战速度各提高11%\n移动速度提高7%", "OrichalcumHelmet": "远程暴击率提高15%\n移动速度提高8%", "OrichalcumHeadgear": "魔法暴击率提高18%\n最大魔力增加80", "OrichalcumBreastplate": "暴击率提高6%", "OrichalcumLeggings": "伤害提高8%、移动速度提高11%", "TitaniumMask": "近战伤害和近战暴击率各提高9%\n近战速度提高9%", "TitaniumHelmet": "远程伤害提高16%\n远程暴击率提高7%", "TitaniumHeadgear": "魔法伤害提高16%、魔法暴击率提高7%\n最大魔力增加100", "TitaniumBreastplate": "伤害提高4%\n暴击率提高3%", "TitaniumLeggings": "伤害和暴击率各提高3%\n移动速度提高6%", "OrichalcumAnvil": "用于制作以秘银锭、山铜锭、精金锭和钛金锭为原料的物品", "TitaniumForge": "用于熔炼精金矿和钛金矿", "ChlorophyteClaymore": "射出强大的球珠", "ChlorophyteSaber": "射出孢子云", "ChlorophytePartisan": "射出孢子云", "MeteorHelmet": "魔法伤害提高9%", "ChlorophyteArrow": "撞墙后弹回", "MeteorSuit": "魔法伤害提高9%", "AmberMosquito": "召唤恐龙宝宝", "NimbusRod": "召唤云朵来向敌人降下大雨", "BeeCloak": "受到伤害后会使星星坠落、释放蜜蜂并将使用者浸入蜂蜜中", "EyeoftheGolem": "暴击率提高10%", "HoneyBalloon": "增加跳跃高度\n受到伤害后释放蜜蜂并将使用者浸入蜂蜜中", "MeteorLeggings": "魔法伤害提高9%", "BlueHorseshoeBalloon": "可让持有者二连跳\n增加跳跃高度、消除掉落伤害", "WhiteHorseshoeBalloon": "可让持有者二连跳\n增加跳跃高度、消除掉落伤害", "YellowHorseshoeBalloon": "可让持有者二连跳\n增加跳跃高度、消除掉落伤害", "FrozenTurtleShell": "生命值低于50%时，在持有者周围放置可降低25%伤害的护罩", "SniperRifle": "射出强大的高速子弹\n<right>可拉远视野", "VenusMagnum": "射出强大的高速子弹", "CrimsonRod": "召唤云朵来向敌人降下血雨", "Stynger": "射出爆炸矢\n会对直接命中的目标造成额外伤害", "FlowerPow": "朝附近的敌人射出锋利的花瓣", "RainbowGun": "射出造成连续伤害的彩虹", "StyngerBolt": "爆炸成致命的弹片", "FlowerofFrost": "射出寒霜球", "Uzi": "射出强大的高速子弹", "RocketBoots": "可飞行", "AmethystRobe": "最大魔力增加20\n魔力消耗降低5%", "TopazRobe": "最大魔力增加40\n魔力消耗降低7%", "SapphireRobe": "最大魔力增加40\n魔力消耗降低9%", "EmeraldRobe": "最大魔力增加60\n魔力消耗降低11%", "RubyRobe": "最大魔力增加60\n魔力消耗降低13%", "DiamondRobe": "最大魔力增加80\n魔力消耗降低15%", "PanicNecklace": "受到伤害后提高移动速度", "LifeFruit": "最大生命永久增加5", "LihzahrdPowerCell": "在丛林蜥蜴祭坛上使用", "Picksaw": "可开采丛林蜥蜴砖", "HeatRay": "射出灼热的高温射线\n“喔啦！！”", "StaffofEarth": "召唤强大的巨石", "GolemFist": "以石巨人之力出拳", "Binoculars": "手持时扩大视野范围", "RifleScope": "扩大枪的视野范围\n<right>可拉远视野", "DestroyerEmblem": "伤害提高10%\n暴击率提高8%", "JellyfishNecklace": "发出非常微弱的光芒，这种光芒在水下会变得更醒目", "IceSickle": "射出冰镰刀", "ClothierVoodooDoll": "“你这人真可怕”", "PoisonStaff": "射出能刺穿多个敌人的毒牙", "SlimeStaff": "召唤史莱姆宝宝来为你战斗", "PoisonDart": "对敌人施毒", "EyeSpring": "召唤弹簧眼", "ToySled": "召唤雪人宝宝", "BookofSkulls": "射出骷髅头", "KOCannon": "射出拳击手套", "PirateMap": "召唤海盗入侵", "TurtleHelmet": "近战伤害提高6%\n敌人更有可能以你为目标", "TurtleScaleMail": "近战伤害和近战暴击率各提高8%\n敌人更有可能以你为目标", "TurtleLeggings": "近战暴击率提高4%\n敌人更有可能以你为目标", "MagicQuiver": "箭的伤害提高10%并大大提高箭的速度\n20%几率不消耗箭", "MagmaStone": "近战攻击造成火焰伤害", "ObsidianRose": "降低因触碰熔岩而受到的伤害", "RodofDiscord": "将你传送至鼠标的位置\n导致混沌状态", "DeathSickle": "射出致命镰刀", "BloodySpine": "召唤克苏鲁之脑", "Ichor": "“众神之血”", "IchorTorch": "可放置在水中", "IchorArrow": "降低目标的防御力", "IchorBullet": "降低目标的防御力", "GoldenShower": "喷射一阵灵液\n降低目标的防御力", "ImbuingStation": "用于制作武器灌注药剂", "EmptyBullet": "用于制作各种弹药", "ShadowbeamStaff": "产生会在墙上反弹的暗影光束", "InfernoFork": "发射爆炸成熊熊狱火的火球", "SpectreStaff": "召唤亡魂来追杀敌人", "BubbleMachine": "吹泡泡", "BubbleWand": "吹泡泡", "WaterCandle": "手持此物可能会引起不必要的注意", "Book": "“上面写着奇怪的符号”", "CopperWatch": "报时", "SpectreRobe": "魔法伤害和魔法暴击率各提高7%", "SpectrePants": "魔法伤害提高8%\n移动速度提高8%", "PaladinsHammer": "威力强大的回旋锤", "BeeWings": "{$CommonItemTooltip.FlightAndSlowfall}", "LargeAmethyst": "用于宝石夺取。在你死亡时会掉落", "LargeTopaz": "用于宝石夺取。在你死亡时会掉落", "LargeSapphire": "用于宝石夺取。在你死亡时会掉落", "LargeEmerald": "用于宝石夺取。在你死亡时会掉落", "LargeRuby": "用于宝石夺取。在你死亡时会掉落", "LargeDiamond": "用于宝石夺取。在你死亡时会掉落", "JungleKey": "打开地牢中的丛林箱", "CorruptionKey": "打开地牢中的腐化箱", "CrimsonKey": "打开地牢中的猩红箱", "HallowedKey": "打开地牢中的神圣箱", "FrozenKey": "打开地牢中的冰雪箱", "SpectrePaintbrush": "与油漆一起用于给物块涂色\n也可以涂覆涂料", "SpectrePaintRoller": "与油漆一起用于给墙涂色\n也可以涂覆涂料", "SpectrePaintScraper": "用于去除油漆或涂料\n有时可以收集苔藓", "ShroomiteHeadgear": "弓的伤害提高15%\n远程暴击率提高5%", "ShroomiteMask": "枪的伤害提高15%\n远程暴击率提高5%", "ShroomiteHelmet": "特定远程伤害提高15%\n这指的是发射器、飞镖枪或其他不发射箭/子弹的武器\n远程暴击率提高5%", "ShroomiteBreastplate": "远程伤害和远程暴击率各提高13%\n20%几率省下弹药", "ShroomiteLeggings": "远程暴击率提高7%\n移动速度提高12%", "Autohammer": "将叶绿锭转化成蘑菇矿锭", "SDMG": "66%几率省下弹药\n“它来自太空边缘”", "CenxsTiara": "{$CommonItemTooltip.DevItem}", "CenxsBreastplate": "{$CommonItemTooltip.DevItem}", "CenxsLeggings": "{$CommonItemTooltip.DevItem}", "CrownosMask": "{$CommonItemTooltip.DevItem}", "CrownosBreastplate": "{$CommonItemTooltip.DevItem}", "CrownosLeggings": "{$CommonItemTooltip.DevItem}", "CobaltShield": "对击退免疫", "WillsHelmet": "{$CommonItemTooltip.DevItem}", "WillsBreastplate": "{$CommonItemTooltip.DevItem}", "WillsLeggings": "{$CommonItemTooltip.DevItem}", "JimsHelmet": "{$CommonItemTooltip.DevItem}", "JimsBreastplate": "{$CommonItemTooltip.DevItem}", "JimsLeggings": "{$CommonItemTooltip.DevItem}", "AaronsHelmet": "{$CommonItemTooltip.DevItem}", "AaronsBreastplate": "{$CommonItemTooltip.DevItem}", "AaronsLeggings": "{$CommonItemTooltip.DevItem}", "VampireKnives": "快速投掷出吸血飞刀", "AquaScepter": "喷射一阵水", "ScourgeoftheCorruptor": "能释放小吞噬怪的强大标枪", "StaffoftheFrostHydra": "{$CommonItemTooltip.Sentry}\n召唤强大的寒霜九头蛇来朝敌人喷吐冰雪", "SweetheartNecklace": "受到伤害后释放蜜蜂并将使用者浸入蜂蜜中、并提高移动速度", "FlurryBoots": "穿戴者可飞速奔跑", "LuckyHorseshoe": "消除掉落伤害\n{$ItemTooltip.GardenGnome}", "DTownsHelmet": "{$CommonItemTooltip.DevItem}", "DTownsBreastplate": "{$CommonItemTooltip.DevItem}", "DTownsLeggings": "{$CommonItemTooltip.DevItem}", "DTownsWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}", "WillsWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}", "CrownosWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}", "CenxsWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}", "CenxsDress": "{$CommonItemTooltip.DevItem}", "CenxsDressPants": "{$CommonItemTooltip.DevItem}", "ShinyRedBalloon": "增加跳跃高度", "MagicCuffs": "最大魔力增加20\n受到伤害时会恢复魔力", "SilverWatch": "报时", "AnkhCharm": "对大部分减益免疫", "AnkhShield": "对击退和火块免疫\n对大部分减益免疫", "WaterBolt": "发射移动缓慢的水矢", "Bomb": "造成会摧毁大部分图格的小爆炸", "Dynamite": "造成会摧毁大部分图格的大爆炸", "Grenade": "造成不会摧毁图格的小爆炸", "GoldWatch": "报时", "FartinaJar": "可让持有者二连跳", "HellstoneBar": "“摸起来烫手”", "LeprechaunHat": "在我看来，它就像个矮妖", "LeprechaunShirt": "我只想知道金子在哪儿！", "LeprechaunPants": "我要金子。我要金子。我要金子。给我金子！", "GoodieBag": "{$CommonItemTooltip.RightClickToOpen}", "CandyCornRifle": "33%几率省下弹药", "Sickle": "可从草地收集干草", "TatteredFairyWings": "{$CommonItemTooltip.FlightAndSlowfall}", "SpiderEgg": "召唤宠物蜘蛛", "MagicalPumpkinSeed": "召唤宠物南瓜娃娃", "DepthMeter": "显示深度", "BatScepter": "召唤蝙蝠来攻击敌人", "RavenStaff": "召唤乌鸦来为你战斗", "JungleKeyMold": "用于制作丛林钥匙", "CorruptionKeyMold": "用于制作腐化钥匙", "CrimsonKeyMold": "用于制作猩红钥匙", "HallowedKeyMold": "用于制作神圣钥匙", "FrozenKeyMold": "用于制作冰冻钥匙", "RottenEgg": "最适合用来整蛊城镇居民", "UnluckyYarn": "召唤黑猫", "TheHorsemansBlade": "召唤南瓜头来攻击敌人", "SpookyWings": "{$CommonItemTooltip.FlightAndSlowfall}", "SpookyHelmet": "仆从数量上限增加1\n召唤伤害提高11%", "SpookyBreastplate": "仆从数量上限增加2\n召唤伤害提高11%", "SpookyLeggings": "仆从数量上限增加1\n召唤伤害提高11%\n移动速度提高20%", "CursedSapling": "召唤诅咒树苗来跟着你", "PumpkinMoonMedallion": "召唤南瓜月", "NecromanticScroll": "仆从数量上限增加1\n召唤伤害提高10%", "SniperScope": "扩大枪的视野范围（<right>可拉远视野）\n远程伤害和远程暴击率各提高10%", "BreathingReed": "延长呼吸时间并可在水中呼吸", "JellyfishDivingGear": "给予游泳能力并大大延长水下呼吸时间\n发出非常微弱的光芒，这种光芒在水下会变得更醒目", "ArcticDivingGear": "给予游泳能力并大大延长水下呼吸时间\n提供额外冰面行动力\n发出非常微弱的光芒，这种光芒在水下会变得更醒目", "FrostsparkBoots": "可飞行、飞速奔跑、并提供额外冰面行动力\n移动速度提高8%", "FartInABalloon": "可让持有者二连跳\n增加跳跃高度", "PapyrusScarab": "仆从数量上限增加1\n召唤伤害提高15%、提高仆从的击退力", "CelestialStone": "小幅提高伤害、近战速度、暴击率、\n生命再生、防御力、挖矿速度和仆从击退力", "Hoverboard": "{$CommonItemTooltip.FlightAndSlowfall}\n{$CommonItemTooltip.PressDownToHover}", "Present": "{$CommonItemTooltip.RightClickToOpen}", "Flipper": "给予游泳能力", "RedRyder": "“别把自己的眼球射出来了！”", "FestiveWings": "{$CommonItemTooltip.FlightAndSlowfall}", "BladeofGrass": "有机会让敌人中毒", "ElfMelter": "用凝胶作为弹药\n忽略15点敌人防御", "ReindeerBells": "召唤可骑乘的驯鹿", "CnadyCanePickaxe": "可开采陨石", "HandWarmer": "对寒冷和冰冻效果免疫", "Coal": "“你今年很淘气”", "Toolbox": "物块放置范围和工具范围扩大1格", "DogWhistle": "召唤小狗", "ChristmasTreeSword": "射出圣诞装饰", "ChainGun": "50%几率省下弹药", "ObsidianSkull": "对火块免疫", "Razorpine": "射出锋利的松针", "BlizzardStaff": "在一片区域内洒下冰锥", "SnowmanCannon": "发射自动制导的导弹", "NorthPole": "射出下雪花的冰矛", "NaughtyPresent": "召唤霜月", "BabyGrinchMischiefWhistle": "召唤格林奇宝宝", "StarCannon": "射出坠落之星", "Fez": "“菲斯帽很酷”", "JungleRose": "“美，太美了”", "FeralClaws": "近战速度提高12%\n让近战武器能自动挥舞", "AnkletoftheWind": "移动速度提高10%", "StaffofRegrowth": "让泥土上长出草\n用它进行采集可提高炼金植物的采集量", "WhoopieCushion": "“可能会惹恼别人”", "HeavyWorkBench": "用于高级制作", "AmmoBox": "20%几率省下弹药", "Flamelash": "召唤可控的火球", "VenomStaff": "射出能刺穿多个敌人的毒液尖牙", "SpectreMask": "最大魔力增加60、魔力消耗降低13%\n魔法伤害和魔法暴击率各提高10%", "BoneWelder": "{$CommonItemTooltip.SpecialCrafting}", "FleshCloningVaat": "{$CommonItemTooltip.SpecialCrafting}", "GlassKiln": "{$CommonItemTooltip.SpecialCrafting}", "LihzahrdFurnace": "{$CommonItemTooltip.SpecialCrafting}", "LivingLoom": "{$CommonItemTooltip.SpecialCrafting}", "SkyMill": "{$CommonItemTooltip.SpecialCrafting}", "IceMachine": "{$CommonItemTooltip.SpecialCrafting}", "BeetleHelmet": "近战伤害提高6%\n敌人更有可能以你为目标", "BeetleScaleMail": "近战伤害和近战暴击率各提高8%\n移动速度和近战速度各提高6%", "BeetleShell": "近战伤害和近战暴击率各提高5%\n敌人更有可能以你为目标", "BeetleLeggings": "移动速度和近战速度各提高6%\n敌人更有可能以你为目标", "SteampunkBoiler": "{$CommonItemTooltip.SpecialCrafting}", "HoneyDispenser": "{$CommonItemTooltip.SpecialCrafting}", "BrickLayer": "提高图格放置速度", "ExtendoGrip": "物块放置范围和工具范围扩大3格", "PaintSprayer": "自动给放置的物体刷油漆或涂料", "PortableCementMixer": "提高墙的放置速度", "CelestialMagnet": "扩大魔力星的拾取范围", "ClayPot": "栽种植物", "CelestialEmblem": "扩大魔力星的拾取范围\n魔法伤害提高15%", "CelestialCuffs": "扩大魔力星的拾取范围\n受到伤害时会恢复魔力\n最大魔力增加20", "PulseBow": "射出带电的箭", "NaturesGift": "魔力消耗降低6%", "RestorationPotion": "缩短药水冷却时间", "Gatligator": "50%几率省下弹药\n非常不精准", "WaterGun": "喷射一股无害的水流", "MagicHat": "魔法伤害和魔法暴击率各提高6%", "Gi": "伤害和暴击率各提高5%\n近战速度和移动速度各提高10%", "GypsyRobe": "魔法伤害和魔法暴击率各提高6%\n魔力消耗降低10%", "JungleHat": "最大魔力增加40\n魔法暴击率提高6%", "BeetleWings": "{$CommonItemTooltip.FlightAndSlowfall}", "JungleShirt": "最大魔力增加20\n魔法伤害提高6%", "Gel": "“既好吃，又易燃”", "JunglePants": "最大魔力增加20\n魔法暴击率提高6%", "NeonTetra": "“它的鳞片五光十色，会很好卖。”", "GoldenCarp": "亮闪闪。这大概会很好卖。", "MiningPotion": "挖矿速度提高25%", "HeartreachPotion": "扩大生命心的拾取范围", "CalmingPotion": "降低敌人生成速度", "BuilderPotion": "提高放置速度、扩大放置范围", "TitanPotion": "提高击退力", "FlipperPotion": "让你在液体中快速移动", "SummoningPotion": "仆从数量上限增加1", "TrapsightPotion": "让你看到附近的危险源", "WoodenCrate": "{$CommonItemTooltip.RightClickToOpen}", "IronCrate": "{$CommonItemTooltip.RightClickToOpen}", "GoldenCrate": "{$CommonItemTooltip.RightClickToOpen}", "AmmoReservationPotion": "20%几率省下弹药", "LifeforcePotion": "最大生命提高20%", "EndurancePotion": "所受伤害降低10%", "RagePotion": "暴击率提高10%", "InfernoPotion": "点燃附近的敌人", "WrathPotion": "伤害提高10%", "StickyBomb": "会摧毁大部分图格的小爆炸\n“投出去可不是件易事。”", "RecallPotion": "将你传送回家", "TeleportationPotion": "将你传送至随机位置", "LovePotion": "投掷此物可让人坠入爱河", "StinkPotion": "投掷此物可让人闻起来恶心", "FishingPotion": "渔力增加15", "SonarPotion": "查明上钩的鱼", "CratePotion": "提高获得宝匣的几率", "WarmthPotion": "降低所受冷系伤害", "BeeHeadgear": "召唤伤害提高4%\n仆从数量上限增加1", "BeeBreastplate": "召唤伤害提高4%\n仆从数量上限增加1", "BeeGreaves": "召唤伤害提高5%", "HornetStaff": "召唤黄蜂来为你战斗", "ImpStaff": "召唤小鬼来为你战斗", "AnglerHat": "渔力增加5", "AnglerVest": "渔力增加5", "AnglerPants": "渔力增加5", "Sunglasses": "“让你看起来酷酷的！”", "SpiderMask": "仆从数量上限增加1\n召唤伤害提高5%", "SpiderBreastplate": "仆从数量上限增加1\n召唤伤害提高5%", "SpiderGreaves": "仆从数量上限增加1\n召唤伤害提高6%", "HighTestFishingLine": "钓鱼线永远不会断", "AnglerEarring": "渔力增加10", "TackleBox": "降低鱼饵消耗几率", "WizardHat": "魔法伤害提高5%", "ZephyrFish": "召唤宠物和风鱼", "FrogLeg": "提高跳跃速度并可自动跳跃\n提高掉落抗性", "FinWings": "{$CommonItemTooltip.FlightAndSlowfall}", "OpticStaff": "召唤双子魔眼来为你战斗", "RedHat": "闻起来怪怪的……", "Goldfish": "“它笑眯眯的，应该是个不错的零食”", "Sandgun": "“这主意不错！”", "GuideVoodooDoll": "“你这人真可怕”", "DivingHelmet": "大大延长水下呼吸时间", "DemonScythe": "投掷恶魔之镰", "Blowpipe": "可收集种子作为弹药", "Glowstick": "湿了也能用", "Seed": "搭配吹管使用", "Aglet": "移动速度提高5%", "ObsidianSkinPotion": "对熔岩免疫", "RegenerationPotion": "提供生命再生", "LifeCrystal": "最大生命永久增加20", "SwiftnessPotion": "移动速度提高25%", "GillsPotion": "可在水中而非空气中呼吸", "IronskinPotion": "防御力增加8", "ManaRegenerationPotion": "提高魔力再生速度", "MagicPowerPotion": "魔法伤害提高20%", "FeatherfallPotion": "减缓坠落速度", "SpelunkerPotion": "显示宝藏和矿石的位置", "InvisibilityPotion": "隐身并降低敌人的生成速度", "ShinePotion": "发出光环", "NightOwlPotion": "夜视力提升", "BattlePotion": "提高敌人生成速度", "ThornsPotion": "攻击者也会受到伤害", "WaterWalkingPotion": "可在水上行走", "ArcheryPotion": "弓伤害提高10%，箭速度提高20%", "HunterPotion": "显示敌人位置", "GravitationPotion": "可控制重力", "IllegalGunParts": "“在大多数地方都被禁止”", "GoldenKey": "打开一个锁住的金箱或金锁盒", "ShadowKey": "打开所有暗影箱和黑曜石锁盒", "Furnace": "用于熔炼矿石", "Loom": "用于制作衣物", "IronAnvil": "用于制作以金属锭为原料的物品", "Keg": "用于酿造麦芽酒", "WorkBench": "用于基础制作", "GoblinBattleStandard": "召唤哥布林军队", "Sawmill": "用于高级木工制作", "Pwnhammer": "强大到足以摧毁恶魔祭坛", "CobaltHat": "最大魔力增加40\n魔法伤害提高10%\n魔法暴击率提高9%", "CobaltHelmet": "移动速度提高10%\n近战伤害提高15%", "CobaltMask": "远程伤害提高10%\n远程暴击率提高10%", "MythrilHood": "最大魔力增加60\n魔法伤害提高15%", "MythrilHelmet": "近战暴击率提高8%\n近战伤害提高10%", "MythrilHat": "远程伤害提高12%\n远程暴击率提高7%", "CobaltDrill": "可开采秘银和山铜", "MythrilDrill": "可开采精金和钛金", "DaoofPow": "有机会造成困惑\n“找到你内心的碎片”", "Compass": "显示水平位置", "DivingGear": "给予游泳能力\n大大延长水下呼吸时间", "GPS": "显示位置\n报时", "ObsidianHorseshoe": "消除掉落伤害\n对火块免疫", "ObsidianShield": "对击退免疫\n对火块免疫", "TinkerersWorkshop": "可将一些配饰组合起来", "CloudinaBalloon": "可让持有者二连跳\n增加跳跃高度", "AdamantiteHeadgear": "最大魔力增加80\n魔法伤害和魔法暴击率各提高12%", "AdamantiteHelmet": "近战暴击率提高7%\n近战伤害提高14%", "AdamantiteMask": "远程伤害提高14%\n远程暴击率提高10%", "AdamantiteBreastplate": "伤害提高8%", "AdamantiteLeggings": "暴击率提高7%\n移动速度提高5%", "SpectreBoots": "可飞行\n穿戴者可飞速奔跑", "Toolbelt": "物块放置范围扩大1格", "HolyWater": "让神圣之地蔓延到一些物块上", "UnholyWater": "让腐化之地蔓延到一些物块上", "FairyBell": "召唤魔法仙灵", "SuspiciousLookingEye": "召唤克苏鲁之眼", "ClockworkAssaultRifle": "三发点射\n只有第一发消耗弹药", "MoonCharm": "在晚上将持有者变成狼人", "SorcererEmblem": "魔法伤害提高15%", "BandofRegeneration": "缓慢再生生命", "WarriorEmblem": "近战伤害提高15%", "RangerEmblem": "远程伤害提高15%", "DemonWings": "{$CommonItemTooltip.FlightAndSlowfall}", "AngelWings": "{$CommonItemTooltip.FlightAndSlowfall}", "RainbowRod": "发射可控的彩虹", "IceRod": "召唤一块冰", "NeptunesShell": "入水时将持有者变成人鱼", "MagicMirror": "盯着镜子便可回家", "Flamethrower": "用凝胶作为弹药\n忽略15点敌人防御", "Wrench": "放置红电线", "WireCutter": "移除电线", "CrystalBullet": "在撞击时产生水晶碎块", "HolyArrow": "在撞击时召唤陨星", "MagicDagger": "有魔力的回旋飞刀", "CrystalStorm": "召唤快速发射的水晶碎块", "CursedFlames": "召唤邪恶的火球", "SoulofLight": "“光明生物的精华”", "SoulofNight": "“黑暗生物的精华”", "CursedFlame": "“连水都无法将此火焰浇灭”", "CursedTorch": "可放置在水中", "AdamantiteForge": "用于熔炼精金矿和钛金矿", "MythrilAnvil": "用于制作以秘银锭、山铜锭、精金锭和钛金锭为原料的物品", "UnicornHorn": "“尖锐又有魔力！”", "DarkShard": "“黑暗沙漠中的生物有时会携带”", "LightShard": "“光明沙漠中的生物有时会携带”", "RedPressurePlate": "踩上时激活", "CloudinaBottle": "可让持有者二连跳", "SpellTome": "可附魔", "StarCloak": "受到伤害后会使星星坠落", "Megashark": "50%几率省下弹药\n“迷你鲨的老哥”", "Shotgun": "射出一片子弹", "PhilosophersStone": "治疗药水的冷却时间缩短25%", "HermesBoots": "穿戴者可飞速奔跑", "GreenPressurePlate": "踩上时激活", "GrayPressurePlate": "玩家踩上时激活", "BrownPressurePlate": "玩家踩上时激活", "MechanicalEye": "召唤双子魔眼", "SoulofFright": "“纯粹恐惧的精华”", "SoulofMight": "“毁灭者的精华”", "SoulofSight": "“全知看守者的精华”", "HallowedPlateMail": "暴击率提高7%", "HallowedGreaves": "伤害提高7%\n移动速度提高8%", "HallowedHelmet": "远程伤害提高15%\n远程暴击率提高8%", "CrossNecklace": "延长受伤后的无敌状态时间", "ManaFlower": "魔力消耗降低8%\n需要时自动使用魔力药水", "MechanicalWorm": "召唤毁灭者", "MechanicalSkull": "召唤机械骷髅王", "HallowedHeadgear": "最大魔力增加100\n魔法伤害和魔法暴击率各提高12%", "HallowedMask": "近战伤害和近战暴击率各提高10%\n近战速度提高10%", "DemoniteOre": "“与黑暗能量休戚与共”", "SlimeCrown": "召唤史莱姆王", "LightDisc": "“你好，程序！”", "DemoniteBar": "“与黑暗能量休戚与共”", "SoulofFlight": "“强大飞行生物的精华”", "MusicBox": "有机会录下歌曲", "Drax": "“不要跟锯刃镐混淆”", "Explosives": "激活时爆炸", "InletPump": "从出水泵出水", "OutletPump": "从入水泵进水", "Timer1Second": "每1秒激活一次", "Timer3Second": "每3秒激活一次", "Timer5Second": "每5秒激活一次", "BluePresent": "{$CommonItemTooltip.RightClickToOpen}", "GreenPresent": "{$CommonItemTooltip.RightClickToOpen}", "YellowPresent": "{$CommonItemTooltip.RightClickToOpen}", "SnowGlobe": "召唤雪人军团", "Carrot": "召唤宠物兔兔", "Vilethorn": "召唤魔刺", "Starfury": "使星星如雨般从天而降\n“用天堂怒火锻造而成”", "PurificationPowder": "净化邪恶", "RedsWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}", "RedsHelmet": "{$CommonItemTooltip.DevItem}", "RedsBreastplate": "{$CommonItemTooltip.DevItem}", "RedsLeggings": "{$CommonItemTooltip.DevItem}", "Fish": "召唤企鹅宝宝", "VilePowder": "蔓延腐化之地", "Frostbrand": "射出冰矢", "RedPotion": "“仅献给配得上的人”", "RottenChunk": "“看上去很好吃！”", "UnholyTrident": "召唤恶魔的三叉戟", "FrostHelmet": "近战伤害和远程伤害各提高16%", "FrostBreastplate": "近战暴击率和远程暴击率各提高11%", "FrostLeggings": "移动速度提高8%\n近战速度提高10%", "WormFood": "召唤世界吞噬怪", "TinWatch": "报时", "TungstenWatch": "报时", "PlatinumWatch": "报时", "LeadAnvil": "用于制作以金属锭为原料的物品", "BeamSword": "射出一束光", "IceBlade": "射出冰矢", "IceBow": "射出寒霜箭", "FrostStaff": "射出寒霜流", "Jetpack": "{$CommonItemTooltip.FlightAndSlowfall}\n{$CommonItemTooltip.PressUpToBooster}", "ButterflyWings": "{$CommonItemTooltip.FlightAndSlowfall}", "FallenStar": "日出后消失", "Seaweed": "召唤宠物海龟", "FairyWings": "{$CommonItemTooltip.FlightAndSlowfall}", "Clentaminator": "喷射时生成和摧毁生物群系\n使用彩色溶液", "GreenSolution": "由环境改造枪使用\n蔓延纯净之地", "BlueSolution": "由环境改造枪使用\n蔓延神圣之地", "PurpleSolution": "由环境改造枪使用\n蔓延腐化之地", "DarkBlueSolution": "由环境改造枪使用\n蔓延发光蘑菇地", "RedSolution": "由环境改造枪使用\n蔓延猩红之地", "HarpyWings": "{$CommonItemTooltip.FlightAndSlowfall}", "BoneWings": "{$CommonItemTooltip.FlightAndSlowfall}", "Hammush": "强大到足以摧毁恶魔祭坛", "NettleBurst": "忽略10点敌人防御", "CrimsonHelmet": "伤害提高3%", "CrimsonScalemail": "伤害提高3%", "CrimsonGreaves": "伤害提高3%", "DeathbringerPickaxe": "能够开采狱石", "Torch": "提供照明", "FlameWings": "{$CommonItemTooltip.FlightAndSlowfall}", "FrozenWings": "{$CommonItemTooltip.FlightAndSlowfall}", "GhostWings": "{$CommonItemTooltip.FlightAndSlowfall}", "LivingWoodWand": "放置生命木", "GrapplingHook": "“给我过来！”", "Actuator": "使实心块能够切换虚化状态", "Chain": "可供攀爬", "BlueWrench": "放置蓝电线", "GreenWrench": "放置绿电线", "BluePressurePlate": "玩家踩上时激活", "YellowPressurePlate": "除玩家以外的任何东西踩上时激活", "DiscountCard": "商店价格降低20%", "LuckyCoin": "击中敌人有时会掉落额外的钱币\n“我捡到一枚钱币！今天真幸运！”", "UnicornonaStick": "“玩得开心！”", "SandstorminaBottle": "可让持有者更好地二连跳", "CharmofMyths": "提供生命再生并使治疗药水的冷却时间缩短25%", "MoonShell": "在晚上将持有者变成狼人，入水时将持有者变成人鱼", "StarVeil": "受到伤害后会使星星坠落并延长受伤后的无敌状态时间", "WaterWalkingBoots": "可让人在水和蜂蜜上行走", "MiningHelmet": "穿戴时可提供照明", "AdhesiveBandage": "对流血免疫", "ArmorPolish": "对破损盔甲免疫", "Bezoar": "对中毒免疫", "Blindfold": "对黑暗免疫", "FastClock": "对减缓免疫", "Megaphone": "对沉默免疫", "Nazar": "对诅咒免疫", "Vitamins": "对虚弱免疫", "TrifoldMap": "对困惑免疫", "LightningBoots": "可飞行、可飞速奔跑\n移动速度提高8%", "SunStone": "白天时，小幅提高伤害、近战速度、暴击率、\n生命再生、防御力、挖矿速度和仆从击退力", "MoonStone": "夜晚时，小幅提高伤害、近战速度、暴击率、\n生命再生、防御力、挖矿速度和仆从击退力", "ArmorBracing": "对虚弱和破损盔甲免疫", "MedicatedBandage": "对中毒和流血免疫", "ThePlan": "对缓慢和困惑免疫", "CountercurseMantra": "对沉默和诅咒免疫", "CoinGun": "用钱币当弹药\n钱币价值越高伤害越大", "LavaCharm": "7秒内对熔岩免疫", "ObsidianWaterWalkingBoots": "可让人在水和蜂蜜上行走\n对火块免疫", "LavaWaders": "可让人在水、蜂蜜和熔岩上行走\n对火块免疫并在7秒内对熔岩免疫\n减少因触碰熔岩而受到的伤害", "BoneWand": "放置骨头", "LeafWand": "放置树叶", "FlyingCarpet": "可让持有者漂浮几秒钟", "AvengerEmblem": "伤害提高12%", "LandMine": "踩上时爆炸", "PaladinsShield": "当生命值高于25%时，吸收团队中其他玩家所受伤害的25%\n对击退免疫", "Umbrella": "持有此物可减缓掉落速度", "ChlorophyteOre": "“对光有反应”", "SteampunkWings": "{$CommonItemTooltip.FlightAndSlowfall}", "IceSkates": "提供额外冰面行动力\n落到冰上时冰不会碎", "SnowballLauncher": "迅速发射雪球", "ClimbingClaws": "可沿墙滑下\n结合鞋钉使用时能力还会有所提升", "AncientShadowHelmet": "暴击率提高5%", "AncientShadowScalemail": "暴击率提高5%", "AncientShadowGreaves": "暴击率提高5%", "AncientCobaltHelmet": "最大魔力增加40\n魔法暴击率提高6%", "AncientCobaltBreastplate": "最大魔力增加20\n魔法伤害提高6%", "AncientCobaltLeggings": "最大魔力增加20\n魔法暴击率提高6%", "BlackBelt": "有机会闪避攻击", "Boomstick": "射出一片子弹", "Rope": "可供攀爬", "Campfire": "靠近篝火时生命再生提速", "Marshmallow": "把它放在棍子上，然后放到篝火上烤\n{$CommonItemTooltip.MinorStats}\n“你一口能吃下多少？”", "MarshmallowonaStick": "放在篝火上烤！", "ShoeSpikes": "可沿墙滑下\n结合攀爬爪使用时能力还会有所提升", "TigerClimbingGear": "可爬墙", "Tabi": "可猛冲\n双击一个方向", "Minishark": "33%几率省下弹药\n“半鲨，半枪，帅呆了。”", "ManaRegenerationBand": "最大魔力增加20\n提高魔力再生速度", "SandstorminaBalloon": "可让持有者二连跳\n增加跳跃高度", "MasterNinjaGear": "可爬墙和猛冲\n有机会闪避攻击", "RopeCoil": "抛出以形成可攀爬的绳索", "Blowgun": "可收集种子作为弹药", "BlizzardinaBottle": "可让持有者二连跳", "EnchantedSword": "射出附魔剑光束", "PickaxeAxe": "“不要与锤钻混淆”", "EatersBone": "召唤噬魂怪宝宝", "BlendOMatic": "用于制作物件", "MeatGrinder": "用于制作物件", "Extractinator": "将泥沙/雪泥/化石堆放到提炼机中可将其变成更有用的东西", "Solidifier": "用于制作物件", "ActuationAccessory": "自动将致动器放在放置的物体上", "ActuationRod": "激活致动器", "AlchemyTable": "33%几率不消耗药水的制作材料", "AncientBattleArmorHat": "魔法伤害和召唤伤害各提高15%", "AncientBattleArmorShirt": "最大魔力增加40\n召唤伤害提高10%\n仆从数量上限增加1", "AncientBattleArmorPants": "最大魔力增加40\n魔法伤害提高10%\n仆从数量上限增加1", "AncientHorn": "召唤可骑乘的蛇蜥怪坐骑", "AnglerTackleBag": "钓鱼线永远不会断，降低鱼饵消耗几率，渔力增加10", "ArchitectGizmoPack": "提高物块和墙的放置速度\n物块放置范围和工具范围扩大3格\n自动给放置的物体上漆或涂料", "AviatorSunglasses": "激发你内心的飞人\n“非常适合冒充游戏主播！”", "BalloonHorseshoeFart": "可让持有者二连跳\n增加跳跃高度、消除掉落伤害", "BalloonHorseshoeHoney": "受到伤害后释放蜜蜂并将使用者浸入蜂蜜中\n增加跳跃高度，消除掉落伤害", "BalloonHorseshoeSharkron": "可让持有者二连跳\n增加跳跃高度、消除掉落伤害", "BalloonPufferfish": "增加跳跃高度", "BeesKnees": "木箭变成一队蜜蜂", "BejeweledValkyrieBody": "{$CommonItemTooltip.DevItem}\n珠光宝气、优雅非凡地在雷鸣的天空中翱翔", "BejeweledValkyrieHead": "{$CommonItemTooltip.DevItem}\n变成风，驾驭闪电。", "BejeweledValkyrieWing": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}\n女武神卫星屏障台是完全安全的。大多数时候是。", "BewitchingTable": "<right>可拥有更多仆从", "Bladetongue": "接触时喷出一股灵液", "BlessedApple": "召唤可骑乘的独角兽坐骑", "BloodWater": "让猩红之地蔓延到一些物块上", "BombFish": "造成会摧毁大部分图格的小爆炸", "BoneCampfire": "靠近篝火时生命再生提速", "BoneRattle": "召唤脸怪宝宝", "BoneTorch": "“发出致命光芒”", "BoosterTrack": "锤击可改变方向", "BottomlessBucket": "盛有无限多水\n可以倒出", "BouncyBomb": "会摧毁大部分图格的小爆炸\n非常有弹性", "BouncyDynamite": "会摧毁大部分图格的大爆炸\n“这必将是一个可怕的想法”", "BouncyGlowstick": "湿了也能用", "BouncyGrenade": "造成不会摧毁图格的小爆炸\n非常有弹性", "BrainOfConfusion": "有机会制造幻象并闪避攻击\n闪避后暂时提高暴击率\n可使附近被击中的敌人陷入困惑", "BrainScrambler": "召唤可骑乘的鳞甲怪坐骑", "BubbleGun": "快速射出强劲的泡泡", "ButchersChainsaw": "被击中的敌人发出火花", "CelestialShell": "在晚上将持有者变成狼人，入水时将持有者变成人鱼\n小幅提高伤害、近战速度、暴击率、\n生命再生、防御力、挖矿速度和仆从击退力", "CelestialSigil": "召唤即将来临的末日", "CellPhone": "显示所有信息\n可随意回家", "ClingerStaff": "召唤诅咒焰墙", "CogWall": "生产力提高200%", "CoinRing": "扩大钱币拾取范围\n击中敌人有时会掉落额外的钱币", "CompanionCube": "易受熔岩伤害！", "CordageGuide": "可从藤蔓收集藤蔓绳", "CorruptFishingCrate": "{$CommonItemTooltip.RightClickToOpen}", "CosmicCarKey": "召唤可骑乘的UFO坐骑", "CrimsonFishingCrate": "{$CommonItemTooltip.RightClickToOpen}", "CrimsonHeart": "召唤一颗心来提供照明", "CrystalSerpent": "射出爆炸水晶电荷", "CrystalVileShard": "召唤巨大的水晶尖刺\n忽略10点敌人防御", "CursedCampfire": "靠近篝火时生命再生提速", "DaedalusStormbow": "从天上射下箭来", "DayBreak": "“用光明之矛将敌人四分五裂！”", "DemonCampfire": "靠近篝火时生命再生提速", "DemonHeart": "永久增加配饰栏数量", "Detonator": "“开膛破肚……血肉模糊！”", "DevDye": "{$CommonItemTooltip.DevItem}", "DjinnsCurse": "牺牲双脚换来了缓慢掉落效果", "DPSMeter": "显示你的每秒伤害", "DrillContainmentUnit": "召唤可骑乘的钻头坐骑\n<left>挖物块、<right>挖墙", "DungeonFishingCrate": "{$CommonItemTooltip.RightClickToOpen}", "EoCShield": "允许玩家猛冲向敌人\n双击一个方向", "FishermansGuide": "显示钓鱼信息", "FishFinder": "显示天气、月相和钓鱼信息", "FishronWings": "{$CommonItemTooltip.FlightAndSlowfall}\n允许在水中快速行进", "Flairon": "喷出自动制导的泡泡", "FleshKnuckles": "敌人更有可能以你为目标", "FloatingIslandFishingCrate": "{$CommonItemTooltip.RightClickToOpen}", "FlowerBoots": "你走过的草地上会长出花朵", "FlyingKnife": "投掷可控的飞刀", "FragmentNebula": "“星系之力驻留于此碎片内”", "FragmentSolar": "“宇宙之怒存在于此碎片内”", "FragmentStardust": "“让人目眩神迷的粒子围绕此碎片旋转”", "FragmentVortex": "“旋涡能量由此碎片散发而出”", "FrozenCampfire": "靠近篝火时生命再生提速", "FuzzyCarrot": "召唤可骑乘的兔兔坐骑", "GemLockAmber": "<right>可放置或移除大琥珀", "GemLockAmethyst": "<right>可放置或移除大紫晶", "GemLockDiamond": "<right>可放置或移除大钻石", "GemLockEmerald": "<right>可放置或移除大翡翠", "GemLockRuby": "<right>可放置或移除大红玉", "GemLockSapphire": "<right>可放置或移除大蓝玉", "GemLockTopaz": "<right>可放置或移除大黄玉", "GoblinTech": "显示移动速度、每秒伤害和贵重矿石", "GoldPickaxe": "可开采陨石", "GoldRing": "扩大钱币拾取范围", "GreedyRing": "扩大钱币拾取范围，商店价格降低20%\n击中敌人有时会掉落额外的钱币", "HallowedFishingCrate": "{$CommonItemTooltip.RightClickToOpen}", "HardySaddle": "召唤可骑乘的海龟坐骑", "HellwingBow": "木箭变成烈焰蝙蝠", "HerbBag": "{$CommonItemTooltip.RightClickToOpen}", "HiveBackpack": "增加友好蜜蜂的力量", "HoneyedGoggles": "召唤可骑乘的蜜蜂坐骑", "IceMirror": "盯着镜子便可回家", "IchorCampfire": "靠近篝火时生命再生提速", "JimsWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}", "JungleFishingCrate": "{$CommonItemTooltip.RightClickToOpen}", "LargeAmber": "用于宝石夺取。在你死亡时会掉落", "LaserRuler": "在屏幕上为物块放置建立测量线", "LastPrism": "“发射生命解离彩虹”", "LifeformAnalyzer": "显示你周围稀有生物的名称", "LightKey": "“汇聚了大量灵魂的精华”", "LivingMahoganyLeafWand": "放置红木树叶", "LivingMahoganyWand": "放置生命红木", "LockBox": "{$CommonItemTooltip.RightClickToOpen}\n需要金钥匙", "LogicGateLamp_Faulty": "将此物放置在逻辑门灯上来让激活随机化", "LogicGateLamp_Off": "将此物放置在逻辑门上来添加判定", "LogicGateLamp_On": "将此物放置在逻辑门上来添加判定", "LogicGate_NOR": "判断它上面的逻辑门灯\n没有灯亮时激活，其他情况下停用", "LogicGate_NXOR": "判断它上面的逻辑门灯\n亮灯的总数不为一盏时激活，其他情况下停用\n也常称为“同或门（NXOR）”", "LogicGate_OR": "判断它上面的逻辑门灯\n有灯亮时激活，其他情况下停用", "LogicGate_XOR": "判断它上面的逻辑门灯\n仅有一盏灯亮时激活，其他情况下停用", "LogicSensor_Above": "当玩家经过时激活，其他情况下停用", "LogicSensor_Honey": "上面有蜂蜜时激活，其他情况下停用", "LogicSensor_Lava": "上面有熔岩时激活，其他情况下停用", "LogicSensor_Liquid": "上面有液体时激活，其他情况下停用", "LogicSensor_Moon": "夜幕降临时激活", "LogicSensor_Sun": "黎明破晓时激活", "LogicSensor_Water": "上面有水时激活，其他情况下停用", "LokisDye": "{$CommonItemTooltip.DevItem}", "LokisHelm": "{$CommonItemTooltip.DevItem}\n乱生于治，怯生于勇，弱生于强", "LokisPants": "{$CommonItemTooltip.DevItem}\n天网恢恢，疏而不漏。", "LokisShirt": "{$CommonItemTooltip.DevItem}\n知彼知己，百战不殆。", "LokisWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}\n难知如阴，动如雷震。", "LunarBar": "“它汇聚了闪耀的天界能量”", "LunarCraftingStation": "用于制作以月亮碎片和夜明矿为原料的物品", "LunarFlareBook": "倾泻月耀球", "LunarHook": "“想要月亮？勾住它然后拉下来！”", "LunarOre": "“天堂卵石”", "MagicLantern": "召唤可显露附近宝藏的魔法灯笼", "MechanicalLens": "给予改良的布线视野", "MetalDetector": "显示你周围最贵重的矿石", "MeteorStaff": "降下流星雨", "Minecart": "让我们在铁轨上飞驰", "MinecartTrack": "锤击轨道尾片可改变缓冲方式\n锤击交叉点可改变方向", "MolotovCocktail": "让敌人着火的小爆炸\n火烧附近区域一会儿", "MoneyTrough": "召唤飞猪存钱罐，存放你的物品", "MoonlordArrow": "“以音速射下它们！”", "MoonlordBullet": "“鬼怪排成行，一下全扫荡……”", "MoonlordTurretStaff": "{$CommonItemTooltip.Sentry}\n召唤月亮传送门，向敌人发射激光", "MothronWings": "{$CommonItemTooltip.FlightAndSlowfall}", "MulticolorWrench": "手持时<right>来编辑布线设置", "NebulaArcanum": "“召唤大量星体能量来追杀敌人”", "NebulaBlaze": "“从猎户座腰带收到掌中”", "NebulaBreastplate": "魔法伤害和魔法暴击率各提高9%", "NebulaHelmet": "最大魔力增加60、魔力消耗降低15%\n魔法伤害和魔法暴击率各提高7%", "NebulaLeggings": "魔法伤害提高10%\n移动速度提高10%", "NebulaMonolith": "“动用少量星云柱之力”", "NightKey": "“汇聚了大量灵魂的精华”", "NightVisionHelmet": "改善视野", "PartyBundleOfBalloonTile": "“系起来让大家都开心”", "PartyGirlGrenade": "造成不会摧毁图格的小爆炸", "PartyMonolith": "“气球将如雨般从天而降”", "PartyPresent": "想知道里面有什么？", "PDA": "显示所有信息", "PeaceCandle": "降低周围生物的敌意", "PedguinHat": "变成Pedguin\n“非常适合冒充游戏主播！”", "PedguinPants": "变成Pedguin\n“非常适合冒充游戏主播！”", "PedguinShirt": "变成Pedguin\n“非常适合冒充游戏主播！”", "Phantasm": "66%几率省下弹药", "Pigronata": "打出狂欢派对！\n可能会有惊喜！", "PinkGel": "“弹弹的，多可爱！”", "PinkSlimeBlock": "非常有弹性", "PirateStaff": "召唤海盗来为你战斗", "PixelBox": "分离电线路径\n用同时在交叉方向上出现的信号来开关灯", "PlatinumPickaxe": "可开采陨石", "PocketMirror": "对石化免疫", "PressureTrack": "不能在斜坡上使用", "ProjectilePressurePad": "射弹触碰到它时激活", "PsychoKnife": "可进入隐身模式", "PutridScent": "敌人不太可能以你为目标\n伤害和暴击率各提高5%", "QueenSpiderStaff": "{$CommonItemTooltip.Sentry}\n召唤蜘蛛女王来朝敌人吐蜘蛛卵", "Radar": "探测你周围的敌人", "RainbowCampfire": "靠近篝火时生命再生提速", "RainbowCrystalStaff": "{$CommonItemTooltip.Sentry}\n召唤用来驱逐敌人的放射水晶\n“公爵，多么缤纷的颜色！”", "RazorbladeTyphoon": "发射快速移动的刀轮", "RedsYoyo": "{$CommonItemTooltip.DevItem}", "REK": "显示怪物数量、杀敌数和稀有生物", "RoyalGel": "史莱姆变得友好", "SailfishBoots": "穿戴者可飞速奔跑", "SandFallBlock": "你可以安全观看的落沙", "SandFallWall": "你可以安全观看的落沙", "ScalyTruffle": "召唤可骑乘的猪龙坐骑", "Sextant": "显示月相", "ShadowFlameBow": "射出暗影焰箭", "ShadowFlameHexDoll": "召唤暗影焰触手来攻击敌人", "ShadowFlameKnife": "击中时造成暗影焰", "SharkronBalloon": "增加跳跃高度\n可让持有者二连跳", "SharkToothNecklace": "盔甲穿透力增加5", "SharpeningStation": "提高近战武器的盔甲穿透力", "ShinyStone": "不移动时，大大提高生命再生速度", "ShrimpyTruffle": "吸引一种在水中和战斗中都十分活跃的传奇生物", "SilkRope": "可供攀爬", "SilkRopeCoil": "抛出以形成可攀爬的丝绸绳", "SillyBalloonGreen": "“闻起来像薄荷，象征着欢乐”", "SillyBalloonGreenWall": "“闻起来像薄荷，象征着欢乐”", "SillyBalloonMachine": "它永远不会停止庆祝！", "SillyBalloonPink": "“闻起来像泡泡糖，象征着幸福”", "SillyBalloonPinkWall": "“闻起来像泡泡糖，象征着幸福”", "SillyBalloonPurple": "“闻起来像薰衣草，象征着热情”", "SillyBalloonPurpleWall": "“闻起来像薰衣草，象征着热情”", "SillyStreamerBlue": "异常耐用，足以用来攀爬！", "SillyStreamerGreen": "异常耐用，足以用来攀爬！", "SillyStreamerPink": "异常耐用，足以用来攀爬！", "SkiphsHelm": "{$CommonItemTooltip.DevItem}", "SkiphsPants": "{$CommonItemTooltip.DevItem}", "SkiphsShirt": "{$CommonItemTooltip.DevItem}", "SkiphsWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}", "SliceOfCake": "“抹你脸上。抹别人脸上。管它呢。”", "SlimeGun": "喷射一股无害的黏液", "SlimySaddle": "召唤可骑乘的史莱姆坐骑", "SnowFallBlock": "比水晶雪球酷多了", "SnowFallWall": "比水晶雪球酷多了", "SolarEruption": "“带着太阳之怒猛击”", "SolarFlareBreastplate": "近战伤害提高29%\n可小幅提高生命再生速度\n敌人更有可能以你为目标", "SolarFlareHelmet": "近战暴击率提高26%\n可小幅提高生命再生速度\n敌人更有可能以你为目标", "SolarFlareLeggings": "移动速度和近战速度各提高15%\n可小幅提高生命再生速度\n敌人更有可能以你为目标", "SolarMonolith": "“动用少量日耀柱之力”", "SolarTablet": "召唤日食", "SoulDrain": "吸取敌人的生命", "SpelunkerGlowstick": "显露附近的宝藏", "SpiderStaff": "召唤蜘蛛来为你战斗", "SporeSac": "随着时间推移召唤可以伤害敌人的孢子", "StardustBreastplate": "仆从数量上限增加2\n召唤伤害提高22%\n鞭子攻击范围扩大15%", "StardustCellStaff": "召唤星尘细胞来为你战斗\n“培养最美丽的细胞感染”", "StardustDragonStaff": "召唤星尘之龙来为你战斗\n“有了一条巨龙后，谁还需要一群仆从呢？”", "StardustHelmet": "仆从数量上限增加1\n哨兵数量上限增加1\n召唤伤害提高22%", "StardustLeggings": "仆从数量上限增加2\n召唤伤害提高22%\n鞭子攻击范围扩大15%", "StardustMonolith": "“动用少量星尘柱之力”", "StickyDynamite": "会摧毁大部分图格的大爆炸\n“投出去可不是件易事。”", "StickyGrenade": "不会破坏图格的小炸弹\n“投出去可不是件易事。”", "Stopwatch": "显示玩家移动的速度", "StrangeBrew": "副作用可能包括：\n-不可预测的治疗\n-不确定的耐药性\n-短暂且莫名其妙的伤害免疫\n“难看且难闻”", "StrangePlant1": "可用来换取稀有染料", "StrangePlant2": "可用来换取稀有染料", "StrangePlant3": "可用来换取稀有染料", "StrangePlant4": "可用来换取稀有染料", "SummonerEmblem": "召唤伤害提高15%", "Sundial": "每周允许将时间快进到黎明一次", "SuperAbsorbantSponge": "能够吸收无限多水", "SuspiciousLookingTentacle": "召唤可疑眼球来提供照明\n“我知道你在想什么……”", "TallyCounter": "显示击杀的怪物数量", "TartarSauce": "召唤迷你牛头怪", "TempestStaff": "召唤鲨鱼旋风来为你战斗", "TheBrideDress": "“妢礼……”", "TheBrideHat": "“耐……真耐……”", "Toxikarp": "吐出毒泡", "Tsunami": "一次射出5支箭", "TsunamiInABottle": "可让持有者二连跳", "TungstenPickaxe": "可开采陨石", "UltraBrightCampfire": "靠近篝火时生命再生提速", "ValkyrieYoyo": "{$CommonItemTooltip.DevItem}", "ViciousPowder": "蔓延猩红之地", "VineRope": "可供攀爬", "VineRopeCoil": "抛出以形成可攀爬的藤蔓绳", "VortexBeater": "66%几率省下弹药\n“劈劈啪啪，乒乒乓乓，一顿混乱，惨绝人寰。”", "VortexBreastplate": "远程伤害和远程暴击率各提高12%\n25%几率省下弹药", "VortexHelmet": "远程伤害提高16%\n远程暴击率提高7%", "VortexLeggings": "远程伤害和远程暴击率各提高8%\n移动速度提高10%", "VortexMonolith": "“动用少量星旋柱之力”", "WandofSparking": "射出小火花", "WeaponRack": "<right>可将物品放置在武器架上", "WeatherRadio": "显示天气", "WebRope": "可供攀爬", "WebRopeCoil": "抛出以形成可攀爬的蛛丝绳", "WeightedPressurePlateCyan": "玩家上去或下来时激活", "WeightedPressurePlateOrange": "玩家上去或下来时激活", "WeightedPressurePlatePink": "玩家上去或下来时激活", "WeightedPressurePlatePurple": "玩家上去或下来时激活", "WingsNebula": "{$CommonItemTooltip.FlightAndSlowfall}\n{$CommonItemTooltip.PressDownToHover}", "WingsSolar": "{$CommonItemTooltip.FlightAndSlowfall}", "WingsStardust": "{$CommonItemTooltip.FlightAndSlowfall}", "WingsVortex": "{$CommonItemTooltip.FlightAndSlowfall}\n{$CommonItemTooltip.PressDownToHover}", "WireBulb": "点亮每种电线颜色对应的灯泡", "WireKite": "允许对线路进行终极控制！\n手持时<right>来编辑电线设置", "WirePipe": "分离电线路径\n亦可锤击！", "WormholePotion": "将你传送至队友处\n在全屏地图上点击他们的头像", "WormScarf": "所受伤害降低17%", "XenoStaff": "召唤UFO来为你战斗", "YellowWrench": "放置黄电线", "Yoraiz0rDarkness": "{$CommonItemTooltip.DevItem}\n看到这个你该逃跑了……", "Yoraiz0rHead": "{$CommonItemTooltip.DevItem}", "Yoraiz0rPants": "{$CommonItemTooltip.DevItem}", "Yoraiz0rShirt": "{$CommonItemTooltip.DevItem}", "Yoraiz0rWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}\n无论此配饰对你产生什么作用都不是Bug！", "YoyoBag": "让使用者掌握悠悠球技巧", "YoYoGlove": "可同时使用两个悠悠球", "BloodMoonRising": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "TheHangedMan": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "GloryoftheFire": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "BoneWarp": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "SkellingtonJSkellingsworth": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "TheCursedMan": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "TheEyeSeestheEnd": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "SomethingEvilisWatchingYou": "{$PaintingArtist.<PERSON>}", "TheTwinsHaveAwoken": "{$PaintingArtist.<PERSON>}", "TheScreamer": "{$PaintingArtist.<PERSON>}", "GoblinsPlayingPoker": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "Dryadisque": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "Sunflowers": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "TerrarianGothic": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "Impact": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "PoweredbyBirds": "{$PaintingArtist.Ness}", "TheDestroyer": "{$PaintingArtist.<PERSON>}", "ThePersistencyofEyes": "{$PaintingArtist.<PERSON>}", "UnicornCrossingtheHallows": "{$PaintingArtist.<PERSON>}", "GreatWave": "{$PaintingArtist.<PERSON>}", "StarryNight": "{$PaintingArtist.<PERSON>}", "GuidePicasso": "{$PaintingArtist.<PERSON>}", "TheGuardiansGaze": "{$Painting<PERSON>rt<PERSON>.<PERSON>}", "FatherofSomeone": "{$PaintingArtist.<PERSON>}", "NurseLisa": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "DarkSoulReaper": "{$PaintingArt<PERSON>.<PERSON>}", "Land": "{$PaintingArt<PERSON>.<PERSON>}", "TrappedGhost": "{$PaintingArt<PERSON>.<PERSON>}", "DemonsEye": "{$PaintingArt<PERSON>.<PERSON>}", "FindingGold": "{$PaintingArt<PERSON>.<PERSON>}", "FirstEncounter": "{$PaintingArtist.<PERSON>}", "GoodMorning": "{$PaintingArtist.<PERSON>}", "UndergroundReward": "{$PaintingArtist.<PERSON>}", "ThroughtheWindow": "{$PaintingArtist.<PERSON>}", "PlaceAbovetheClouds": "{$PaintingArtist.<PERSON>}", "DoNotStepontheGrass": "{$PaintingArtist.<PERSON>}", "ColdWatersintheWhiteLand": "{$PaintingArtist.<PERSON>}", "LightlessChasms": "{$PaintingArtist.<PERSON>}", "TheLandofDeceivingLooks": "{$PaintingArtist.<PERSON>}", "Daylight": "{$PaintingArtist.<PERSON>}", "SecretoftheSands": "{$PaintingArtist.<PERSON>}", "DeadlandComesAlive": "{$PaintingArtist.<PERSON>}", "EvilPresence": "{$PaintingArtist.<PERSON>}", "SkyGuardian": "{$PaintingArtist.<PERSON>}", "AmericanExplosive": "{$Painting<PERSON>rt<PERSON>.<PERSON>}", "Discover": "{$PaintingArt<PERSON>.<PERSON>}", "HandEarth": "{$PaintingArt<PERSON>.<PERSON>}", "OldMiner": "{$PaintingArt<PERSON>.<PERSON>}", "Skelehead": "{$PaintingArt<PERSON>.<PERSON>}", "FacingtheCerebralMastermind": "{$Painting<PERSON>rt<PERSON>.<PERSON>}", "LakeofFire": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "TrioSuperHeroes": "{$PaintingArt<PERSON><PERSON>}", "ImpFace": "{$PaintingArt<PERSON>.<PERSON>}", "OminousPresence": "{$<PERSON><PERSON>rt<PERSON>.<PERSON>}", "ShiningMoon": "{$<PERSON><PERSON>rt<PERSON>.<PERSON>}", "LivingGore": "{$<PERSON><PERSON>rt<PERSON>.<PERSON>}", "FlowingMagma": "{$<PERSON><PERSON>rt<PERSON>.<PERSON>}", "TheCreationoftheGuide": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "TheMerchant": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "CrownoDevoursHisLunch": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "RareEnchantment": "{$<PERSON><PERSON><PERSON><PERSON><PERSON>}", "GloriousNight": "{$<PERSON><PERSON>rt<PERSON>.<PERSON>}", "AnglerFishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.AnglerFish}", "AngryNimbusBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.AngryNimbus}", "AnomuraFungusBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.AnomuraFungus}", "AntlionBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Antlion}", "ArapaimaBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Arapaima}", "ArmoredSkeletonBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ArmoredSkeleton}", "BatBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CaveBat}", "BirdBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Bird}", "BlackRecluseBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BlackRecluse}", "BloodFeederBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BloodFeeder}", "BloodJellyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BloodJelly}", "BloodCrawlerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BloodCrawler}", "BoneSerpentBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BoneSerpentHead}", "BunnyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Bunny}", "ChaosElementalBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ChaosElemental}", "MimicBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Mimic}", "ClownBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Clown}", "CorruptBunnyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CorruptBunny}", "CorruptGoldfishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CorruptGoldfish}", "CrabBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Crab}", "CrimeraBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Crimera}", "CrimsonAxeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CrimsonAxe}", "CursedHammerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CursedHammer}", "DemonBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Demon}", "DemonEyeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DemonEye}", "DerplingBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Derpling}", "EaterofSoulsBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.EaterofSouls}", "EnchantedSwordBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.EnchantedSword}", "ZombieEskimoBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ZombieEskimo}", "FaceMonsterBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.FaceMonster}", "FloatyGrossBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.FloatyGross}", "FlyingFishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.FlyingFish}", "FlyingSnakeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.FlyingSnake}", "FrankensteinBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Frankenstein}", "FungiBulbBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.FungiBulb}", "FungoFishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.FungoFish}", "GastropodBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Gastropod}", "GoblinThiefBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GoblinThief}", "GoblinSorcererBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GoblinSorcerer}", "GoblinPeonBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GoblinPeon}", "GoblinScoutBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GoblinScout}", "GoblinWarriorBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GoblinWarrior}", "GoldfishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Goldfish}", "HarpyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Harpy}", "HellbatBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Hellbat}", "HerplingBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Herpling}", "HornetBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Hornet}", "IceElementalBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.IceElemental}", "IcyMermanBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.IcyMerman}", "FireImpBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.FireImp}", "JellyfishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BlueJellyfish}", "JungleCreeperBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.JungleCreeper}", "LihzahrdBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.LihzahrdCrawler}", "ManEaterBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ManEater}", "MeteorHeadBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MeteorHead}", "MothBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Moth}", "MummyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Mummy}", "MushiLadybugBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MushiLadybug}", "ParrotBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Parrot}", "PigronBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PigronCorruption}", "PiranhaBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Piranha}", "PirateBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PirateDeckhand}", "PixieBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Pixie}", "RaincoatZombieBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ZombieRaincoat}", "ReaperBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Reaper}", "SharkBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Shark}", "SkeletonBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Skeleton}", "SkeletonMageBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DarkCaster}", "SlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BlueSlime}", "SnowFlinxBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SnowFlinx}", "SpiderBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.WallCreeper}", "SporeZombieBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ZombieMushroom}", "SwampThingBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SwampThing}", "TortoiseBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GiantTortoise}", "ToxicSludgeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ToxicSludge}", "UmbrellaSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.UmbrellaSlime}", "UnicornBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Unicorn}", "VampireBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.VampireBat}", "VultureBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Vulture}", "NypmhBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Nymph}", "WerewolfBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Werewolf}", "WolfBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Wolf}", "WorldFeederBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SeekerHead}", "WormBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GiantWormHead}", "WraithBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Wraith}", "WyvernBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.WyvernHead}", "ZombieBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Zombie}", "JackingSkeletron": "{$PaintingArtist.<PERSON>}", "BitterHarvest": "{$PaintingArtist.Lazure}", "BloodMoonCountess": "{$PaintingArtist.Lazure}", "HallowsEve": "{$PaintingArtist.Lazure}", "MorbidCuriosity": "{$PaintingArtist.Lazure}", "StarTopper1": "{$CommonItemTooltip.PlaceableOnXmasTree}", "StarTopper2": "{$CommonItemTooltip.PlaceableOnXmasTree}", "StarTopper3": "{$CommonItemTooltip.PlaceableOnXmasTree}", "BowTopper": "{$CommonItemTooltip.PlaceableOnXmasTree}", "WhiteGarland": "{$CommonItemTooltip.PlaceableOnXmasTree}", "WhiteAndRedGarland": "{$CommonItemTooltip.PlaceableOnXmasTree}", "RedGardland": "{$CommonItemTooltip.PlaceableOnXmasTree}", "RedAndGreenGardland": "{$CommonItemTooltip.PlaceableOnXmasTree}", "GreenGardland": "{$CommonItemTooltip.PlaceableOnXmasTree}", "GreenAndWhiteGarland": "{$CommonItemTooltip.PlaceableOnXmasTree}", "MulticoloredBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "RedBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "YellowBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "GreenBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "RedAndGreenBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "YellowAndGreenBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "RedAndYellowBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "WhiteBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "WhiteAndRedBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "WhiteAndYellowBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "WhiteAndGreenBulb": "{$CommonItemTooltip.PlaceableOnXmasTree}", "MulticoloredLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "RedLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "GreenLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "BlueLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "YellowLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "RedAndYellowLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "RedAndGreenLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "YellowAndGreenLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "BlueAndGreenLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "RedAndBlueLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "BlueAndYellowLights": "{$CommonItemTooltip.PlaceableOnXmasTree}", "PillaginMePixels": "{$PaintingArtist.Lazure}", "PaintingCastleMarsberg": "{$PaintingArtist.Lazure}", "PaintingMartiaLisa": "{$PaintingArtist.Lazure}", "PaintingTheTruthIsUpThere": "{$PaintingArtist.Lazure}", "AngryTrapperBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.AngryTrapper}", "ArmoredVikingBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ArmoredViking}", "BlackSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BlackSlime}", "BlueArmoredBonesBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BlueArmoredBones}", "BlueCultistArcherBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CultistArcherBlue}", "BlueCultistFighterBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.None}", "BoneLeeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BoneLee}", "ClingerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Clinger}", "CochinealBeetleBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CochinealBeetle}", "CorruptPenguinBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CorruptPenguin}", "CorruptSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CorruptSlime}", "CorruptorBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Corruptor}", "CrimslimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Crimslime}", "CursedSkullBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CursedSkull}", "CyanBeetleBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CyanBeetle}", "DevourerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DevourerHead}", "DiablolistBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DiabolistRed}", "DoctorBonesBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DoctorBones}", "DungeonSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DungeonSlime}", "DungeonSpiritBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DungeonSpirit}", "ElfArcherBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ElfArcher}", "ElfCopterBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ElfCopter}", "EyezorBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Eyezor}", "FlockoBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Flocko}", "GhostBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Ghost}", "GiantBatBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GiantBat}", "GiantCursedSkullBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GiantCursedSkull}", "GiantFlyingFoxBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GiantFlyingFox}", "GingerbreadManBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GingerbreadMan}", "GoblinArcherBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GoblinArcher}", "GreenSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GreenSlime}", "HeadlessHorsemanBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.HeadlessHorseman}", "HellArmoredBonesBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.HellArmoredBones}", "HellhoundBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Hellhound}", "HoppinJackBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.HoppinJack}", "IceBatBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.IceBat}", "IceGolemBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.IceGolem}", "IceSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.IceSlime}", "IchorStickerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.IchorSticker}", "IlluminantBatBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.IlluminantBat}", "IlluminantSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.IlluminantSlime}", "JungleBatBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.JungleBat}", "JungleSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.JungleSlime}", "KrampusBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Krampus}", "LacBeetleBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.LacBeetle}", "LavaBatBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Lavabat}", "LavaSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.LavaSlime}", "MartianBrainscramblerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BrainScrambler}", "MartianDroneBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MartianDrone}", "MartianEngineerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MartianEngineer}", "MartianGigazapperBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GigaZapper}", "MartianGreyGruntBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GrayGrunt}", "MartianOfficerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MartianOfficer}", "MartianRaygunnerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.RayGunner}", "MartianScutlixGunnerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ScutlixRider}", "MartianTeslaTurretBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MartianTurret}", "MisterStabbyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MisterStabby}", "MotherSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MotherSlime}", "NecromancerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Necromancer}", "NutcrackerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Nutcracker}", "PaladinBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Paladin}", "PenguinBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Penguin}", "PinkyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Pinky}", "PoltergeistBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Poltergeist}", "PossessedArmorBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PossessedArmor}", "PresentMimicBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PresentMimic}", "PurpleSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PurpleSlime}", "RaggedCasterBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.RaggedCaster}", "RainbowSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.RainbowSlime}", "RavenBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Raven}", "RedSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.RedSlime}", "RuneWizardBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.RuneWizard}", "RustyArmoredBonesBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.RustyArmoredBonesAxe}", "ScarecrowBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Scarecrow1}", "ScutlixBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Scutlix}", "SkeletonArcherBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SkeletonArcher}", "SkeletonCommandoBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SkeletonCommando}", "SkeletonSniperBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SkeletonSniper}", "SlimerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Slimer}", "SnatcherBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Snatcher}", "SnowBallaBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SnowBalla}", "SnowmanGangstaBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SnowmanGangsta}", "SpikedIceSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SpikedIceSlime}", "SpikedJungleSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SpikedJungleSlime}", "SplinterlingBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Splinterling}", "SquidBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Squid}", "TacticalSkeletonBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.TacticalSkeleton}", "TheGroomBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.TheGroom}", "TimBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Tim}", "UndeadMinerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.UndeadMiner}", "UndeadVikingBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.UndeadViking}", "WhiteCultistArcherBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CultistArcherWhite}", "WhiteCultistCasterBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.None}", "WhiteCultistFighterBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.None}", "YellowSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.YellowSlime}", "YetiBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Yeti}", "ZombieElfBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ZombieElf}", "SparkyPainting": "{$PaintingArtist.<PERSON>o}\n“在爱的回忆中”", "PaintingAcorns": "{$PaintingArtist.Lazure}", "PaintingColdSnap": "{$PaintingArtist.Lazure}", "PaintingCursedSaint": "{$PaintingArtist.Lazure}", "PaintingSnowfellas": "{$PaintingArtist.Lazure}", "PaintingTheSeason": "{$PaintingArtist.Lazure}", "RedString": "{$CommonItemTooltip.String}", "OrangeString": "{$CommonItemTooltip.String}", "YellowString": "{$CommonItemTooltip.String}", "LimeString": "{$CommonItemTooltip.String}", "GreenString": "{$CommonItemTooltip.String}", "TealString": "{$CommonItemTooltip.String}", "CyanString": "{$CommonItemTooltip.String}", "SkyBlueString": "{$CommonItemTooltip.String}", "BlueString": "{$CommonItemTooltip.String}", "PurpleString": "{$CommonItemTooltip.String}", "VioletString": "{$CommonItemTooltip.String}", "PinkString": "{$CommonItemTooltip.String}", "BrownString": "{$CommonItemTooltip.String}", "WhiteString": "{$CommonItemTooltip.String}", "RainbowString": "{$CommonItemTooltip.String}", "BlackString": "{$CommonItemTooltip.String}", "BlackCounterweight": "{$CommonItemTooltip.Counterweight}", "BlueCounterweight": "{$CommonItemTooltip.Counterweight}", "GreenCounterweight": "{$CommonItemTooltip.Counterweight}", "PurpleCounterweight": "{$CommonItemTooltip.Counterweight}", "RedCounterweight": "{$CommonItemTooltip.Counterweight}", "YellowCounterweight": "{$CommonItemTooltip.Counterweight}", "KingSlimeBossBag": "{$CommonItemTooltip.RightClickToOpen}", "EyeOfCthulhuBossBag": "{$CommonItemTooltip.RightClickToOpen}", "EaterOfWorldsBossBag": "{$CommonItemTooltip.RightClickToOpen}", "BrainOfCthulhuBossBag": "{$CommonItemTooltip.RightClickToOpen}", "QueenBeeBossBag": "{$CommonItemTooltip.RightClickToOpen}", "SkeletronBossBag": "{$CommonItemTooltip.RightClickToOpen}", "WallOfFleshBossBag": "{$CommonItemTooltip.RightClickToOpen}", "DestroyerBossBag": "{$CommonItemTooltip.RightClickToOpen}", "TwinsBossBag": "{$CommonItemTooltip.RightClickToOpen}", "SkeletronPrimeBossBag": "{$CommonItemTooltip.RightClickToOpen}", "PlanteraBossBag": "{$CommonItemTooltip.RightClickToOpen}", "GolemBossBag": "{$CommonItemTooltip.RightClickToOpen}", "FishronBossBag": "{$CommonItemTooltip.RightClickToOpen}", "CultistBossBag": "{$CommonItemTooltip.RightClickToOpen}", "MoonLordBossBag": "{$CommonItemTooltip.RightClickToOpen}", "GoblinSummonerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GoblinSummoner}", "SalamanderBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Salamander}", "GiantShellyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GiantShelly}", "CrawdadBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Crawdad}", "FritzBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Fritz}", "CreatureFromTheDeepBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CreatureFromTheDeep}", "DrManFlyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DrManFly}", "MothronBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Mothron}", "SeveredHandBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.None}", "ThePossessedBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ThePossessed}", "ButcherBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Butcher}", "PsychoBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Psycho}", "DeadlySphereBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DeadlySphere}", "NailheadBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Nailhead}", "PoisonousSporeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.None}", "MedusaBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Medusa}", "GreekSkeletonBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GreekSkeleton}", "GraniteFlyerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GraniteFlyer}", "GraniteGolemBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GraniteGolem}", "BloodZombieBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BloodZombie}", "DripplerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Drippler}", "TombCrawlerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.TombCrawlerHead}", "DuneSplicerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DuneSplicerHead}", "FlyingAntlionBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.FlyingAntlion}", "WalkingAntlionBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.WalkingAntlion}", "DesertGhoulBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DesertGhoul}", "DesertLamiaBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DesertLamiaDark}", "DesertDjinnBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DesertDjinn}", "DesertBasiliskBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DesertBeast}", "RavagerScorpionBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DesertScorpionWalk}", "StardustSoldierBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.StardustSoldier}", "StardustWormBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.StardustWormHead}", "StardustJellyfishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.StardustJellyfishBig}", "StardustSpiderBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.StardustSpiderBig}", "StardustSmallCellBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.StardustCellSmall}", "StardustLargeCellBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.StardustCellBig}", "SolarCoriteBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SolarCorite}", "SolarSrollerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SolarSroller}", "SolarCrawltipedeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SolarCrawltipedeHead}", "SolarDrakomireRiderBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SolarDrakomireRider}", "SolarDrakomireBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SolarDrakomire}", "SolarSolenianBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SolarSolenian}", "NebulaSoldierBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.NebulaSoldier}", "NebulaHeadcrabBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.NebulaHeadcrab}", "NebulaBrainBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.NebulaBrain}", "NebulaBeastBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.NebulaBeast}", "VortexLarvaBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.VortexLarva}", "VortexHornetQueenBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.VortexHornetQueen}", "VortexHornetBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.VortexHornet}", "VortexSoldierBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.VortexSoldier}", "VortexRiflemanBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.VortexRifleman}", "PirateCaptainBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PirateCaptain}", "PirateDeadeyeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PirateDeadeye}", "PirateCorsairBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PirateCorsair}", "PirateCrossbowerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PirateCrossbower}", "MartianWalkerBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MartianWalker}", "RedDevilBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.RedDevil}", "PinkJellyfishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.PinkJellyfish}", "GreenJellyfishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GreenJellyfish}", "DarkMummyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.DarkMummy}", "LightMummyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.LightMummy}", "AngryBonesBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.AngryBones}", "IceTortoiseBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.IceTortoise}", "SandSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SandSlime}", "SeaSnailBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SeaSnail}", "MoonLordPainting": "{$PaintingArtist.<PERSON>}", "SandElementalBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SandElemental}", "SandsharkBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SandShark}", "SandsharkCorruptBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SandsharkCorrupt}", "SandsharkCrimsonBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SandsharkCrimson}", "SandsharkHallowedBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SandsharkHallow}", "TumbleweedBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Tumbleweed}", "BossBagBetsy": "{$CommonItemTooltip.RightClickToOpen}", "BossBagOgre": "{$CommonItemTooltip.RightClickToOpen}", "BossBagDarkMage": "{$CommonItemTooltip.RightClickToOpen}", "ExplosiveBunny": "搭配兔兔炮使用", "VialofVenom": "“剧毒”", "FlaskofVenom": "近战攻击和鞭子攻击会对敌人施放酸性毒液", "VenomArrow": "对目标施放酸性毒液", "VenomBullet": "对目标施放酸性毒液", "PartyBullet": "碰撞时爆炸成彩纸", "NanoBullet": "造成困惑效果，并在撞墙后弹回", "ExplodingBullet": "碰撞时爆炸", "GoldenBullet": "杀死的敌人会掉落更多的钱", "FlaskofCursedFlames": "近战攻击和鞭子攻击会对敌人施放诅咒焰", "FlaskofFire": "近战攻击和鞭子攻击会使敌人着火", "FlaskofGold": "近战攻击和鞭子攻击会使敌人掉落更多金子", "FlaskofIchor": "近战攻击和鞭子攻击会降低敌人防御力", "FlaskofNanites": "近战攻击和鞭子攻击会让敌人进入困惑状态", "FlaskofParty": "近战攻击和鞭子攻击会喷撒彩纸", "FlaskofPoison": "近战攻击和鞭子攻击会使敌人中毒", "CobaltBreastplate": "暴击率提高5%", "CobaltLeggings": "移动速度提高10%，伤害提高3%", "MythrilChainmail": "伤害提高7%", "MythrilGreaves": "暴击率提高10%", "RocketI": "爆破半径小。不会破坏图格", "RocketII": "爆破半径小。会破坏图格", "RocketIII": "爆破半径大。不会破坏图格", "RocketIV": "爆破半径大。会破坏图格", "AsphaltBlock": "提高奔跑速度", "CobaltPickaxe": "可开采秘银和山铜", "MythrilPickaxe": "可开采精金和钛金", "Cannonball": "搭配大炮使用", "Arkhalis": "“我不是从虚身上打出这个的”", "BoneGlove": "在你攻击时向敌人射出交叉骨头", "LogicGate_AND": "所有灯亮时激活，其他情况下停用", "LogicGate_NAND": "所有灯不全亮时激活，其他情况下停用", "DD2ElderCrystalStand": "托住永恒水晶\n在放着永恒水晶时与其交互可召唤埃特尼亚传送门\n与水晶交互可跳过进攻波次之间的附加时间", "DD2ElderCrystal": "放入永恒水晶座来召唤埃特尼亚传送门", "DefenderMedal": "与酒馆老板进行交易的货币", "DD2FlameburstTowerT1Popper": "{$CommonItemTooltip.Sentry}\n速度一般的防御塔，可以射出爆炸火球\n{$CommonItemTooltip.EtherianManaCost10}", "DD2FlameburstTowerT2Popper": "{$ItemTooltip.DD2FlameburstTowerT1Popper}", "DD2FlameburstTowerT3Popper": "{$ItemTooltip.DD2FlameburstTowerT1Popper}", "DD2BallistraTowerT1Popper": "{$CommonItemTooltip.Sentry}\n速度缓慢但伤害力极高的防御塔，可以射出穿透性箭矢\n{$CommonItemTooltip.EtherianManaCost10}", "DD2BallistraTowerT2Popper": "{$ItemTooltip.DD2BallistraTowerT1Popper}", "DD2BallistraTowerT3Popper": "{$ItemTooltip.DD2BallistraTowerT1Popper}", "DD2ExplosiveTrapT1Popper": "{$CommonItemTooltip.Sentry}\n可在敌人来到附近时爆炸的机关\n{$CommonItemTooltip.EtherianManaCost10}", "DD2ExplosiveTrapT2Popper": "{$ItemTooltip.DD2ExplosiveTrapT1Popper}", "DD2ExplosiveTrapT3Popper": "{$ItemTooltip.DD2ExplosiveTrapT1Popper}", "DD2LightningAuraT1Popper": "{$CommonItemTooltip.Sentry}\n可以反复击打进入其内部的敌人的光环\n光环的伤害会穿透敌人的防御\n消耗50%标记伤害快速击打\n{$CommonItemTooltip.EtherianManaCost10}", "DD2LightningAuraT2Popper": "{$ItemTooltip.DD2LightningAuraT1Popper}", "DD2LightningAuraT3Popper": "{$ItemTooltip.DD2LightningAuraT1Popper}", "DD2PetGato": "召唤宠物Gato", "DD2PetGhost": "召唤宠物闪烁灯芯来提供照明", "DD2PetDragon": "召唤宠物龙", "DD2SquireDemonSword": "<right>来用护盾防护", "DD2SquireBetsySword": "向前释放心的能量", "DD2PhoenixBow": "利用不朽火焰的力量", "DD2BetsyBow": "射出分裂的箭，对空中的敌人造成更多伤害", "MonkStaffT1": "在挥舞着粉碎敌人时能充能", "MonkStaffT2": "在击中敌人时召唤鬼魂", "MonkStaffT3": "手持时<right>可施展另一种攻击！", "BookStaff": "想知道是谁把一本无限智慧巨著戳在一根棍子上……\n<right>来施放强大的龙卷风", "ApprenticeStaffT3": "泼洒能降低防御的瘴气！", "ApprenticeHat": "哨兵数量上限增加1，魔法伤害提高10%，魔力消耗降低10%", "ApprenticeRobe": "召唤伤害提高20%，魔法伤害提高10%", "ApprenticeTrousers": "召唤伤害提高10%，魔法暴击率和移动速度各提高20%", "SquireGreatHelm": "哨兵数量上限增加1，提高生命再生速度", "SquirePlating": "召唤伤害和近战伤害各提高15%", "SquireGreaves": "召唤伤害提高15%，近战暴击率和移动速度各提高15%", "MonkBrows": "哨兵数量上限增加1，近战攻击速度提高20%", "MonkShirt": "召唤伤害和近战伤害各提高20%", "MonkPants": "召唤伤害提高10%，\n近战暴击率提高15%，移动速度提高20%", "HuntressWig": "哨兵数量上限增加1，远程暴击率提高10%", "HuntressJerkin": "召唤伤害和远程伤害各提高20%，10%几率省下弹药", "HuntressPants": "召唤伤害提高10%，移动速度提高20%", "ApprenticeScarf": "{$ItemTooltip.HuntressBuckler}", "SquireShield": "{$ItemTooltip.HuntressBuckler}", "MonkBelt": "{$ItemTooltip.HuntressBuckler}", "HuntressBuckler": "哨兵数量上限增加1\n召唤伤害提高10%", "SquireAltHead": "哨兵数量上限增加2，召唤伤害和近战伤害各提高10%", "SquireAltShirt": "召唤伤害提高30%，生命再生速度大幅提高", "SquireAltPants": "召唤伤害和近战暴击率各提高20%\n移动速度提高20%", "ApprenticeAltHead": "哨兵数量上限增加2\n召唤伤害和魔法伤害各提高15%", "ApprenticeAltShirt": "召唤伤害提高25%，魔法伤害提高10%\n魔力消耗降低15%", "ApprenticeAltPants": "召唤伤害提高20%，魔法暴击率提高25%\n移动速度提高20%", "HuntressAltHead": "哨兵数量上限增加2\n召唤伤害和远程暴击率各提高10%", "HuntressAltShirt": "召唤伤害和远程伤害各提高25%，20%几率省下弹药", "HuntressAltPants": "召唤伤害提高25%，远程暴击率提高10%\n移动速度提高20%", "MonkAltHead": "哨兵数量上限增加2，召唤伤害和近战伤害各提高20%", "MonkAltShirt": "召唤伤害和近战速度各提高20%\n近战暴击率提高5%", "MonkAltPants": "召唤伤害和近战暴击率各提高20%\n移动速度提高30%", "DD2EnergyCrystal": "经常用于将人的意志表现为实体形态的防御。", "ArkhalisHat": "{$CommonItemTooltip.DevItem}\n“我不是从网格中拿到这个的”", "ArkhalisShirt": "{$ItemTooltip.ArkhalisHat}", "ArkhalisPants": "{$ItemTooltip.ArkhalisHat}", "ArkhalisWings": "{$ItemTooltip.ArkhalisHat}\n{$CommonItemTooltip.FlightAndSlowfall}", "LeinforsHat": "{$CommonItemTooltip.DevItem}\n“为了保持甜美动人的风格”", "LeinforsShirt": "{$CommonItemTooltip.DevItem}\n“重获性感”", "LeinforsPants": "{$CommonItemTooltip.DevItem}\n“口袋里可能有口香糖”", "LeinforsWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}\n“它已全开！啥意思啊？！”", "LeinforsAccessory": "{$CommonItemTooltip.DevItem}\n“LeinCorp出品”", "WoodenCrateHard": "{$ItemTooltip.WoodenCrate}", "IronCrateHard": "{$ItemTooltip.IronCrate}", "GoldenCrateHard": "{$ItemTooltip.GoldenCrate}", "CorruptFishingCrateHard": "{$ItemTooltip.CorruptFishingCrate}", "CrimsonFishingCrateHard": "{$ItemTooltip.CrimsonFishingCrate}", "DungeonFishingCrateHard": "{$ItemTooltip.DungeonFishingCrate}", "FloatingIslandFishingCrateHard": "{$ItemTooltip.FloatingIslandFishingCrate}", "HallowedFishingCrateHard": "{$ItemTooltip.HallowedFishingCrate}", "JungleFishingCrateHard": "{$ItemTooltip.JungleFishingCrate}", "HeartLantern": "放在附近时提高生命再生速度", "StarinaBottle": "放在附近时提高魔力再生速度", "Cloud": "防止掉落伤害", "RainCloud": "防止掉落伤害", "SnowCloudBlock": "防止掉落伤害", "AmphibianBoots": "穿戴者可飞速奔跑\n提高跳跃速度，并可自动跳跃\n提高掉落抗性", "ArcaneFlower": "魔力消耗降低8%\n需要时自动使用魔力药水\n敌人不太可能以你为目标", "BerserkerGlove": "提高近战击退力\n近战速度提高12%\n让近战武器能自动挥舞\n加大近战武器的尺寸\n敌人更有可能以你为目标", "FairyBoots": "可飞行\n穿戴者可飞速奔跑\n你走过的草地上会长出花朵", "FrogFlipper": "给予游泳能力\n提高跳跃速度，并可自动跳跃\n提高掉落抗性", "FrogGear": "给予游泳能力\n可爬墙\n提高跳跃速度，并可自动跳跃\n提高掉落抗性\n“身为绿皮生物可太难了”", "FrogWebbing": "可爬墙\n提高跳跃速度，并可自动跳跃\n提高掉落抗性", "FrozenShield": "对击退免疫\n生命值低于50%时，在所有者周围放置可减少25%伤害的护罩\n当生命值高于25%时，吸收团队中其他玩家所受伤害的25%", "HeroShield": "对击退免疫\n当生命值高于25%时，吸收团队中其他玩家所受伤害的25%\n敌人更有可能以你为目标", "LavaSkull": "7秒内对熔岩免疫\n对火块免疫", "MoltenCharm": "对火块免疫\n7秒内对熔岩免疫", "MagnetFlower": "魔力消耗降低8%\n需要时自动使用魔力药水\n扩大魔力星的拾取范围", "ManaCloak": "魔力消耗降低8%\n需要时自动使用魔力药水\n受到伤害后会使星星坠落\n收集星星会恢复魔力", "MoltenQuiver": "箭的伤害提高10%，箭的速度大大提高\n20%几率不消耗箭\n点燃木箭，火光熊熊\n“在恐惧中颤抖吧！”", "ObsidianSkullRose": "对火块免疫\n减少因触碰熔岩而受到的伤害", "MoltenSkullRose": "7秒内对熔岩免疫\n对火块免疫\n减少因触碰熔岩而受到的伤害", "ReconScope": "扩大枪的视野范围（<right>可拉远视野）\n远程伤害和远程暴击率各提高10%\n敌人不太可能以你为目标\n“发现敌人”", "StalkersQuiver": "箭的伤害提高10%，箭的速度大大提高\n20%几率不消耗箭\n敌人不太可能以你为目标", "StingerNecklace": "盔甲穿透力提高5点\n受到伤害后释放蜜蜂并将使用者浸入蜂蜜中", "UltrabrightHelmet": "穿戴时可改善视野并提供照明\n“黑暗对你毫无保留”", "SandBoots": "穿戴者可飞速奔跑，在沙地上还能跑得更快\n“无节律行走就不会引来蠕虫”", "AncientChisel": "挖矿速度提高25%\n“古老的问题需要古老的解决方案”", "Mannequin": "<right>可定制服装", "Womannquin": "<right>可定制服装", "HatRack": "放置状态下<right>可以显示帽子和头盔", "DeadMansChest": "打开时激活", "GolfBall": "{$CommonItemTooltip.GolfBall}", "GolfClubIron": "{$CommonItemTooltip.GolfIron}", "GolfCup": "把高尔夫球击进球洞\n让所有人知道你有多厉害", "LawnMower": "修剪纯净草和神圣草\n修剪过的草能降低敌人的生成几率", "GolfTee": "<right>可把高尔夫球放在上面", "AntiPortalBlock": "不能在这些物块表面创建传送门", "GolfClubPutter": "{$CommonItemTooltip.GolfPutter}", "GolfClubWedge": "{$CommonItemTooltip.GolfWedge}", "GolfClubDriver": "{$CommonItemTooltip.GolfDriver}", "GolfWhistle": "将最后一次击出的高尔夫球恢复到之前的位置\n罚一杆", "VoidLens": "召唤虚空保险库\n打开时，可用作扩展物品栏\n打开时，可拾取溢出的物品\n{$CommonItemTooltip.RightClickToClose}\n“这个口袋的大小太了不得了！”", "ClosedVoidBag": "召唤虚空保险库\n打开时，可用作扩展物品栏\n打开时，可拾取溢出的物品\n{$CommonItemTooltip.RightClickToOpen}\n“这个口袋的大小太了不得了！”", "LesionStation": "用于特殊制作", "TitanGlove": "提高近战击退力\n加大近战武器的尺寸", "PowerGlove": "提高近战击退力\n近战速度提高12%\n让近战武器能自动挥舞\n加大近战武器的尺寸", "MechanicalGlove": "提高近战击退力\n近战伤害和近战速度各提高12%\n让近战武器能自动挥舞\n加大近战武器的尺寸", "FireGauntlet": "提高近战击退力，近战攻击造成火焰伤害\n近战伤害和近战速度各提高12%\n让近战武器能自动挥舞\n加大近战武器的尺寸", "MusicBoxDayRemix": "由Xenon和DJ Sniper为你呈现", "GolfBallDyedBlack": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedBlue": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedBrown": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedCyan": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedGreen": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedLimeGreen": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedOrange": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedPink": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedPurple": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedRed": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedSkyBlue": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedTeal": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedViolet": "{$CommonItemTooltip.GolfBall}", "GolfBallDyedYellow": "{$CommonItemTooltip.GolfBall}", "AmberRobe": "最大魔力增加60\n魔力消耗降低13%", "OrangePressurePlate": "玩家踩上时激活然后就破碎", "PumpkinPie": "{$CommonItemTooltip.MediumStats}\n“一口就吃完了！”", "ChristmasPudding": "{$CommonItemTooltip.MajorStats}\n“壁炉边的温馨美味。”", "SugarCookie": "{$CommonItemTooltip.MajorStats}\n“你会变得精力充沛！”", "GingerbreadCookie": "{$CommonItemTooltip.MajorStats}\n“别动橡皮糖纽扣！”", "PadThai": "{$CommonItemTooltip.MediumStats}\n“五星级辣度！”", "Pho": "{$CommonItemTooltip.MediumStats}\n“粉它！”", "CookedFish": "{$CommonItemTooltip.MinorStats}\n“薯条在哪里！？”", "CookedShrimp": "{$CommonItemTooltip.MediumStats}\n“烧、煮、烤、烘……”", "Sashimi": "{$CommonItemTooltip.MediumStats}\n“是生的！异国风味！”", "BowlofSoup": "{$CommonItemTooltip.MediumStats}\n“简单但爽口。”", "CookedMarshmallow": "{$CommonItemTooltip.MinorStats}\n“我怎么才能再吃点棉花糖呢？”", "Bacon": "{$CommonItemTooltip.MajorStats}\n“培根？培根。”", "GrubSoup": "{$CommonItemTooltip.MediumStats}\n“蛆虫雄起！”", "Apple": "{$CommonItemTooltip.MinorStats}\n“一天一苹果，骷髅博士远离我！”", "ApplePieSlice": "{$CommonItemTooltip.MediumStats}\n“嗯……派。”", "ApplePie": "{$CommonItemTooltip.MajorStats}\n“没有什么比苹果派更具泰拉瑞亚特色了。”", "BananaSplit": "{$CommonItemTooltip.MediumStats}\n“做成香蕉样，然后剖开！”", "BBQRibs": "{$CommonItemTooltip.MajorStats}\n“烤至极致！”", "BunnyStew": "{$CommonItemTooltip.MinorStats}\n“这只兔兔的好运到头了。”", "Burger": "{$CommonItemTooltip.MajorStats}\n“……等等！它只要99美分。”", "ChickenNugget": "{$CommonItemTooltip.MediumStats}\n“警告：可能含有鸟妖肉。”", "ChocolateChipCookie": "{$CommonItemTooltip.MediumStats}\n“新鲜出炉”", "CreamSoda": "{$CommonItemTooltip.MediumStats}\n“好多泡沫！”", "Escargot": "{$CommonItemTooltip.MediumStats}\n“看，蜗轮增压！”", "FriedEgg": "{$CommonItemTooltip.MediumStats}\n“只煎一面！”", "Fries": "{$CommonItemTooltip.MediumStats}\n“番茄酱在哪里！？”", "GoldenDelight": "{$CommonItemTooltip.MajorStats}\n“（金）好吃！”", "Grapes": "{$CommonItemTooltip.MediumStats}\n“不包括怒气。”", "GrilledSquirrel": "{$CommonItemTooltip.MinorStats}\n“这样就能让你远离我的喂鸟器了……”", "Hotdog": "{$CommonItemTooltip.MajorStats}\n“美味！”", "IceCream": "{$CommonItemTooltip.MediumStats}\n“在它融化之前吃掉！”", "Milkshake": "{$CommonItemTooltip.MajorStats}\n“它把男孩们带到了院子里！”", "Nachos": "{$CommonItemTooltip.MediumStats}\n“熟玉米？属于我！”", "Pizza": "{$CommonItemTooltip.MajorStats}\n“有意大利辣香肠还多加了奶酪。”", "PotatoChips": "{$CommonItemTooltip.MinorStats}\n“包你停不了嘴！”", "RoastedBird": "{$CommonItemTooltip.MinorStats}\n“每份：1小孩。”", "RoastedDuck": "{$CommonItemTooltip.MediumStats}\n“比墙里找到的烤鸡更好吃。”", "SauteedFrogLegs": "{$CommonItemTooltip.MinorStats}\n“另一种，另一种白肉。”", "SeafoodDinner": "{$CommonItemTooltip.MediumStats}\n“我，海鲜，吃掉。”", "ShrimpPoBoy": "{$CommonItemTooltip.MediumStats}\n“尽情享用三明治吧！”", "Spaghetti": "{$CommonItemTooltip.MajorStats}\n“弹牙！”", "Steak": "{$CommonItemTooltip.MajorStats}\n“搭配番茄酱很棒！”", "Ale": "{$CommonItemTooltip.TipsyStats}\n“干杯！”", "Sake": "{$CommonItemTooltip.TipsyStats}\n“喝多了你会变成空手道大师。”", "GolfCart": "召唤可骑乘的高尔夫球车坐骑", "BloodMoonStarter": "召唤血月\n“诅咒之夜太可怕了。”", "GoldGoldfish": "“不要与难以捉摸的金金金鱼混淆”", "GoldGoldfishBowl": "“金的是缸还是鱼？”", "Apricot": "{$CommonItemTooltip.MinorStats}\n“你会喜欢的！”", "Banana": "{$CommonItemTooltip.MinorStats}\n“优质钾源！”", "BlackCurrant": "{$CommonItemTooltip.MinorStats}", "BloodOrange": "{$CommonItemTooltip.MinorStats}\n“吸血鬼的素食之选”", "Cherry": "{$CommonItemTooltip.MinorStats}", "Coconut": "{$CommonItemTooltip.MinorStats}\n“你是说椰子会迁徙吗？”", "Dragonfruit": "{$CommonItemTooltip.MediumStats}", "Elderberry": "{$CommonItemTooltip.MinorStats}\n“闻起来像你爸爸”", "Grapefruit": "{$CommonItemTooltip.MinorStats}", "Lemon": "{$CommonItemTooltip.MinorStats}\n“当生活给了你柠檬……”", "Mango": "{$CommonItemTooltip.MinorStats}", "Peach": "{$CommonItemTooltip.MinorStats}", "Pineapple": "{$CommonItemTooltip.MinorStats}\n“搭配披萨很好吃”", "Plum": "{$CommonItemTooltip.MinorStats}", "Rambutan": "{$CommonItemTooltip.MinorStats}", "Starfruit": "{$CommonItemTooltip.MediumStats}", "StarWrath": "使星星如雨般从天而降", "FairyCritterPink": "“嘿！听着！”", "FairyCritterGreen": "“嘿！听着！”", "FairyCritterBlue": "“嘿！听着！”", "BabyBirdStaff": "召唤雀宝宝来为你战斗", "MysticCoilSnake": "召唤绳蛇", "SanguineStaff": "召唤血红蝙蝠来为你战斗", "CatBast": "放在附近时防御力增加5", "ItemFrame": "<right>可将物品放置在物品框上", "FoodPlatter": "<right>可将物品放置在盘子上", "CanOfWorms": "{$CommonItemTooltip.RightClickToOpen}", "Trident": "手持时可提高在水中的移动能力\n按住向上键可以减缓下降速度", "EucaluptusSap": "召唤宠物蜜袋鼯", "CelestialWand": "召唤埃斯蒂", "NecroHelmet": "远程伤害提高5%", "NecroBreastplate": "远程伤害提高5%", "NecroGreaves": "远程伤害提高5%", "AncientNecroHelmet": "远程伤害提高5%", "NinjaHood": "暴击率提高3%", "NinjaPants": "暴击率提高3%", "NinjaShirt": "暴击率提高3%", "FossilHelm": "远程暴击率提高4%", "FossilShirt": "远程伤害提高5%", "FossilPants": "远程暴击率提高4%", "EncumberingStone": "锁上时能防止拾取物品\n<right>可解锁\n“你负重过多”", "UncumberingStone": "锁上时能防止拾取物品\n<right>可锁上\n“你负重过多”", "SharpTears": "从地面召出血荆棘", "VampireFrogStaff": "召唤吸血鬼青蛙来为你战斗", "CombatBook": "增强所有城镇居民的防御力和力量\n“包含攻防战斗技能”", "PortableStool": "按住向上键可以够到更高的地方", "CoralTorch": "可放置在水中", "KryptonMoss": "“难以忘怀的景象”", "XenonMoss": "“难以忘怀的景象”", "ArgonMoss": "“难以忘怀的景象”", "ThinIce": "掉落在上面时它会裂开\n“注意脚下”", "FrozenCrate": "{$CommonItemTooltip.RightClickToOpen}", "FrozenCrateHard": "{$CommonItemTooltip.RightClickToOpen}", "OasisCrate": "{$CommonItemTooltip.RightClickToOpen}", "OasisCrateHard": "{$CommonItemTooltip.RightClickToOpen}", "OceanCrate": "{$CommonItemTooltip.RightClickToOpen}", "OceanCrateHard": "{$CommonItemTooltip.RightClickToOpen}", "ArrowSign": "<right>可改变方向", "PaintedArrowSign": "<right>可改变方向", "SpectreGoggles": "开启回声视觉，显示隐藏物块", "LobsterTail": "{$CommonItemTooltip.MediumStats}\n“加一点黄油就很美味了”", "Oyster": "{$CommonItemTooltip.RightClickToOpen}", "ShuckedOyster": "{$CommonItemTooltip.MinorStats}\n“噢，壳！”", "ClusterRocketI": "不会破坏图格", "ClusterRocketII": "会破坏图格", "MiniNukeI": "爆破半径巨大。不会破坏图格", "MiniNukeII": "爆破半径巨大。会破坏图格", "SandcastleBucket": "放置沙堡", "EchoBlock": "只在回声视觉下才能看到", "Grate": "仅允许液体通过\n可以切换开闭状态\n“太棒了！”", "LuckPotionLesser": "提高使用者的运气", "LuckPotion": "提高使用者的运气", "LuckPotionGreater": "提高使用者的运气", "MagicConch": "仔细听，你会听到海洋的声音", "TimerOneHalfSecond": "每二分之一秒激活一次", "TimerOneFourthSecond": "每四分之一秒激活一次", "Terragrim": "释放切割疾风", "FloatingTube": "可让人浮在水面", "SharkBait": "召唤小鲨鱼\n“嘟嘟嘟嘟嘟嘟”", "Geode": "打碎后可能会掉落珍贵的闪亮之物！", "CrackedBlueBrick": "踩上时可能会破碎", "CrackedGreenBrick": "踩上时可能会破碎", "CrackedPinkBrick": "踩上时可能会破碎", "BloodMoonMonolith": "“维持血月的一部分”", "VoidMonolith": "“动用少量虚空之力”", "ScarabBomb": "会摧毁大部分图格的微弱爆炸\n爆炸会背离你所在的位置", "GolfClubStoneIron": "{$CommonItemTooltip.GolfIron}", "GolfClubRustyPutter": "{$CommonItemTooltip.GolfPutter}", "GolfClubBronzeWedge": "{$CommonItemTooltip.GolfWedge}", "GolfClubWoodDriver": "{$CommonItemTooltip.GolfDriver}", "GolfClubMythrilIron": "{$CommonItemTooltip.GolfIron}", "GolfClubLeadPutter": "{$CommonItemTooltip.GolfPutter}", "GolfClubGoldWedge": "{$CommonItemTooltip.GolfWedge}", "GolfClubPearlwoodDriver": "{$CommonItemTooltip.GolfDriver}", "GolfClubTitaniumIron": "{$CommonItemTooltip.GolfIron}", "GolfClubShroomitePutter": "{$CommonItemTooltip.GolfPutter}", "GolfClubDiamondWedge": "{$CommonItemTooltip.GolfWedge}", "GolfClubChlorophyteDriver": "{$CommonItemTooltip.GolfDriver}", "FishMinecart": "允许在水中快速行进\n“干掉它”", "HellMinecart": "7秒内对熔岩免疫", "TheBrideBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.TheBride}", "ZombieMermanBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ZombieMerman}", "EyeballFlyingFishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.EyeballFlyingFish}", "BloodSquidBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BloodSquid}", "BloodEelBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BloodEelHead}", "GoblinSharkBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.GoblinShark}", "BloodNautilusBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BloodNautilus}", "WitchBroom": "“给人灵感的火花！”", "BirdieRattle": "召唤鸟妖宝宝\n“不适合你的日常小鹦鹉”", "ExoticEasternChewToy": "召唤耳廓狐\n“它的叫声是惊人的96千赫！”", "BedazzledNectar": "召唤宠物蝴蝶\n“只要最好、最精致的花蜜！”", "HellCake": "召唤小鬼宝宝\n“他还没学会怎么传送！”", "BambooLeaf": "召唤小熊猫宝宝", "Celeb2": "50%几率省下弹药\n“一切美好的事物都结束于砰的一声……或很多声！”", "AppleJuice": "{$CommonItemTooltip.MinorStats}\n“想吃苹果吗？”", "GrapeJuice": "{$CommonItemTooltip.MajorStats}\n“糖，水，紫。”", "Lemonade": "{$CommonItemTooltip.MinorStats}\n“……做柠檬水！”", "BananaDaiquiri": "{$CommonItemTooltip.MinorStats}\n“又黄又芳”", "PeachSangria": "{$CommonItemTooltip.MinorStats}\n“生活真美好”", "PinaColada": "{$CommonItemTooltip.MinorStats}\n“如果你喜欢椰林飘香又喜欢雨中漫步”", "TropicalSmoothie": "{$CommonItemTooltip.MinorStats}\n“真丝滑”", "BloodyMoscato": "{$CommonItemTooltip.MinorStats}\n“不是真正的血……是不是？”", "SmoothieofDarkness": "{$CommonItemTooltip.MinorStats}\n“到黑暗面来吧，我们有奶昔”", "PrismaticPunch": "{$CommonItemTooltip.MediumStats}\n“感受彩虹，领略水晶！”", "FruitJuice": "{$CommonItemTooltip.MinorStats}\n“含5%纯果汁！”", "FruitSalad": "{$CommonItemTooltip.MinorStats}", "AndrewSphinx": "{$PaintingArtist.UnitOne}（修复）", "WatchfulAntlion": "{$PaintingArtist.Aurora}（修复）", "BurningSpirit": "{$PaintingArtist.Zoomo}", "JawsOfDeath": "{$PaintingArtist.<PERSON>}", "TheSandsOfSlime": "{$PaintingArt<PERSON>.<PERSON>}", "SnakesIHateSnakes": "{$PaintingArtist.Xman101}", "LifeAboveTheSand": "{$PaintingArtist.Zoomo}", "Oasis": "{$PaintingArt<PERSON>.<PERSON>}", "PrehistoryPreserved": "“由<PERSON><PERSON> Schneider发掘”", "AncientTablet": "“由<PERSON><PERSON> Schneider发掘”", "Uluru": "{$PaintingArt<PERSON>.<PERSON>}", "VisitingThePyramids": "{$PaintingArtist.Leinfors}", "BandageBoy": "{$PaintingArtist.<PERSON>}", "DivineEye": "{$PaintingArtist.<PERSON>}", "FogMachine": "浓雾喷涌而出", "GolfPainting1": "{$PaintingArtist.<PERSON>}", "GolfPainting2": "{$PaintingArtist.<PERSON>}", "GolfPainting3": "{$PaintingArtist.<PERSON>}", "GolfPainting4": "“这不是高尔夫球杆”\n{$PaintingArtist.Crowno}", "UnicornHornHat": "“新的一天，新的开始！”", "ChumBucket": "扔入水中可提高渔力，最多3次\n“浮游生物！”", "GardenGnome": "“据说能带来好运、驱除邪灵”", "BloodFishingRod": "提高血月期间钓出敌人的几率", "IvyGuitar": "可演奏的乐器\n“死人毛衣乐队的东西”", "CarbonGuitar": "可演奏的乐器\n“这些小过门很劲爆”", "DrumStick": "在鼓组旁可演奏\n“咖啡浓烈……且乡土”", "GlowPaint": "完全照亮所涂覆的物体\n可以与任何其它漆或涂料组合使用\n“真是个好主意！”", "WetRocket": "碰撞时泼洒水", "LavaRocket": "碰撞时泼洒熔岩", "HoneyRocket": "碰撞时泼洒蜂蜜", "DryRocket": "碰撞时吸收液体", "BloodRainBow": "鲜血从天而降\n“血腥统治”", "DandelionBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Dandelion}", "GnomeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.Gnome}", "CrystalDart": "在敌人之间弹来弹去", "CursedDart": "滴落诅咒焰到地上", "IchorDart": "炸成多个飞镖", "DeadlySphereStaff": "召唤致命球来为你战斗", "DesertCampfire": "靠近篝火时生命再生提速", "CoralCampfire": "靠近篝火时生命再生提速", "CorruptCampfire": "靠近篝火时生命再生提速", "CrimsonCampfire": "靠近篝火时生命再生提速", "HallowedCampfire": "靠近篝火时生命再生提速", "JungleCampfire": "靠近篝火时生命再生提速", "EmptyBucket": "可用来舀起少量液体", "WaterBucket": "盛有少量水\n可以倒出", "LavaBucket": "盛有少量熔岩\n可以倒出", "HoneyBucket": "盛有少量蜂蜜\n可以倒出", "PiggyBank": "可用来储存你的物品\n储存的物品只有你能存取", "Safe": "可用来储存你的物品\n储存的物品只有你能存取", "DefendersForge": "可用来储存你的物品\n储存的物品只有你能存取", "VoidVault": "可用来储存你的物品\n储存的物品只有你能存取\n将包含由虚空袋拾取的物品", "MudBud": "召唤微型世纪之花", "ReleaseDoves": "“在某些仪式上放飞”", "TragicUmbrella": "持有此物可减缓掉落速度", "GravediggerShovel": "挖掘区域要比镐大\n只能挖掘软图格\n“明白了吗？”", "DungeonDesertKey": "打开地牢中的沙漠箱", "MolluskWhistle": "召唤可骑乘的火烈鸟坐骑", "BreakerBlade": "对未受伤的敌人造成更多伤害", "Nevermore": "{$PaintingArtist.<PERSON>}", "Reborn": "{$PaintingArtist.<PERSON>}", "Graveyard": "{$PaintingArtist.<PERSON>}", "GhostManifestation": "{$PaintingArtist.<PERSON>}", "WickedUndead": "{$PaintingArtist.<PERSON>}", "BloodyGoblet": "{$PaintingArtist.<PERSON>}", "StillLife": "{$PaintingArtist.<PERSON>}", "GhostarsWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}\n“我就在这里”", "GhostarSkullPin": "{$CommonItemTooltip.DevItem}\n“这会是真正的Ghostar吗？”", "GhostarShirt": "{$CommonItemTooltip.DevItem}\n“由一条龙手工制作的精美服装”", "GhostarPants": "{$CommonItemTooltip.DevItem}\n“千里之行，始于足下”", "BallOfFuseWire": "召唤雷管小猫\n“它就像线团，但更惊险刺激！”", "FullMoonSqueakyToy": "召唤狼人宝宝", "OrnateShadowKey": "召唤宠物暗影宝箱怪", "FoodBarbarianWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}\n“这些洞能减轻重量。”", "FoodBarbarianHelm": "{$CommonItemTooltip.DevItem}\n“安全第一”", "FoodBarbarianArmor": "{$CommonItemTooltip.DevItem}\n“Max是个好孩子。”", "FoodBarbarianGreaves": "{$CommonItemTooltip.DevItem}\n“他们不让我再穿缠腰布了。”", "SafemanWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}\n“你能帮我把这个系上吗？”", "SafemanSunHair": "{$CommonItemTooltip.DevItem}\n“灵光一闪”", "SafemanSunDress": "{$CommonItemTooltip.DevItem}\n“既时尚又实用”", "SafemanDressLeggings": "{$CommonItemTooltip.DevItem}\n“跟裤子差不多”", "GroxTheGreatWings": "{$CommonItemTooltip.DevItem}\n{$CommonItemTooltip.FlightAndSlowfall}", "GroxTheGreatHelm": "{$CommonItemTooltip.DevItem}", "GroxTheGreatArmor": "{$CommonItemTooltip.DevItem}", "GroxTheGreatGreaves": "{$CommonItemTooltip.DevItem}", "KiteWyvern": "{$CommonItemTooltip.Kite}", "KiteBlue": "{$CommonItemTooltip.Kite}", "KiteBlueAndYellow": "{$CommonItemTooltip.Kite}", "KiteRed": "{$CommonItemTooltip.Kite}", "KiteRedAndYellow": "{$CommonItemTooltip.Kite}", "KiteYellow": "{$CommonItemTooltip.Kite}", "KiteBoneSerpent": "{$CommonItemTooltip.Kite}", "KiteWorldFeeder": "{$CommonItemTooltip.Kite}", "KiteBunny": "{$CommonItemTooltip.Kite}", "KitePigron": "{$CommonItemTooltip.Kite}", "KiteManEater": "{$CommonItemTooltip.Kite}", "KiteJellyfishBlue": "{$CommonItemTooltip.Kite}", "KiteJellyfishPink": "{$CommonItemTooltip.Kite}", "KiteShark": "{$CommonItemTooltip.Kite}", "KiteSandShark": "{$CommonItemTooltip.Kite}", "KiteBunnyCorrupt": "{$CommonItemTooltip.Kite}", "KiteBunnyCrimson": "{$CommonItemTooltip.Kite}", "KiteGoldfish": "{$CommonItemTooltip.Kite}", "KiteAngryTrapper": "{$CommonItemTooltip.Kite}", "KiteKoi": "{$CommonItemTooltip.Kite}", "KiteCrawltipede": "{$CommonItemTooltip.Kite}", "KiteSpectrum": "{$CommonItemTooltip.Kite}", "KiteWanderingEye": "{$CommonItemTooltip.Kite}\n“这只不会游荡到太远”", "KiteUnicorn": "{$CommonItemTooltip.Kite}", "CritterShampoo": "给仆从染色", "DontHurtCrittersBook": "放在物品栏中时能防止你伤害小动物\n<right>停用其效果", "FairyGlowstick": "扔出后会悬停\n湿了也能用", "LightningCarrot": "召唤伏特兔", "SquirrelHook": "像真正的松鼠一样抓住树！\n“藏身于树，化身为树”", "Football": "试着接住它！", "FairyQueenBossBag": "{$CommonItemTooltip.RightClickToOpen}", "TreeGlobe": "投掷它可改变树的外观！\n“是时候换换风景了”", "WorldGlobe": "投掷它可改变世界的面貌！\n“是时候换换风景了”", "BottomlessLavaBucket": "盛有无限多熔岩\n可以倒出", "BugNet": "用来捕捉小动物", "GoldenBugNet": "用来捕捉小动物\n也可以捕捉熔岩小动物！", "WetBomb": "会泼洒水的小爆炸", "LavaBomb": "会泼洒熔岩的小爆炸", "HoneyBomb": "会泼洒蜂蜜的小爆炸", "DryBomb": "会吸收液体的小爆炸", "LicenseCat": "用于为城镇领养一只猫咪\n已经养了猫咪？\n再用一张许可证可以激活宠物交换计划！\n找到最搭你的猫咪！", "LicenseDog": "用于为城镇领养一只狗狗\n已经养了狗狗？\n再用一张许可证可以激活宠物交换计划！\n找到最搭你的狗狗！", "LavaAbsorbantSponge": "能够吸收无限多熔岩", "HallowedHood": "仆从数量上限增加1\n召唤伤害提高10%", "FlameWaderBoots": "可让人在水、蜂蜜和熔岩上行走\n对火块免疫并在7秒内对熔岩免疫", "LavaCrate": "{$CommonItemTooltip.RightClickToOpen}", "LavaCrateHard": "{$CommonItemTooltip.RightClickToOpen}", "ObsidianLockbox": "{$CommonItemTooltip.RightClickToOpen}\n需要暗影钥匙", "DemonConch": "仔细听，你会听到尖叫声\n“注意脚下”", "HotlineFishingHook": "{$CommonItemTooltip.LavaFishing}", "LavaFishingHook": "{$CommonItemTooltip.LavaFishing}", "AncientHallowedMask": "近战伤害和近战暴击率各提高10%\n近战速度提高10%", "AncientHallowedHelmet": "远程伤害提高15%\n远程暴击率提高8%", "AncientHallowedHeadgear": "最大魔力增加100\n魔法伤害和魔法暴击率各提高12%", "AncientHallowedHood": "仆从数量上限增加1\n召唤伤害提高10%", "AncientHallowedPlateMail": "暴击率提高7%", "AncientHallowedGreaves": "伤害提高7%\n移动速度提高8%", "PotionOfReturn": "将你传送回家并创建一个传送门\n完事后用传送门返回\n“适合往返行程！”", "HellfireTreads": "可飞行\n穿戴者可飞速奔跑\n在你身后留下一道火舌痕迹", "LavaFishbowl": "“不，不能套在头上”", "PirateShipMountItem": "召唤黑斑坐骑\n“啊！这是叛变！”", "SpookyWoodMountItem": "召唤可骑乘的阴森树坐骑\n“用受诅咒树的树枝制成的魔棒。”", "SantankMountItem": "召唤可骑乘的圣诞老人坦克坐骑\n“给熊孩子。”", "WallOfFleshGoatMountItem": "召唤可骑乘的死亡山羊坐骑\n“野蛮！”", "DarkMageBookMountItem": "召唤魔法巨著坐骑\n“据说是一本会听命于其持有者的书。”", "KingSlimePetItem": "召唤史莱姆王子\n“适合国王的甜点！”", "EyeOfCthulhuPetItem": "召唤可疑眼睛\n“似乎已经面目全非。”", "EaterOfWorldsPetItem": "召唤蠕虫吞噬怪\n“能给你带来蠕虫！”", "BrainOfCthulhuPetItem": "召唤蜘蛛脑\n“腌制脱水后，这个大脑再也不能伤害你。”", "SkeletronPetItem": "召唤小骷髅王\n“力量无与伦比的骷髅头变得迟钝了。”", "QueenBeePetItem": "召唤宝贝蜜蜂\n“皇家蜜蜂的秘方。”", "DestroyerPetItem": "召唤微型毁灭工具\n“操作这个是安全的，对吗？”", "TwinsPetItem": "召唤小机械魔眼\n“团结就是力量。”", "SkeletronPrimePetItem": "召唤小机械骷髅王\n“我们可以重建它。”", "PlanteraPetItem": "召唤刚萌芽的世纪之花\n“找到问题的根源。”", "GolemPetItem": "召唤玩具石巨人来照亮道路\n“不包括电池。”", "DukeFishronPetItem": "召唤小猪龙鱼\n“腌至完美。”", "LunaticCultistPetItem": "召唤幻影龙宝宝\n“含有幻影能量碎片。”", "MoonLordPetItem": "召唤月精灵\n“禁戒枪乌贼。”", "FairyQueenPetItem": "召唤仙灵公主来提供照明\n“住着强大仙灵的发光宝石。”", "PumpkingPetItem": "召唤疯狂杰克南瓜灯\n“火焰无法扑灭！”", "EverscreamPetItem": "召唤常绿尖叫怪树苗\n“一闪一闪亮晶晶！”", "IceQueenPetItem": "召唤小冰雪女王\n“适合女王！”", "MartianPetItem": "召唤异星滑板小子\n“零重力下，脚尖翻板要容易得多！”", "DD2OgrePetItem": "召唤食人魔宝宝\n“除了砸东西，其他什么都不会。”", "DD2BetsyPetItem": "召唤小双足翼龙\n“不需要火祭！”", "PaintedHorseSaddle": "召唤可骑乘的花马坐骑", "MajesticHorseSaddle": "召唤可骑乘的白马坐骑", "DarkHorseSaddle": "召唤可骑乘的黑马坐骑", "SuperheatedBlood": "召唤可骑乘的熔岩鲨坐骑\n“血腥地狱！”", "PogoStick": "召唤可骑乘的蹦蹦跷坐骑\n在空中再按一次“跳跃”可做特技！", "LicenseBunny": "用于为城镇领养一只兔兔\n已经养了兔兔？\n再用一张许可证可以激活宠物交换计划！\n找到最搭你的兔兔！", "TeleportationPylonJungle": "{$CommonItemTooltip.TeleportationPylon}", "TeleportationPylonPurity": "{$CommonItemTooltip.TeleportationPylon}", "TeleportationPylonHallow": "{$CommonItemTooltip.TeleportationPylon}", "TeleportationPylonUnderground": "{$CommonItemTooltip.TeleportationPylon}", "TeleportationPylonOcean": "{$CommonItemTooltip.TeleportationPylon}", "TeleportationPylonDesert": "{$CommonItemTooltip.TeleportationPylon}", "TeleportationPylonSnow": "{$CommonItemTooltip.TeleportationPylon}", "TeleportationPylonMushroom": "{$CommonItemTooltip.TeleportationPylon}", "TerraToilet": "当真？你拿断裂英雄剑做这玩意？", "QueenSlimePetItem": "召唤史莱姆公主\n“适合皇后的甜点！”", "AccentSlab": "另一种版本的石板，和临近物块的融合方式有所不同\n深受高级建造者青睐", "TeleportationPylonVictory": "传送至另一个晶塔\n可以在任何地方发挥作用\n“你必须建造更多晶塔”", "RockGolemBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.RockGolem}", "BloodMummyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BloodMummy}", "SporeSkeletonBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SporeSkeleton}", "SporeBatBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.SporeBat}", "LarvaeAntlionBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.LarvaeAntlion}", "CrimsonBunnyBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CrimsonBunny}", "CrimsonGoldfishBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CrimsonGoldfish}", "CrimsonPenguinBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CrimsonPenguin}", "BigMimicCorruptionBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BigMimicCorruption}", "BigMimicCrimsonBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BigMimicCrimson}", "BigMimicHallowBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.BigMimicHallow}", "MossHornetBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.MossHornet}", "WanderingEyeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.WanderingEye}", "BlueCultistCasterBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.CultistDevote}", "CreativeWings": "{$CommonItemTooltip.FlightAndSlowfall}", "RainbowWings": "{$CommonItemTooltip.FlightAndSlowfall}\n{$CommonItemTooltip.PressUpToBooster}", "LongRainbowTrailWings": "{$CommonItemTooltip.FlightAndSlowfall}\n{$CommonItemTooltip.PressDownToHover}\n{$CommonItemTooltip.PressUpToBooster}\n“你懂得越多”", "DirtBomb": "会散布土的小爆炸", "DirtStickyBomb": "会散布土的小爆炸", "DiggingMoleMinecart": "如果携带有矿车轨道，会挖开物块并铺设新轨道\n只在地下挖掘", "CrystalNinjaHelmet": "暴击率提高5%\n魔力消耗降低10%", "CrystalNinjaChestplate": "伤害提高5%\n10%几率省下弹药", "CrystalNinjaLeggings": "移动速度提高20%\n近战速度提高10%", "QueenSlimeMountSaddle": "召唤可骑乘的羽翼史莱姆坐骑", "QueenSlimeBossBag": "{$CommonItemTooltip.RightClickToOpen}", "QueenSlimeCrystal": "召唤史莱姆皇后", "RocketLauncher": "会对直接命中的目标造成额外伤害", "ProximityMineLauncher": "部署好的雷会造成三倍伤害", "EmpressFlightBooster": "给予无限翅膀飞行时间和无限火箭靴飞行时间\n提高飞行和跳跃机动性", "Smolstar": "召唤附魔飞刀来为你战斗\n忽略敌人的大量防御\n消耗25%标记伤害快速击打\n“别被它们小小的个头给骗了”", "QueenSlimeHook": "将你传送至钩爪的位置", "VolatileGelatin": "定期释放能伤害敌人的挥发明胶", "TerrasparkBoots": "可飞行、飞速奔跑，并提供额外冰面行动力\n移动速度提高8%\n可让人在水、蜂蜜和熔岩上行走\n对火块免疫并在7秒内对熔岩免疫\n减少因触碰熔岩而受到的伤害", "EmpressButterfly": "“它的翅膀很娇嫩，一定要小心，不要伤到它……”", "BlandWhip": "4召唤标记伤害\n{$CommonItemTooltip.Whips}\n“该死的怪物！”", "MaceWhip": "8召唤标记伤害\n12%的召唤标记暴击率\n{$CommonItemTooltip.Whips}", "ScytheWhip": "{$CommonItemTooltip.Whips}\n用黑暗能量打击敌人以增加鞭子攻击速度\n被召唤物击中的敌人身上会跳出黑暗能量", "SwordWhip": "9召唤标记伤害\n{$CommonItemTooltip.Whips}\n打击敌人以增加鞭子攻击速度", "ThornWhip": "6召唤标记伤害\n{$CommonItemTooltip.Whips}\n打击敌人以增加鞭子攻击速度", "FireWhip": "{$CommonItemTooltip.Whips}\n用烈焰能量打击敌人\n被召唤物击中的敌人会爆出烈焰能量", "CoolWhip": "6召唤标记伤害\n{$CommonItemTooltip.Whips}\n打击敌人以召唤友好的雪花\n“让我来点冷酷的”", "RainbowWhip": "20召唤标记伤害\n10%召唤标记暴击率\n{$CommonItemTooltip.Whips}", "BadgersHat": "你的绿幕抠像好像有问题\n“非常适合冒充游戏主播！”", "ChippysCouch": "你~~好~~，泰拉瑞亚爱好者！\n“非常适合像主播那般休息！”", "ZapinatorGray": "“它可能坏了”", "ZapinatorOrange": "“它可能坏了”", "JoustingLance": "提升速度来加强攻击力\n“纳命来！”", "ShadowJoustingLance": "提升速度来加强攻击力", "HallowJoustingLance": "提升速度来加强攻击力", "StormTigerStaff": "召唤白虎来为你战斗", "EmpressBlade": "召唤附魔剑来为你战斗", "TreasureMagnet": "扩大物品的拾取范围", "Mace": "可以用火把升级", "FlamingMace": "“愿火焰照亮你前行的路”", "MoonLordLegs": "略微提高移动能力\n“这些可能是Steve的”", "Keybrand": "对受伤的敌人造成更多伤害", "SpiderSinkSpiderSinkDoesWhateverASpiderSinkDoes": "“蜘蛛水槽，蜘蛛水槽，蜘蛛能干的它都行……”", "SuperStarCannon": "“当然，天才，但你要去哪弄这么多星星呢？”", "DrumSet": "“咚咚，锵”", "ToiletCactus": "“你有多绝望？”", "GameMasterShirt": "“我记得告诉过你把房间收拾干净！”", "GameMasterPants": "“我记得告诉过你把房间收拾干净！”", "RollingCactus": "“它们讨厌……”", "RockLobster": "“但它不是岩石！”", "ShroomMinecart": "“蘑菇发出呜呜声”", "Gladius": "“你不觉得刺激吗？！”", "MeowmereMinecart": "“飙呜”", "PartyMinecart": "“大家快上派对矿车！”", "SteampunkMinecart": "“蒸汽动力！”", "ChefHat": "“这鸡肉是生的！”", "Fedora": "“*致意* 女士”", "PigronMinecart": "“培根动力！”", "QuadBarrelShotgun": "“当两管或三管未达预期时”", "SparkleGuitar": "“这些和弦简直太棒了”", "BouncingShield": "“这个我可以玩一整天”", "BunnyTail": "“这就是人们叫我桑普的原因”", "CombatWrench": "“‘修理’它们”", "FireproofBugNet": "“当事情变得棘手的时候使用”", "FlameWakerBoots": "“脚再也不冷了”", "RabbitOrder": "“看似一只兔兔，实际上真是一只兔兔”", "TruffleWormCage": "“现在跑不掉了……”", "GelBalloon": "“注满了派对女孩的洗澡水”", "Teacup": "{$CommonItemTooltip.MinorStats}\n“茶会必备品”", "MilkCarton": "{$CommonItemTooltip.MinorStats}\n“强健骨骼”", "CoffeeCup": "{$CommonItemTooltip.MediumStats}\n“你好，黑暗，我的老朋友”", "TorchGodsFavor": "解锁物品栏左边的能力切换按钮\n启用后，普通火把会随着生物群系而变化", "LavaproofTackleBag": "钓鱼线永远不会断，降低鱼饵消耗几率，渔力增加10\n可在熔岩中钓鱼", "MoltenHelmet": "近战暴击率提高7%", "MoltenBreastplate": "近战伤害提高7%", "MoltenGreaves": "近战速度提高7%", "ObsidianHelm": "召唤伤害提高8%", "ObsidianShirt": "仆从数量上限增加1", "ObsidianPants": "召唤伤害提高8%", "TimelessTravelerHood": "“愿时光有温柔的双手”\n“为旅行之人指引方向……”\n概念设计：DisRicardo", "TimelessTravelerRobe": "“……尽管旅程早已结束……”\n概念设计：DisRicardo", "TimelessTravelerBottom": "“……亘古通今！”\n概念设计：DisRicardo", "FloretProtectorHelmet": "“当宇宙射线穿透星云时，那奇妙的光芒……”\n概念设计：yikescloud", "FloretProtectorChestplate": "“编号170122 - RSS ZEPHYRUS III”\n概念设计：yikescloud", "FloretProtectorLegs": "“我是一只鸭，嘎嘎嘎”\n概念设计：yikescloud", "CapricornMask": "“充分体现星宿的威严，闪亮！”\n概念设计：R-MK", "CapricornChestplate": "“具有玻璃般的防护性！”\n概念设计：R-MK", "CapricornLegs": "“你现在可以穿鞋了！也许吧！”\n<right>可变身\n概念设计：R-MK", "CapricornTail": "“很遗憾，不提供游泳能力”\n<right>可变身\n概念设计：R-MK", "TVHeadMask": "“你看不见屏幕背后的我，我是半人半机器……”\n概念设计：Dr.<PERSON>it", "TVHeadSuit": "“生意能有多糟糕？”\n概念设计：Dr.Zootsuit", "TVHeadPants": "“我下不了决定……光面革还是绒面革？”\n概念设计：Dr.<PERSON>it", "FlinxFurCoat": "召唤伤害提高5%\n仆从数量上限增加1", "FlinxStaff": "召唤小雪怪来为你战斗", "FlinxFur": "“毛绒绒的！”", "PlaguebringerHelmet": "“由100%纯正阿罕卡拉制成，值得信赖！”\n概念设计：SodaHunter", "PlaguebringerChestplate": "“谁能想到一个制造瘟疫的世界终结者居然有这么好的时尚品味呢？”\n概念设计：SodaHunter", "PlaguebringerGreaves": "“这双靴子见证过太多恐怖了，或者也许应该说，践踏过。”\n概念设计：SodaHunter", "RoninHat": "“你最忠实的旅伴。光球柔软而热情。”\n概念设计：crowflux", "RoninShirt": "“年代异常久远，然而异乎寻常地耐用。天知道它为了找到你漂泊了多久。”\n概念设计：crowflux", "RoninPants": "“它不断提醒你，通往山顶的路不止一条。”\n概念设计：crowflux", "RainbowCursor": "可让你的鼠标光标具有不断变化的彩虹色", "BoneWhip": "7召唤标记伤害\n{$CommonItemTooltip.Whips}\n对抗多个目标时，比大多数鞭子表现更好\n“到十一了”", "Princess64": "{$PaintingArtist.Lazure}", "PaintingOfALass": "{$PaintingArtist.Lazure}", "DarkSideHallow": "{$PaintingArtist.Lazure}", "GlommerPetItem": "召唤咕噜咪\n“花瓣在阳光下闪闪发光”", "BerniePetItem": "召唤珍爱的泰迪熊\n“我儿时的伙伴——伯尼！”", "DeerclopsPetItem": "召唤小独眼巨鹿\n“你在看我吗？你是不是在看我？”", "PigPetItem": "召唤小猪人\n“真恶心。全都是毛。”", "ChesterPetItem": "召唤一只活宝箱来存放你的物品\n“它正在审视我的灵魂。”", "MonsterLasagna": "{$CommonItemTooltip.MediumStats}\n“味道像是面条配上毛和肉。”", "FroggleBunwich": "{$CommonItemTooltip.MediumStats}\n“味道有点刺激。”", "TentacleSpike": "将破坏性尖刺插入敌人\n“不要抓尖的一头。”", "LucyTheAxe": "“我爱露西！”", "HamBat": "你吃得越好，它的威力就越强大\n击败敌人后暂时提高治疗效果\n“这是用猪屁股做的武器！”", "BatBat": "击中时有可能治愈使用者\n“也许我可以用这个打些蝙蝠。”", "GarlandHat": "“既好闻又好看。”", "BoneHelm": "召唤暗影之手来攻击敌人\n“看到它我就头疼。”", "Eyebrella": "“别让雨淋到眼睛！”", "Magiluminescence": "提高移动速度和加速度\n穿戴时可提供照明\n“我黑暗生命中的一道短暂曙光。”", "WilsonBeardShort": "“这是人的面部毛发。”", "WilsonBeardLong": "“这是人的面部毛发。”", "WilsonBeardMagnificent": "“这是人的面部毛发。”", "DeerclopsBossBag": "{$CommonItemTooltip.RightClickToOpen}", "PewMaticHorn": "可将子弹转换成随机的东西\n“允许无线传输损伤！”", "WeatherPain": "忽略10点敌人防御\n“真棒……呵呵”", "HoundiusShootius": "{$CommonItemTooltip.Sentry}\n召唤奥术构造体，向敌人发射光爆\n“我的天哪，快看那只眼睛！”", "AbigailsFlower": "召唤友好鬼魂来为你战斗\n“它美得摄人心魄。”", "DontStarveShaderItem": "允许使用者以不同的方式看世界\n“禁忌的知识在其中回响……”", "DeerThing": "召唤独眼巨鹿", "PaintingWilson": "{$PaintingArt<PERSON>.<PERSON>}\n由<PERSON><PERSON> <PERSON><PERSON>改编", "PaintingWillow": "{$PaintingArt<PERSON>.<PERSON>}\n由<PERSON><PERSON> <PERSON><PERSON>改编", "PaintingWendy": "{$PaintingArt<PERSON>.<PERSON>}\n由<PERSON><PERSON> <PERSON><PERSON>改编", "PaintingWolfgang": "{$PaintingArt<PERSON>.<PERSON>}\n由<PERSON><PERSON> <PERSON><PERSON>改编", "HandOfCreation": "挖矿速度提高25%\n提高物块和墙的放置速度\n物块放置范围和工具范围扩大3格\n物品的拾取范围扩大\n自动给放置的物体上漆或涂料\n按住向上键可以够到更高的地方", "Musket": "“民兵说，我必须做好准备”", "WaspGun": "忽略10点敌人防御", "ResplendentDessert": "召唤两个皇家史莱姆\n“庆祝爱的甜点”", "BottledHoney": "短时间内提高自然恢复", "VioletMoss": "“难以忘怀的景象”", "RainbowMoss": "“难以忘怀的景象”", "LavaMoss": "“难以忘怀的景象”", "WandofFrosting": "射出一点寒霜", "DD2GoblinBomberBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2GoblinBomberT1}", "DD2GoblinBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2GoblinT1}", "DD2SkeletonBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2SkeletonT1}", "DD2DrakinBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2DrakinT2}", "DD2KoboldFlyerBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2KoboldFlyerT2}", "DD2KoboldBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2KoboldWalkerT2}", "DD2WitherBeastBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2WitherBeastT2}", "DD2WyvernBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2WyvernT1}", "DD2JavelinThrowerBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2JavelinstT1}", "DD2LightningBugBanner": "{$CommonItemTooltip.BannerBonusReduced}{$NPCName.DD2LightningBugT3}", "PortalGun": "创建一对可以穿越的传送门。\n用<left>和<right>放置传送门。\n提高动量和掉落速度。\n“快进快出。”", "Clentaminator2": "喷射时生成和摧毁生物群系\n使用彩色溶液\n33%几率省下弹药", "Outcast": "{$PaintingArt<PERSON>.<PERSON>}", "FairyGuides": "{$PaintingArtist.Sereni<PERSON>}", "AHorribleNightforAlchemy": "{$PaintingArtist.Altermaven}", "MorningHunt": "{$PaintingArt<PERSON>.<PERSON>}", "SuspiciouslySparkly": "{$Painting<PERSON>rt<PERSON>.<PERSON>}", "Requiem": "{$PaintingArt<PERSON>.<PERSON>}", "CatSword": "{$PaintingArtist.Redigit}", "KargohsSummon": "{$PaintingArt<PERSON>.<PERSON>}", "HighPitch": "{$PaintingArtist.Disc}", "AMachineforTerrarians": "{$PaintingArtist.Aurora}", "TerraBladeChronicles": "{$PaintingArtist.Sereni<PERSON>}", "BennyWarhol": "{$PaintingArtist.UnitOne}", "LizardKing": "{$PaintingArtist.Loki}\n“我就是这样一只蜥蜴，你都不知道我的真名……”", "MySon": "{$PaintingArtist.Leinfors}", "Duality": "{$PaintingArtist.Disc}", "ParsecPals": "{$PaintingArt<PERSON>.<PERSON>}", "RemnantsofDevotion": "{$PaintingArt<PERSON>.<PERSON>}", "NotSoLostInParadise": "{$Painting<PERSON>rt<PERSON>.<PERSON>}", "OcularResonance": "{$PaintingArt<PERSON>.<PERSON>}", "WingsofEvil": "{$PaintingArtist.Grox}", "Constellation": "{$PaintingArtist.Sigma}", "Eyezorhead": "{$PaintingArtist.Leinfors}", "DreadoftheRedSea": "{$PaintingArtist.Aurora}", "DoNotEattheVileMushroom": "{$PaintingArtist.<PERSON>}", "YuumaTheBlueTiger": "{$<PERSON><PERSON>rt<PERSON>.<PERSON>}", "MoonmanandCompany": "{$PaintingArtist.Zoomo}", "SunshineofIsrapony": "{$PaintingArt<PERSON>.<PERSON>}", "Purity": "{$PaintingArt<PERSON>.<PERSON>}", "SufficientlyAdvanced": "{$PaintingArtist.ManaUser}", "StrangeGrowth": "{$PaintingArtist.Sigma}", "HappyLittleTree": "{$PaintingArtist.Leinfors}\n“纪念R. N<PERSON> Ross”", "StrangeDeadFellows": "{$PaintingArtist.Leinfors}", "Secrets": "{$PaintingArt<PERSON>.<PERSON>}", "Thunderbolt": "{$PaintingArtist.Sereni<PERSON>}", "TheWerewolf": "{$Painting<PERSON>rt<PERSON>.<PERSON>}", "BlessingfromTheHeavens": "{$PaintingArtist.Sigma}", "LoveisintheTrashSlot": "{$PaintingArtist.<PERSON>}", "Fangs": "{$PaintingArtist.<PERSON>}", "HailtotheKing": "{$PaintingArtist.<PERSON>}", "Crustography": "{$PaintingArt<PERSON>.<PERSON>}", "StinkbugHousingBlocker": "放置在家中时，可防止城镇居民搬进来\n“闻起来像香菜”", "StinkbugHousingBlockerEcho": "放置在家中时，可防止城镇居民搬进来\n只在回声视觉下才能看到\n“闻起来像变质的香菜”", "SeeTheWorldForWhatItIs": "{$PaintingArt<PERSON>.<PERSON>}", "WhatLurksBelow": "{$PaintingArt<PERSON>.<PERSON>}", "ThisIsGettingOutOfHand": "{$PaintingArt<PERSON>.<PERSON>}", "Buddies": "{$PaintingArtist.Disc}", "MidnightSun": "{$PaintingArtist.Xman101}", "CouchGag": "{$PaintingArtist.Cenx}", "Wildflowers": "{$PaintingArtist.Sereni<PERSON>}", "VikingVoyage": "{$PaintingArt<PERSON>.<PERSON>}", "SilentFish": "{$PaintingArtist.Leinfors}\n“传说电池很久以前就没电了。”", "Bioluminescence": "{$PaintingArtist.Aurora}", "TheDuke": "{$PaintingArt<PERSON>.<PERSON>}", "RoyalRomance": "{$PaintingArtist.<PERSON>}", "Flymeal": "最适合用来整蛊城镇居民\n“让别人闻起来像香菜”", "LadyOfTheLake": "{$PaintingArt<PERSON>.<PERSON>}", "Bifrost": "{$PaintingArt<PERSON>.<PERSON>}", "Heartlands": "{$PaintingArt<PERSON>.<PERSON>}", "ForestTroll": "{$PaintingArt<PERSON>.<PERSON>}", "AuroraBorealis": "{$PaintingArt<PERSON>.<PERSON>}", "JojaCola": "{$CommonItemTooltip.MinorStats}\n“请回收利用”", "JunimoPetItem": "召唤祝尼魔\n“来自异界的神秘果实。味道如梦如幻……”", "SpicyPepper": "{$CommonItemTooltip.MinorStats}", "Pomegranate": "{$CommonItemTooltip.MinorStats}", "MoonGlobe": "抛出它可改变月亮的样子！\n“是时候换换风景了”", "BiomeSightPotion": "显示感染物块的位置", "FishingBobber": "渔力增加10", "FishingBobberGlowingStar": "渔力增加10\n你的浮标现在会发光", "FishingBobberGlowingLava": "渔力增加10\n你的浮标现在会发光", "FishingBobberGlowingKrypton": "渔力增加10\n你的浮标现在会发光", "FishingBobberGlowingXenon": "渔力增加10\n你的浮标现在会发光", "FishingBobberGlowingArgon": "渔力增加10\n你的浮标现在会发光", "FishingBobberGlowingViolet": "渔力增加10\n你的浮标现在会发光", "FishingBobberGlowingRainbow": "渔力增加10\n你的浮标现在会发光", "MiningShirt": "挖矿速度提高10%", "MiningPants": "挖矿速度提高10%", "Trimarang": "“三个回旋镖总比一个好”", "MushroomCampfire": "靠近篝火时生命再生提速", "EchoWall": "只在回声视觉下才能看到", "EchoPlatform": "只在回声视觉下才能看到", "HoneyAbsorbantSponge": "能够吸收无限多蜂蜜", "UltraAbsorbantSponge": "能够吸收无限多液体", "BottomlessHoneyBucket": "盛有无限多蜂蜜\n可以倒出", "WarTable": "<right>可拥有更多哨兵", "HiveFive": "攻击敌人后召唤杀人蜂", "ChlorophyteExtractinator": "将淤泥/雪泥/化石堆放入提炼机中可将其变成更有用的东西\n将受污染的物块放入提炼机可净化之\n放入其他物品可能会产生有趣的效果", "BlueEgg": "召唤蓝鸡\n“普普通通的蓝色鸡蛋”", "DontHurtNatureBook": "放在物品栏中时能防止你意外破坏环境\n<right>停用其效果", "MinecartPowerup": "永久提高矿车的速度并加装防御探测器\n“内含免费的机械货车！”", "WolfMountItem": "赋予穿戴者狼的力量", "PrincessStyle": "{$PaintingArt<PERSON>.<PERSON>}", "PlacePainting": "“泰拉瑞亚社区”", "DontHurtComboBook": "放在物品栏中时能防止你伤害小动物\n放在物品栏中时能防止你意外破坏环境\n<right>停用其效果", "AcornAxe": "让泥土上长出草\n用它进行采集可提高炼金植物的采集量\n砍树时会顺便种下橡实", "ArtisanLoaf": "使用后可永久扩大制作站范围\n“曾经让泰迪想家的传奇面包”", "ShadowCandle": "使城镇的和平收益化为乌有", "TNTBarrel": "高爆性", "ChestLock": "可用来锁住一些宝箱", "HorseshoeBundle": "可让持有者四连跳\n增加跳跃高度，消除掉落伤害", "Batfish": "“抓捕位置：地下和洞穴”", "BumblebeeTuna": "“抓捕位置：蜂蜜”", "Catfish": "“抓捕位置：丛林地表”", "Cloudfish": "“抓捕位置：天湖”", "Cursedfish": "“抓捕位置：腐化之地”", "Dirtfish": "“抓捕位置：地表和地下”", "DynamiteFish": "“抓捕位置：地表”", "EaterofPlankton": "“抓捕位置：腐化之地”", "FallenStarfish": "“抓捕位置：天湖和地表”", "TheFishofCthulu": "“抓捕位置：天湖和地表”", "Fishotron": "“抓捕位置：洞穴”", "Harpyfish": "“抓捕位置：天湖和地表”", "Hungerfish": "“抓捕位置：洞穴”", "Ichorfish": "“抓捕位置：猩红之地”", "Jewelfish": "“抓捕位置：地下和洞穴”", "MirageFish": "“抓捕位置：地下神圣之地”", "MutantFlinxfin": "“抓捕位置：地下苔原”", "Pengfish": "“抓捕位置：地表苔原”", "Pixiefish": "“抓捕位置：地表神圣之地”", "Spiderfish": "“抓捕位置：地下和洞穴”", "UnicornFish": "“抓捕位置：神圣之地”", "GuideVoodooFish": "“抓捕位置：洞穴”", "Wyverntail": "“抓捕位置：天湖”", "ZombieFish": "“抓捕位置：地表”", "AmanitaFungifin": "“抓捕位置：发光蘑菇地”", "Angelfish": "“抓捕位置：天湖”", "BloodyManowar": "“抓捕位置：猩红之地”", "Bonefish": "“抓捕位置：地下和洞穴”", "Bunnyfish": "“抓捕位置：地表”", "CapnTunabeard": "“抓捕位置：海洋”", "Clownfish": "“抓捕位置：海洋”", "DemonicHellfish": "“抓捕位置：洞穴”", "Derpfish": "“抓捕位置：丛林地表”", "Fishron": "“抓捕位置：地下苔原”", "InfectedScabbardfish": "“抓捕位置：腐化之地”", "Mudfish": "“抓捕位置：丛林”", "TropicalBarracuda": "“抓捕位置：丛林地表”", "TundraTrout": "“抓捕位置：地表苔原”", "Slimefish": "“抓捕位置：地表森林”", "ScarabFish": "“抓捕位置：沙漠”", "ScorpioFish": "“抓捕位置：沙漠”", "SpiffoPlush": "召唤浣熊Spiffo!", "GlowTulip": "召唤洞穴园丁\n“完全绽放的远古能量。”", "MechdusaSummon": "召唤 ???\n“你会后悔的”", "RodOfHarmony": "将你传送至鼠标位置", "RubblemakerSmall": "与材料一起使用可放置相应的碎石\n<right>切换放置大小\n按向上键/向下键可在样式之间循环切换\n“不是打桩机，也不是定位机：它是堆石器”", "RubblemakerMedium": "与材料一起使用可放置相应的碎石\n<right>切换放置大小\n按向上键/向下键可在样式之间循环切换\n“不是打桩机，也不是定位机：它是堆石器”", "RubblemakerLarge": "与材料一起使用可放置相应的碎石\n<right>切换放置大小\n按向上键/向下键可在样式之间循环切换\n“不是打桩机，也不是定位机：它是堆石器”", "CombatBookVolumeTwo": "增强所有城镇居民的防御力和力量\n“包含攻防战斗技术，卷二！”", "AegisCrystal": "永久加速生命再生", "AegisFruit": "永久提高防御力", "ArcaneCrystal": "永久提高魔力再生", "GalaxyPearl": "永久提高运气", "GummyWorm": "永久提高钓鱼技能", "Ambrosia": "永久提高挖矿速度和建造速度", "PeddlersSatchel": "永久增加旅商售卖的物品", "EchoCoating": "所涂覆的物体只在回声视觉下可见\n可以与任何其它漆或涂料组合使用\n“现在全都通透了。”", "GasTrap": "当容器打开时，释放有毒气体", "ReflectiveShades": "对黑暗和石化免疫", "EchoMonolith": "开启回声视觉，显示隐藏物块", "ShimmerMonolith": "“一窥以太之伟力。”\n可用来显现或掩匿以太", "ShimmerArrow": "向上坠落", "ShimmerCloak": "对微光相位化免疫\n当淹没在微光中时按住向下键可以相位化", "Shellphone": "显示所有信息\n可随意回家\n<right>切换目的地\n“仔细听，你会听到一些关于你汽车保修的事情”", "ShellphoneSpawn": "显示所有信息\n可随意返回生成点\n<right>切换目的地\n“仔细听，你会听到一些关于你汽车保修的事情”", "ShellphoneOcean": "显示所有信息\n可随意前往海洋\n<right>切换目的地\n“仔细听，你会听到一些关于你汽车保修的事情”", "ShellphoneHell": "显示所有信息\n可随意前往地狱\n<right>切换目的地\n“仔细听，你会听到一些关于你汽车保修的事情”", "ShellphoneDummy": "显示所有信息\n<right>切换目的地\n“仔细听，你会听到一些关于你汽车保修的事情”", "SpiderWallUnsafe": "一片全是卵的蜘蛛墙，会生成蜘蛛", "BlueBrickWallUnsafe": "一片被诅咒的地牢墙，会生成怪物", "BlueSlabWallUnsafe": "一片被诅咒的地牢墙，会生成怪物", "BlueTiledWallUnsafe": "一片被诅咒的地牢墙，会生成怪物", "PinkBrickWallUnsafe": "一片被诅咒的地牢墙，会生成怪物", "PinkSlabWallUnsafe": "一片被诅咒的地牢墙，会生成怪物", "PinkTiledWallUnsafe": "一片被诅咒的地牢墙，会生成怪物", "GreenBrickWallUnsafe": "一片被诅咒的地牢墙，会生成怪物", "GreenSlabWallUnsafe": "一片被诅咒的地牢墙，会生成怪物", "GreenTiledWallUnsafe": "一片被诅咒的地牢墙，会生成怪物", "BottomlessShimmerBucket": "盛有无限多微光\n可以倒出", "SandstoneWallUnsafe": "一片危险的沙墙，会生成怪物", "HardenedSandWallUnsafe": "一片危险的沙墙，会生成怪物", "LihzahrdWallUnsafe": "一片古老的神庙墙，会生成丛林蜥蜴", "SpelunkerFlare": "显露附近的宝藏", "Moondial": "每周允许将时间快进到黄昏一次", "ShimmerSlimeBanner": "{$CommonItemTooltip.BannerBonus}{$NPCName.ShimmerSlime}", "DizzyHat": "“Blue Traktor来了”\n“非常适合冒充游戏主播！”", "HoplitePizza": "{$PaintingArtist.Cheesy}", "SunOrnament": "{$PaintingArtist.Cheesy}", "LincolnsHoodie": "“Raynebro的财产”", "LincolnsHood": "“Raynebro的财产”", "LincolnsPants": "“Raynebro的财产”", "SandSolution": "由环境改造枪使用\n蔓延沙漠", "SnowSolution": "由环境改造枪使用\n蔓延雪地", "DirtSolution": "由环境改造枪使用\n蔓延森林", "LunarRustBrick": "{$CommonItemTooltip.LuminiteVariant}", "DarkCelestialBrick": "{$CommonItemTooltip.LuminiteVariant}", "AstraBrick": "{$CommonItemTooltip.LuminiteVariant}", "CosmicEmberBrick": "{$CommonItemTooltip.LuminiteVariant}", "CryocoreBrick": "{$CommonItemTooltip.LuminiteVariant}", "MercuryBrick": "{$CommonItemTooltip.LuminiteVariant}", "StarRoyaleBrick": "{$CommonItemTooltip.LuminiteVariant}", "HeavenforgeBrick": "{$CommonItemTooltip.LuminiteVariant}", "LunarRustBrickWall": "{$CommonItemTooltip.LuminiteVariant}", "DarkCelestialBrickWall": "{$CommonItemTooltip.LuminiteVariant}", "AstraBrickWall": "{$CommonItemTooltip.LuminiteVariant}", "CosmicEmberBrickWall": "{$CommonItemTooltip.LuminiteVariant}", "CryocoreBrickWall": "{$CommonItemTooltip.LuminiteVariant}", "MercuryBrickWall": "{$CommonItemTooltip.LuminiteVariant}", "StarRoyaleBrickWall": "{$CommonItemTooltip.LuminiteVariant}", "HeavenforgeBrickWall": "{$CommonItemTooltip.LuminiteVariant}", "DirtiestBlock": "“土量增加20%！”", "Fertilizer": "使树苗立即长成大树", "WaffleIron": "“现在是华夫饼时间！”", "JimsDrone": "向左/向右旋转可转向，按向上键可加速\n“飞行俱乐部的第一条规则：不谈论飞行俱乐部”", "JimsDroneVisor": "允许佩戴者通过无人机摄像头观看\n“矮胖猪龙鱼遥控系统”", "ShimmerCampfire": "靠近篝火时生命再生提速", "DontHurtCrittersBookInactive": "放在物品栏中时能防止你伤害小动物\n效果目前处于停用状态，<right>激活其效果", "DontHurtNatureBookInactive": "放在物品栏中时能防止你意外破坏环境\n效果目前处于停用状态，<right>激活其效果", "DontHurtComboBookInactive": "放在物品栏中时能防止你伤害小动物\n放在物品栏中时能防止你意外破坏环境\n效果目前处于停用状态，<right>激活其效果", "BonePickaxe": "“我得跟你挑个刺”"}}