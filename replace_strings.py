#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def load_json_file(filename):
    """加载JSON文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件 {filename} 时出错: {e}")
        return None

def save_json_file(filename, data):
    """保存JSON文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"文件 {filename} 保存成功")
    except Exception as e:
        print(f"保存文件 {filename} 时出错: {e}")

def replace_strings_in_items_cn():
    """
    遍历 items_parse.json 里面每个items_trans中的name和fullname在 items_cn.json 中查找有没有相同的字符串，
    如果有的话就把这些字符串替换成items_trans中的name_cn字段的字符串
    """

    # 加载 items_parse.json
    items_parse = load_json_file('items_parse.json')
    if items_parse is None:
        return

    # 加载 items_cn.json
    items_cn = load_json_file('items_cn.json')
    if items_cn is None:
        return

    print(f"加载了 {len(items_parse)} 个翻译项")
    print(f"items_cn.json 包含 {len(items_cn)} 个物品")

    # 创建替换映射表
    replacement_map = {}

    for item in items_parse:
        name = item.get('name', '')
        fullname = item.get('fullname', '')
        name_cn = item.get('name_cn', '')

        if name and name_cn:
            replacement_map[name] = name_cn
            # 添加"Any"开头的映射
            replacement_map[f"Any {name}"] = f"任何{name_cn}"

        if fullname and name_cn and fullname != name:
            replacement_map[fullname] = name_cn
            # 添加"Any"开头的映射
            replacement_map[f"Any {fullname}"] = f"任何{name_cn}"

    print(f"创建了 {len(replacement_map)} 个替换映射")

    # 统计替换次数
    replacement_count = 0

    # 遍历 items_cn.json 中的每个物品
    for item_key, item_data in items_cn.items():
        if isinstance(item_data, dict):
            # 递归替换字典中的所有字符串值
            replacement_count += replace_strings_in_dict(item_data, replacement_map)

    print(f"总共进行了 {replacement_count} 次字符串替换")

    # 保存修改后的 items_cn.json
    save_json_file('items_cn_updated.json', items_cn)

def replace_strings_in_dict(data, replacement_map):
    """递归替换字典中的字符串"""
    replacement_count = 0

    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, str):
                # 检查是否需要替换
                if value in replacement_map:
                    old_value = value
                    data[key] = replacement_map[value]
                    replacement_count += 1
                    print(f"替换: '{old_value}' -> '{replacement_map[value]}'")
            elif isinstance(value, (dict, list)):
                replacement_count += replace_strings_in_dict(value, replacement_map)
    elif isinstance(data, list):
        for i, item in enumerate(data):
            if isinstance(item, str):
                if item in replacement_map:
                    old_value = item
                    data[i] = replacement_map[item]
                    replacement_count += 1
                    print(f"替换: '{old_value}' -> '{replacement_map[item]}'")
            elif isinstance(item, (dict, list)):
                replacement_count += replace_strings_in_dict(item, replacement_map)

    return replacement_count

if __name__ == "__main__":
    replace_strings_in_items_cn()
