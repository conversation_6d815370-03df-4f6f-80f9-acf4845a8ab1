import requests
import json
import time
import os
#url https://cdn.jsdelivr.net/gh/BlackHopkins/ti@main/images/work_bench.webp
base_url = "https://cdn.jsdelivr.net/gh/BlackHopkins/ti@main/images/"

def process_name(name):
    """处理名称：转小写并将空格替换为下划线"""
    return name.lower().replace(" ", "_")

def download_image(url, filename):
    """下载图片并保存到images目录"""
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        # 确保images目录存在
        os.makedirs("images", exist_ok=True)
        
        # 保存图片
        with open(os.path.join("images", filename), 'wb') as f:
            f.write(response.content)
        print(f"成功下载: {filename}")
    except Exception as e:
        print(f"下载失败 {filename}: {str(e)}")

# 读取items.json文件
with open("items.json", "r", encoding="utf-8") as f:
    items_data = json.load(f)

# 遍历所有物品
for key, item in items_data.items():
    if "name" in item:
        # 处理名称并构建URL
        processed_name = process_name(item["name"])
        image_url = base_url + processed_name + ".webp"
        filename = processed_name + ".webp"
        
        # 下载图片
        download_image(image_url, filename)
        
        # 添加延迟，避免请求过于频繁
        time.sleep(0.5)  # 每次下载后等待1秒
