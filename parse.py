"""
items_trans.html里面包含的html字符串结构类似下面这样
<table class="terraria cellborder sortable">
    <tr>
        <th> 内部名称 </th>
        <th> 英文文本 </th>
        <th> 游戏内置中文文本 </th>
        <th> 本语言包文本</th>
    </tr>
    <tr>
        <th style="text-align:left"> BloodMoonMonolith </th>
        <td> Blood Moon Monolith </td>
        <td> 血月天塔柱 </td>
        <td> </td>
    </tr>
    <tr>
        <th style="text-align:left"> CrimstoneBrick </th>
        <td> Crimstone Brick </td>
        <td> 猩红石砖 </td>
        <td> </td>
    </tr>
    <tr>
        <th style="text-align:left"> CrimstoneBrickWall </th>
        <td> Crimstone Brick Wall </td>
        <td> 猩红石砖墙 </td>
        <td> </td>
    </tr>
    <tr>
        <th style="text-align:left"> DontHurtNatureBookInactive </th>
        <td> Guide to Environmental Preservation (Inactive) </td>
        <td> 环境保护指南（非活动） </td>
        <td> 环境保护指南（停用）</td>
    </tr>
    <tr>
        <th style="text-align:left"> DontHurtComboBookInactive </th>
        <td> Guide to Peaceful Coexistence (Inactive) </td>
        <td> 和平共处指南（非活动） </td>
        <td> 和平共处指南（停用）</td>
    </tr>
"""
#解析同目录下 items_trans.html的数据，生成新的json文件: items_parse.json
# 生成的格式：
# {
#     "name": "DontHurtComboBookInactive",
#     "name_cn": "和平共处指南（非活动）/ 和平共处指南（停用）",
#     "fullname": "Guide to Peaceful Coexistence (Inactive)",
# }

import re
import json

# 读取 items_trans.html 文件
with open('items_trans.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

# 使用正则表达式解析 HTML 内容
pattern = r'<tr>\s*<th style="text-align:left">(.*?)</th>\s*<td>(.*?)</td>\s*<td>(.*?)</td>\s*<td>(.*?)</td>\s*</tr>'

# 使用正则表达式查找所有匹配项
matches = re.findall(pattern, html_content, re.DOTALL)

# 创建一个列表来存储解析结果
parsed_data = []

# 遍历所有匹配项
for match in matches:
    name = match[0].strip()
    fullname = match[1].strip()
    name_cn = match[2].strip() + '/' + match[3].strip() if match[3].strip() else match[2].strip()
    parsed_data.append({
        "name": name,
        "name_cn": name_cn,
        "fullname": fullname,
    })

# 将解析结果写入 JSON 文件
with open('items_parse.json', 'w', encoding='utf-8') as f:
    json.dump(parsed_data, f, ensure_ascii=False, indent=4)

print("解析完成，结果已保存到 items_parse.json")






