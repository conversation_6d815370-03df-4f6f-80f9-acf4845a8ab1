import json
import re
import copy
import pprint # 导入 pprint

# 加载各种数据文件
try:
    with open('items.json', 'r', encoding='utf-8') as f:
        items_data = json.load(f)
    with open('items_trans.json', 'r', encoding='utf-8') as f:
        items_trans_data = json.load(f)
    # 新增：加载 npc.json
    with open('npc.json', 'r', encoding='utf-8') as f:
        npc_trans_data = json.load(f)
        # 确保 npc_trans_data 包含 'NPCName' 键
        if "NPCName" not in npc_trans_data or not isinstance(npc_trans_data["NPCName"], dict):
             print("警告：npc.json 文件缺少有效的 'NPCName' 字典。NPC 名称占位符将无法解析。")
             npc_trans_data["NPCName"] = {} # 创建一个空的，防止后续出错
    
    # 新增：从 items_parse.json 获取物品名称翻译
    with open('items_parse.json', 'r', encoding='utf-8') as f:
        items_parse_data = json.load(f)
        # 创建 fullname(小写) 到 name_cn 的映射字典，用于快速查找
        fullname_to_cn_dict = {}
        for item in items_parse_data:
            if "fullname" in item and "name_cn" in item and item["fullname"]:
                fullname_to_cn_dict[item["fullname"].lower()] = item["name_cn"]
        print(f"从 items_parse.json 加载了 {len(fullname_to_cn_dict)} 个物品名称的翻译")
except FileNotFoundError as e:
    print(f"错误：找不到文件 {e.filename}。请确保文件在正确的目录下。")
    # 可以选择退出或使用示例数据
    items_data = { "1": {"id": 1, "name": "Example Item", "tip": "Example tip"}} # 提供最小示例数据以避免后续错误
    items_trans_data = {"ItemName": {"ExampleItem": "示例物品"}, "ItemTooltip": {"ExampleItem": "示例提示"}}
    npc_trans_data = {"NPCName": {}}
    fullname_to_cn_dict = {} # 创建空字典
except json.JSONDecodeError as e:
    print(f"错误：解析 JSON 文件时出错 - {e}")
    items_data = { "1": {"id": 1, "name": "Example Item", "tip": "Example tip"}}
    items_trans_data = {"ItemName": {"ExampleItem": "示例物品"}, "ItemTooltip": {"ExampleItem": "示例提示"}}
    npc_trans_data = {"NPCName": {}}
    fullname_to_cn_dict = {} # 创建空字典


def normalize_name(name):
    """移除名称中的空格和非字母数字字符"""
    if not isinstance(name, str):
        return ""
    # 移除非字母数字字符 (保留字母和数字)
    normalized = re.sub(r'[^a-zA-Z0-9]', '', name)
    return normalized

def process_tip(tip_text, normalized_name, item_translations, npc_translations):
    """处理物品提示文本，查找翻译并替换占位符 (包括 NPC 名称)"""
    if not isinstance(tip_text, str):
        # 如果原始 tip 不是字符串，我们返回一个空字符串或者原始值
        # 返回空字符串可能更安全，避免后续处理非字符串类型
        return ""

    # 1. 查找 ItemTooltip 中的翻译
    # 如果 normalized_name 有效，则用它查找；否则没有基于名称的翻译
    translated_tip = "" # 默认为空字符串，表示没有找到翻译
    if normalized_name:
        translated_tip = item_translations.get("ItemTooltip", {}).get(normalized_name) # 只获取翻译，找不到则为 None

    # 如果没有找到基于 normalized_name 的翻译，或者 normalized_name 为空，
    # 则使用原始 tip_text 作为处理占位符的基础
    # 但最终 tip_cn 应该主要是翻译内容，所以如果找不到翻译，可以让 tip_cn 为空或包含占位符处理后的原始文本
    # 这里选择，如果找不到 ItemTooltip 翻译，则 tip_cn 为空，避免将英文原文放入 _cn 字段
    # 如果需要处理原始文本中的占位符并放入 tip_cn，则取消下一行注释
    # base_text_for_placeholder = translated_tip if translated_tip is not None else tip_text
    if translated_tip is None:
        # print(f"信息：物品 '{normalized_name}' 在 ItemTooltip 中没有直接翻译。")
        # 如果需要将占位符处理后的英文放入 tip_cn，可以取消下面注释
        # base_text_for_placeholder = tip_text
        # processed_tip = re.sub(r'\{\$([^\}]+)\}', replace_placeholder, base_text_for_placeholder)
        # return processed_tip
        return "" # 没有找到翻译，tip_cn 留空


    # 2. 处理占位符 {$...}
    def replace_placeholder(match):
        placeholder = match.group(1) # 例如 "CommonItemTooltip.DevItem"
        parts = placeholder.split('.')
        if len(parts) == 2:
            category, key = parts

            # *** 特殊处理 NPCName ***
            if category == "NPCName":
                npc_name_dict = npc_translations.get("NPCName") # 直接从传入的 npc_translations 获取
                if npc_name_dict: # 已在加载时检查过 npc_name_dict 是否为字典
                    replacement = npc_name_dict.get(key)
                    if replacement is not None:
                        # 检查 NPC 翻译本身是否包含 NPC 占位符 (简单一层检查)
                        # 例如 DD2GoblinT3: "{$NPCName.DD2GoblinT1}"
                        nested_match = re.match(r'\{\$NPCName\.([^}]+)\}', str(replacement))
                        if nested_match:
                            nested_key = nested_match.group(1)
                            nested_replacement = npc_name_dict.get(nested_key)
                            if nested_replacement is not None:
                                print(f"信息：解析嵌套 NPC 占位符 {replacement} -> {nested_replacement}")
                                return str(nested_replacement)
                            else:
                                print(f"警告：处理 Tip '{normalized_name}' 时找不到嵌套 NPCName '{nested_key}' 的翻译。占位符: {replacement}")
                                return str(replacement) # 返回包含未解析占位符的翻译
                        return str(replacement) # 返回找到的 NPC 名称翻译
                # 如果 npc_name_dict 为空或找不到 key
                print(f"警告：在 npc.json 的 NPCName 中找不到 '{key}' 的翻译。占位符: {match.group(0)}")
                return match.group(0) # 保留未翻译的占位符

            # *** 处理其他 item_translations 中的占位符 (如 CommonItemTooltip) ***
            else:
                category_dict = item_translations.get(category)
                if category_dict and isinstance(category_dict, dict):
                     replacement = category_dict.get(key)
                     if replacement is not None:
                         return str(replacement)
                # 如果在 item_translations 中找不到
                print(f"警告：在 item_translations 中找不到 '{category}.{key}' 的翻译。占位符: {match.group(0)}")
                return match.group(0) # 保留未翻译的占位符
        else:
            # 格式不符合 Category.Key
            print(f"警告：占位符格式错误，无法解析：{match.group(0)}")
            return match.group(0)

    # 对找到的 translated_tip 进行占位符替换
    processed_tip = re.sub(r'\{\$([^\}]+)\}', replace_placeholder, translated_tip)

    return processed_tip


# --- 主要处理逻辑 ---

if not isinstance(items_data, dict):
    print("错误：items_data 不是有效的字典格式。无法继续处理。")
    modified_items = {}
else:
    modified_items = copy.deepcopy(items_data)

    for item_id, item_details in modified_items.items():
        if not isinstance(item_details, dict):
            # print(f"警告：物品 ID '{item_id}' 的值不是字典，已跳过处理。值为: {item_details}")
            continue

        original_name = item_details.get("name")
        original_tip = item_details.get("tip")
        normalized = ""
        translated_name = "" # 初始化翻译后的名称
        processed_tooltip = "" # 初始化处理后的提示

        if original_name:
            # 将 name 转为小写，与 fullname_to_cn_dict 中的 key (fullname小写) 比较
            lowercase_name = original_name.lower()
            translated_name = fullname_to_cn_dict.get(lowercase_name)
            
            # 如果直接查找找不到，尝试规范化后查找 (从原先的 items_trans_data 查找方式)
            if translated_name is None:
                normalized = normalize_name(original_name)
                translated_name = items_trans_data.get("ItemName", {}).get(normalized)
                
                # 如果仍然没有找到，设为空字符串
                if translated_name is None:
                    # print(f"信息：物品 '{original_name}' 在 items_parse.json 和 items_trans.json 中都没有翻译")
                    translated_name = "" 
        else:
            # print(f"警告：物品 ID '{item_id}' 缺少 'name' 字段。")
            pass # name 缺失，无法进行翻译

        # 调用 process_tip，传入 item 和 npc 的翻译数据
        processed_tooltip = process_tip(original_tip, normalized, items_trans_data, npc_trans_data)

        # 添加 name_cn 字段，即使翻译为空也添加该字段
        modified_items[item_id]["name_cn"] = translated_name

        # 添加 tip_cn 字段，即使处理后的提示为空也添加该字段
        modified_items[item_id]["tip_cn"] = processed_tooltip


# --- 输出结果 ---
# print("\n--- 修改后的 items 数据 ---")
# pprint.pprint(modified_items)
# print("\n--- 原始 items 数据 (验证未被修改) ---")
# pprint.pprint(items_data)

# 将结果保存到新的 JSON 文件
try:
    with open('items_processed.json', 'w', encoding='utf-8') as outfile:
        json.dump(modified_items, outfile, ensure_ascii=False, indent=4)
    print("\n处理完成，结果已保存到 items_processed.json")
except Exception as e:
    print(f"\n保存文件时出错: {e}")
