import json
import time
import random
import requests
from bs4 import BeautifulSoup
import re
import os
import argparse
from urllib.parse import urljoin
# 不再需要lxml.etree

def fetch_page(url, retries=3, timeout=15):
    """
    获取页面内容，支持重试机制

    Args:
        url: 要获取的页面URL
        retries: 最大重试次数，默认为3
        timeout: 请求超时时间（秒），默认为15

    Returns:
        str: 页面内容文本，如果获取失败则返回None
    """
    for attempt in range(retries):
        try:
            # 增加随机延迟，避免请求过于频繁
            if attempt > 0:
                delay = 1 + random.random() * 2  # 1-3秒的随机延迟
                print(f"重试 {attempt+1}/{retries}，等待 {delay:.2f} 秒...")
                time.sleep(delay)

            # 使用最基本的requests请求
            response = requests.get(url, timeout=timeout)
            response.raise_for_status()

            # 确保正确设置编码
            if response.encoding == 'ISO-8859-1':
                # 网站可能使用了UTF-8但被错误识别为ISO-8859-1
                response.encoding = 'utf-8'

            # 打印响应状态和URL，便于调试
            print(f"成功获取页面: {url} (状态码: {response.status_code}, 编码: {response.encoding})")

            return response.text
        except requests.RequestException as e:
            print(f"获取页面失败: {url} (尝试 {attempt+1}/{retries}): {e}")
            if attempt == retries - 1:  # 最后一次尝试
                print(f"达到最大重试次数 {retries}，放弃获取页面: {url}")
                return None

def extract_english_name(img_src):
    """
    从图片URL提取英文名，处理形如 https://terraria.wiki.gg/images/8/8a/Bottomless_Shimmer_Bucket.png?894a05 的URL
    同时支持.png和.gif文件

    Args:
        img_src: 图片URL

    Returns:
        str: 提取的英文名称，例如 Bottomless_Shimmer_Bucket
    """
    # 先移除URL参数（问号后面的部分）
    img_src = img_src.split('?')[0]
    # 提取文件名部分，先尝试匹配.png文件
    match = re.search(r'/([^/]+)\.png$', img_src)
    if match:
        return match.group(1)
    # 如果不是.png文件，尝试匹配.gif文件
    match = re.search(r'/([^/]+)\.gif$', img_src)
    return match.group(1) if match else ""

def load_checkpoint(checkpoint_file="recipes_checkpoint.json"):
    """加载检查点文件，恢复爬取进度"""
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint = json.load(f)
                print(f"Loaded checkpoint with {len(checkpoint['recipes'])} recipes and {len(checkpoint['processed_pages'])} processed pages")
                return checkpoint
        except Exception as e:
            print(f"Error loading checkpoint: {e}")
    return None

def save_checkpoint(recipes, processed_pages, processed_urls, checkpoint_file="recipes_checkpoint.json"):
    """保存当前爬取进度到检查点文件"""
    checkpoint = {
        "recipes": recipes,
        "processed_pages": processed_pages,
        "processed_urls": processed_urls
    }
    with open(checkpoint_file, 'w', encoding='utf-8') as f:
        json.dump(checkpoint, f, ensure_ascii=False)
    print(f"Saved checkpoint with {len(recipes)} recipes and {len(processed_pages)} processed pages")

def scrape_recipes(resume=True, checkpoint_file="recipes_checkpoint.json", max_retries=3):
    """
    爬取泰拉瑞亚的合成配方，支持断点续爬

    Args:
        resume: 是否从检查点恢复爬取，默认为True
        checkpoint_file: 检查点文件路径，默认为"recipes_checkpoint.json"
        max_retries: 请求失败时的最大重试次数，默认为3

    Returns:
        list: 包含所有配方信息的列表
    """
    base_url = "https://terraria.wiki.gg"
    main_url = f"{base_url}/zh/wiki/%E9%85%8D%E6%96%B9"

    # 添加随机延迟，避免被反爬
    def random_delay(min_delay=0.5, max_delay=2.0):
        """生成随机延迟并等待"""
        delay = min_delay + random.random() * (max_delay - min_delay)
        print(f"等待 {delay:.2f} 秒...")
        time.sleep(delay)

    # 检查是否有检查点文件并加载
    checkpoint = None
    if resume:
        checkpoint = load_checkpoint(checkpoint_file)
        if checkpoint:
            print(f"成功加载检查点文件: {checkpoint_file}")
        else:
            print(f"未找到有效的检查点文件，将从头开始爬取")

    # 如果有检查点且选择恢复，则从检查点恢复
    if checkpoint and resume:
        recipes = checkpoint["recipes"]
        processed_pages = checkpoint["processed_pages"]
        processed_urls = checkpoint.get("processed_urls", [])  # 兼容旧版检查点
        print(f"从检查点恢复爬取，已收集 {len(recipes)} 个配方，已处理 {len(processed_urls)} 个子页面")
    else:
        recipes = []
        processed_pages = []  # 已处理的页面URL
        processed_urls = []   # 已处理的子页面URL
        print("开始新的爬取任务")

    # 获取主页面（如果尚未处理）
    if main_url not in processed_pages:
        print(f"获取主页面: {main_url}")
        main_html = fetch_page(main_url, retries=max_retries)
        if not main_html:
            print("获取主页面失败，终止爬取")
            return recipes

        # 添加随机延迟，避免被反爬
        random_delay()

        # 使用BeautifulSoup解析HTML
        print("使用BeautifulSoup解析主页面")
        main_soup = BeautifulSoup(main_html, 'html.parser')

        # 查找所有包含"查看此数据"文本的<a>标签
        view_data_links = main_soup.find_all('a', string='查看此数据')
        if not view_data_links:
            # 如果没有找到精确匹配，尝试部分匹配
            view_data_links = []
            for a in main_soup.find_all('a'):
                if a.string and '查看此数据' in a.string:
                    view_data_links.append(a)

        print(f"找到 {len(view_data_links)} 个'查看此数据'链接")

        # 构建子页面URL
        sub_urls = []
        for link in view_data_links:
            href = link.get('href')
            if href:
                sub_url = urljoin(base_url, href)
                sub_urls.append(sub_url)
                print(f"找到子页面: {sub_url}")

        # 将主页面添加到已处理列表
        processed_pages.append(main_url)

        # 保存检查点
        save_checkpoint(recipes, processed_pages, processed_urls, checkpoint_file)
        print(f"主页面处理完成，找到 {len(sub_urls)} 个子页面")
    else:
        # 如果主页面已处理，则从检查点恢复子页面URL
        print(f"主页面已处理，重新获取以更新子页面列表")
        main_html = fetch_page(main_url, retries=max_retries)
        if not main_html:
            print("警告: 无法重新获取主页面，将使用检查点中的子页面列表")
            # 如果无法获取主页面，但已有检查点，继续使用检查点中的数据
            if not processed_urls:
                print("错误: 无法获取主页面且检查点中没有子页面信息，终止爬取")
                return recipes
            sub_urls = []
            print(f"从检查点恢复 {len(processed_urls)} 个已处理的子页面URL")
        else:
            # 添加随机延迟，避免被反爬
            random_delay()

            # 使用BeautifulSoup解析HTML
            main_soup = BeautifulSoup(main_html, 'html.parser')

            # 查找所有包含"查看此数据"文本的<a>标签
            view_data_links = main_soup.find_all('a', string='查看此数据')
            if not view_data_links:
                # 如果没有找到精确匹配，尝试部分匹配
                view_data_links = []
                for a in main_soup.find_all('a'):
                    if a.string and '查看此数据' in a.string:
                        view_data_links.append(a)

            # 构建子页面URL
            sub_urls = []
            for link in view_data_links:
                href = link.get('href')
                if href:
                    sub_url = urljoin(base_url, href)
                    sub_urls.append(sub_url)

            print(f"更新子页面列表，找到 {len(sub_urls)} 个子页面")

    # 处理所有子页面
    total_subpages = len(sub_urls)
    print(f"开始处理 {total_subpages} 个子页面")

    for index, sub_url in enumerate(sub_urls):
        if sub_url in processed_urls:
            print(f"跳过已处理的子页面 [{index+1}/{total_subpages}]: {sub_url}")
            continue

        print(f"获取子页面 [{index+1}/{total_subpages}]: {sub_url}")

        # 下载并保存子页面
        success, filepath, sub_html, made_request = download_subpage(sub_url, retries=max_retries)
        if not success or not sub_html:
            print(f"获取子页面失败，跳过: {sub_url}")
            continue

        # 如果进行了网络请求，添加随机延迟
        if made_request:
            random_delay()

        if filepath:
            print(f"使用本地子页面文件: {filepath}")

        # 使用BeautifulSoup解析HTML
        print(f"解析子页面: {sub_url}")
        sub_soup = BeautifulSoup(sub_html, 'html.parser')

        # 检查页面标题，如果包含需要跳过的平台名称，则跳过该页面
        title = sub_soup.find('h1', id='firstHeading')
        if title:
            title_text = title.text.strip()
            if should_skip_page(title_text):
                print(f"跳过特定平台的页面: {title_text}")
                processed_urls.append(sub_url)  # 标记为已处理，避免下次重复处理
                continue

        # 查找配方表格，使用正确的类名
        recipe_tables = []

        # 首先查找所有带有 'terraria cellborder recipes sortable' 类的表格
        # 这些通常是包含配方的表格，每个表格可能有不同的制作站
        recipe_tables = sub_soup.find_all('table', class_='terraria cellborder recipes sortable')
        if recipe_tables:
            print(f"找到 {len(recipe_tables)} 个配方表格，类名: terraria cellborder recipes sortable")

        # 如果没有找到特定类名的表格，尝试查找任何terraria类的表格
        if not recipe_tables:
            terraria_tables = sub_soup.find_all('table', class_='terraria')
            if terraria_tables:
                print(f"找到 {len(terraria_tables)} 个terraria表格")
                recipe_tables = terraria_tables

        # 如果仍然没有找到，尝试其他方式查找表格
        if not recipe_tables:
            tables = sub_soup.find_all('table')
            print(f"找到 {len(tables)} 个表格")

            for table in tables:
                class_attr = table.get('class', [])
                if class_attr and any(cls in class_attr for cls in ['recipes', 'sortable', 'terraria']):
                    recipe_tables.append(table)
                    print(f"找到配方表格，类名: {class_attr}")

            if not recipe_tables:
                print(f"在 {sub_url} 中未找到配方表格")

        # 如果没有找到配方表格，尝试使用其他方法
        if not recipe_tables:
            # 筛选出配方表格（通常包含特定的列结构）
            for table in tables:
                rows = table.find_all('tr')
                if len(rows) <= 1:  # 跳过只有表头的表格
                    continue

                # 检查表格是否有足够的列来表示配方
                first_data_row = None
                for row in rows:
                    if row.find('th'):  # 跳过表头行
                        continue
                    first_data_row = row
                    break

                if not first_data_row:
                    continue

                cols = first_data_row.find_all(['td', 'th'])
                if len(cols) >= 2:  # 至少需要2列：产物、材料
                    recipe_tables.append(table)
                    print(f"找到配方表格，列数: {len(cols)}")

        print(f"在 {sub_url} 中找到 {len(recipe_tables)} 个配方表格")

        # 处理每个配方表格
        for table_idx, table in enumerate(recipe_tables):
            print(f"处理表格 {table_idx+1}/{len(recipe_tables)}, 页面: {sub_url}")

            # 提前获取制作站信息，用于日志
            caption = table.find('caption')
            if caption:
                print(f"表格 {table_idx+1} 的caption: {caption.text.strip()}")
                station_info = extract_crafting_station_from_caption(caption)
                if station_info["name_cn"]:
                    print(f"表格 {table_idx+1} 的制作站: {station_info['name_cn']}")

            rows = table.find_all('tr')
            print(f"表格 {table_idx+1} 有 {len(rows)} 行")

            # 确定表头行
            header_row = None
            for row in rows:
                if row.find('th'):
                    header_row = row
                    break

            if header_row:
                print(f"找到表头行: {header_row.text.strip()}")
            else:
                print("未找到表头行")

            # 跳过表头行，处理数据行
            data_rows = [row for row in rows if row != header_row]
            print(f"找到 {len(data_rows)} 个数据行")

            for row_idx, row in enumerate(data_rows):
                if row_idx % 10 == 0 and row_idx > 0:
                    print(f"处理第 {row_idx+1}/{len(data_rows)} 行，表格 {table_idx+1}")
                    # 每处理10行保存一次检查点
                    save_checkpoint(recipes, processed_pages, processed_urls, checkpoint_file)

                try:
                    # 获取产物单元格和材料单元格
                    result_cell = row.find('td', class_='result')
                    ingredients_cell = row.find('td', class_='ingredients')

                    if not result_cell or not ingredients_cell:
                        # 如果没有找到特定类名的单元格，尝试使用位置
                        cols = row.find_all('td')
                        if len(cols) < 2:
                            continue
                        result_cell = cols[0]
                        ingredients_cell = cols[1]

                    # 解析产物信息
                    result_info = parse_result(result_cell)
                    if not result_info["name_cn"]:
                        continue

                    # 检查产品名称是否包含需要跳过的特定字符串
                    if should_skip_product(result_info["name_cn"]):
                        print(f"跳过包含特定字符串的产品: {result_info['name_cn']}")
                        continue

                    # 解析材料信息
                    ingredients_info = parse_ingredients(ingredients_cell)

                    # 从表格的caption中获取制作站信息
                    caption = table.find('caption')
                    crafting_station = extract_crafting_station_from_caption(caption)

                    # 如果从caption中没有获取到制作站信息，尝试从页面标题中获取
                    if not crafting_station["name_cn"]:
                        title = sub_soup.find('h1', id='firstHeading')
                        if title:
                            title_text = title.text.strip()
                            # 确保不是特定平台的页面（这里仍然保留页面级别的检查）
                            if not should_skip_page(title_text):
                                # 从标题中提取制作站名称
                                if '/' in title_text:
                                    station_name = title_text.split('/')[-1].strip()
                                    crafting_station["name_cn"] = station_name

                    # 打印找到的制作站信息
                    if "combined_name" in crafting_station:
                        print(f"找到组合制作站: {crafting_station['combined_name']}")
                    elif crafting_station["name_cn"]:
                        print(f"找到制作站: {crafting_station['name_cn']}")

                    # 如果有额外的制作站，记录它们
                    if "additional_stations" in crafting_station and crafting_station["additional_stations"]:
                        print(f"额外制作站数量: {len(crafting_station['additional_stations'])}")
                        for idx, station in enumerate(crafting_station["additional_stations"]):
                            print(f"  额外制作站 {idx+1}: {station['name_cn']}")

                    # 构造配方对象
                    recipe = {
                        "result": result_info,
                        "ingredients": ingredients_info,
                        # "source_url": sub_url  # 记录数据来源页面
                    }

                    # 添加制作站信息
                    if "combined_name" in crafting_station:
                        # 如果有组合制作站，使用组合名称
                        # 创建主制作站的副本，不包含additional_stations字段
                        main_station = {
                            "name_cn": crafting_station["name_cn"],
                            "name_en": crafting_station["name_en"],
                            # "image_url": crafting_station["image_url"]
                        }

                        recipe["crafting_station"] = {
                            "name_cn": crafting_station["combined_name"],
                            "name_en": crafting_station["name_en"],
                            # "image_url": crafting_station["image_url"],
                            "is_combined": True,
                            "stations": [main_station] + crafting_station["additional_stations"]
                        }
                    else:
                        # 使用单个制作站
                        recipe["crafting_station"] = {
                            "name_cn": crafting_station["name_cn"],
                            "name_en": crafting_station["name_en"],
                            # "image_url": crafting_station["image_url"]
                        }

                    recipes.append(recipe)
                    print(f"添加配方: {result_info['name_cn']}")
                except Exception as e:
                    print(f"处理第 {row_idx+1} 行时出错: {e}")

        # 标记页面为已处理
        processed_urls.append(sub_url)
        # 保存检查点
        save_checkpoint(recipes, processed_pages, processed_urls, checkpoint_file)

    # 爬取完成后，保存最终结果并删除检查点文件
    print(f"爬取完成，共收集 {len(recipes)} 个配方")

    # 只有在成功爬取完所有页面后才删除检查点文件
    if len(processed_urls) >= len(sub_urls) and os.path.exists(checkpoint_file):
        os.remove(checkpoint_file)
        print(f"所有页面已处理完毕，检查点文件已删除")
    else:
        print(f"部分页面未处理完毕，保留检查点文件以便下次继续爬取")
        print(f"已处理 {len(processed_urls)}/{len(sub_urls)} 个页面")
        # 保存最终检查点
        save_checkpoint(recipes, processed_pages, processed_urls, checkpoint_file)

    return recipes

# 解析产物单元格，提取名称、图片和数量
def parse_result(result_cell):
    """
    优化的产物解析函数，从表格单元格中提取产物信息

    Args:
        result_cell: BeautifulSoup对象，表示产物单元格

    Returns:
        dict: 包含产物中文名、英文名、数量和图片URL的字典
    """
    result = {
        "name_cn": "",
        "name_en": "",
        "quantity": "1",  # 默认数量为1
        # "image_url": ""
    }

    if not result_cell:
        return result

    img = result_cell.find('img')
    if img:
        if 'src' in img.attrs:
            img_src = img['src']
            if img_src.startswith('//'):
                img_src = 'https:' + img_src
            elif not img_src.startswith(('http://', 'https://')):
                img_src = 'https://terraria.wiki.gg' + ('' if img_src.startswith('/') else '/') + img_src

            # result["image_url"] = img_src
            result["name_en"] = extract_english_name(img_src)

        if 'alt' in img.attrs and img['alt'].strip():
            result["name_cn"] = img['alt'].strip()

    if not result["name_cn"]:
        item_span = result_cell.find('span', class_='i')
        if not item_span:
            item_span = result_cell.find('span')

        if item_span:
            link = item_span.find('a')
            if link:
                title = link.get('title', '').strip()
                if title:
                    result["name_cn"] = title
                else:
                    result["name_cn"] = link.text.strip()
            else:
                result["name_cn"] = item_span.text.strip()

    quantity_span = result_cell.find('span', class_='am')
    if quantity_span and quantity_span.text.strip():
        result["quantity"] = quantity_span.text.strip()
    else:
        for text in result_cell.stripped_strings:
            if text.isdigit():
                result["quantity"] = text
                break
            elif text.startswith('x') and text[1:].isdigit():
                result["quantity"] = text[1:]
                break

    return result

# 解析材料单元格，提取所有材料的名称、图片和数量
def parse_ingredients(ingredients_cell):
    """
    优化的材料解析函数，从表格单元格中提取所有材料信息

    Args:
        ingredients_cell: BeautifulSoup对象，表示材料单元格

    Returns:
        list: 包含所有材料信息的列表，每个材料是一个字典
    """
    ingredients = []

    if not ingredients_cell:
        return ingredients

    # 查找所有材料项 - 通常在li元素中
    material_items = ingredients_cell.find_all('li')

    # 如果找到了li元素，处理每个li
    if material_items:
        for item in material_items:
            ingredient = extract_ingredient_info(item)
            if ingredient["name_cn"]:  # 只添加有名称的材料
                ingredients.append(ingredient)
    else:
        # 如果没有找到li元素，尝试其他结构

        # 1. 检查是否有ul元素
        ul = ingredients_cell.find('ul')
        if ul:
            for li in ul.find_all('li', recursive=False):
                ingredient = extract_ingredient_info(li)
                if ingredient["name_cn"]:
                    ingredients.append(ingredient)
        else:
            # 2. 检查是否有div元素
            divs = ingredients_cell.find_all('div')
            if divs:
                for div in divs:
                    ingredient = extract_ingredient_info(div)
                    if ingredient["name_cn"]:
                        ingredients.append(ingredient)
            else:
                # 3. 如果没有明确的结构，尝试直接从单元格提取信息
                ingredient = extract_ingredient_info(ingredients_cell)
                if ingredient["name_cn"]:
                    ingredients.append(ingredient)

    return ingredients

def extract_ingredient_info(element):
    """
    从HTML元素中提取材料信息

    Args:
        element: BeautifulSoup对象，表示包含材料信息的HTML元素

    Returns:
        dict: 包含材料中文名、英文名、数量和图片URL的字典
    """
    ingredient = {
        "name_cn": "",
        "name_en": "",
        "quantity": "1",  # 默认数量为1
        # "image_url": ""
    }

    img = element.find('img')
    if img:
        if 'src' in img.attrs:
            img_src = img['src']
            if img_src.startswith('//'):
                img_src = 'https:' + img_src
            elif not img_src.startswith(('http://', 'https://')):
                img_src = 'https://terraria.wiki.gg' + ('' if img_src.startswith('/') else '/') + img_src

            # ingredient["image_url"] = img_src
            ingredient["name_en"] = extract_english_name(img_src)

        if 'alt' in img.attrs and img['alt'].strip():
            ingredient["name_cn"] = img['alt'].strip()

    if not ingredient["name_cn"]:
        item_span = element.find('span', class_='i')
        if item_span:
            link = item_span.find('a')
            if link:
                title = link.get('title', '').strip()
                if title:
                    ingredient["name_cn"] = title
                else:
                    ingredient["name_cn"] = link.text.strip()
            else:
                ingredient["name_cn"] = item_span.text.strip()

    if not ingredient["name_cn"]:
        link = element.find('a')
        if link:
            title = link.get('title', '').strip()
            if title:
                ingredient["name_cn"] = title
            else:
                ingredient["name_cn"] = link.text.strip()

    quantity_span = element.find('span', class_='am')
    if quantity_span and quantity_span.text.strip():
        ingredient["quantity"] = quantity_span.text.strip()
    else:
        text = element.text.strip()
        if text:
            quantity_match = re.match(r'^(\d+)\s+(.+)$', text)
            if quantity_match:
                ingredient["quantity"] = quantity_match.group(1)
                if not ingredient["name_cn"]:
                    ingredient["name_cn"] = quantity_match.group(2)
            elif 'x' in text.lower():
                parts = text.lower().split('x')
                if len(parts) > 1 and parts[1].strip().isdigit():
                    ingredient["quantity"] = parts[1].strip()

    if not ingredient["name_cn"]:
        text = element.text.strip()
        if text:
            parts = text.split(None, 1)
            if len(parts) > 1 and parts[0].isdigit():
                ingredient["name_cn"] = parts[1]
            else:
                ingredient["name_cn"] = text

    return ingredient

def ensure_subpage_dir():
    """
    确保subpage目录存在，如果不存在则创建

    Returns:
        str: subpage目录的路径
    """
    subpage_dir = os.path.join(os.getcwd(), "subpage")
    if not os.path.exists(subpage_dir):
        os.makedirs(subpage_dir)
        print(f"创建subpage目录: {subpage_dir}")
    return subpage_dir

def get_subpage_filename(url):
    """
    从URL生成子页面文件名

    Args:
        url: 子页面URL

    Returns:
        str: 生成的文件名
    """
    # 从URL中提取唯一标识符
    # 移除协议和域名部分
    path = url.split('://')[-1].split('/', 1)[-1]
    # 替换不适合作为文件名的字符
    filename = re.sub(r'[\\/:*?"<>|]', '_', path)
    # 确保文件名不超过255个字符
    if len(filename) > 240:
        filename = filename[-240:]
    return f"{filename}.html"

def should_skip_page(title_text):
    """
    检查页面标题是否包含需要跳过的平台名称

    跳过的平台名称包括：
    - 前代主机版
    - 电脑版
    - 主机板
    - 任天堂3DS版
    - 移动版
    - 日本主机版

    Args:
        title_text: 页面标题文本

    Returns:
        bool: 如果页面标题包含需要跳过的平台名称，返回True；否则返回False
    """
    skip_platforms = ["前代主机版", "电脑版", "主机板", "任天堂3DS版", "移动版", "日本主机版"]
    for platform in skip_platforms:
        if platform in title_text:
            return True
    return False

def should_skip_product(product_name):
    """
    检查产品名称是否包含需要跳过的特定字符串

    跳过的产品名称包括：
    - 前代主机版
    - 电脑版
    - 主机板
    - 任天堂3DS版
    - 移动版
    - 日本主机版

    Args:
        product_name: 产品名称

    Returns:
        bool: 如果产品名称包含需要跳过的字符串，返回True；否则返回False
    """
    skip_product_names = ["前代主机版", "电脑版", "主机板", "任天堂3DS版", "移动版", "日本主机版"]
    for skip_name in skip_product_names:
        if skip_name in product_name:
            return True
    return False

def extract_crafting_station_from_caption(caption):
    """
    从表格的caption中提取制作站信息，优化版本

    Args:
        caption: BeautifulSoup对象，表示表格的caption

    Returns:
        dict: 包含制作站中文名、英文名和图片URL的字典
    """
    crafting_station = {
        "name_cn": "",
        "name_en": "",
        # "image_url": "",
        "additional_stations": []
    }

    if not caption:
        return crafting_station

    caption_text = caption.text.strip()
    print(f"处理caption: {caption_text}")

    all_images = caption.find_all('img')
    image_urls = []
    for img in all_images:
        if 'src' in img.attrs:
            img_src = img['src']
            if img_src.startswith('//'):
                img_src = 'https:' + img_src
            elif not img_src.startswith(('http://', 'https://')):
                img_src = 'https://terraria.wiki.gg' + ('' if img_src.startswith('/') else '/') + img_src
            image_urls.append(img_src)

    print(f"找到 {len(image_urls)} 个图片URL")

    station_links = caption.find_all('a')
    if not station_links:
        if image_urls:
            crafting_station["image_url"] = image_urls[0]
            crafting_station["name_en"] = extract_english_name(image_urls[0])
        return crafting_station

    has_multiple_stations = "和" in caption_text or "或" in caption_text

    stations_found = 0
    image_index = 0

    for link in station_links:
        link_text = link.text.strip()
        if not link_text or link_text in ["和", "或"]:
            continue

        station_info = {
            "name_cn": link_text,
            "name_en": "",
            "image_url": ""
        }

        img = link.find('img')
        if img and 'src' in img.attrs:
            img_src = img['src']
            if img_src.startswith('//'):
                img_src = 'https:' + img_src
            elif not img_src.startswith(('http://', 'https://')):
                img_src = 'https://terraria.wiki.gg' + ('' if img_src.startswith('/') else '/') + img_src

            station_info["image_url"] = img_src
            station_info["name_en"] = extract_english_name(img_src)

        if not station_info["image_url"] and image_index < len(image_urls):
            station_info["image_url"] = image_urls[image_index]
            station_info["name_en"] = extract_english_name(image_urls[image_index])
            image_index += 1

        if stations_found == 0:
            crafting_station["name_cn"] = station_info["name_cn"]
            crafting_station["name_en"] = station_info["name_en"]
            crafting_station["image_url"] = station_info["image_url"]
        else:
            crafting_station["additional_stations"].append(station_info)

        stations_found += 1

        if not has_multiple_stations and stations_found > 0:
            break

    if has_multiple_stations and len(crafting_station["additional_stations"]) > 0:
        combined_name = crafting_station["name_cn"]
        combined_name += " 和 "
        for station in crafting_station["additional_stations"]:
            combined_name += station["name_cn"] + " 或 "
        combined_name = combined_name.rstrip(" 或 ")

        crafting_station["combined_name"] = combined_name
        print(f"找到多个制作站: {combined_name}")

    return crafting_station

def download_subpage(url, retries=3, timeout=15):
    """
    下载子页面并保存到本地文件

    Args:
        url: 子页面URL
        retries: 最大重试次数
        timeout: 请求超时时间

    Returns:
        tuple: (是否成功, 本地文件路径或None, 页面内容或None, 是否进行了网络请求)
    """
    subpage_dir = ensure_subpage_dir()
    filename = get_subpage_filename(url)
    filepath = os.path.join(subpage_dir, filename)

    # 检查文件是否已存在
    if os.path.exists(filepath):
        print(f"子页面已存在，直接读取: {filepath}")
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            return True, filepath, content, False  # 第四个参数表示没有进行网络请求
        except Exception as e:
            print(f"读取已存在的子页面文件失败: {e}")
            # 如果读取失败，尝试重新下载

    # 下载子页面
    html_content = fetch_page(url, retries=retries, timeout=timeout)
    if not html_content:
        return False, None, None, True  # 进行了网络请求，但失败了

    # 保存子页面
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"成功保存子页面: {filepath}")
        return True, filepath, html_content, True  # 进行了网络请求，并成功了
    except Exception as e:
        print(f"保存子页面失败: {e}")
        return False, None, html_content, True  # 进行了网络请求，但保存失败

def save_to_json(data, filename="terraria_recipes.json"):
    """
    将爬取的数据保存为JSON文件

    Args:
        data: 要保存的数据
        filename: 输出文件名，默认为"terraria_recipes.json"
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"成功保存 {len(data)} 个配方到文件: {filename}")
        return True
    except Exception as e:
        print(f"保存数据到文件 {filename} 时出错: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="爬取泰拉瑞亚Wiki的合成配方")
    parser.add_argument("--no-resume", action="store_true", help="不从检查点恢复，重新开始爬取")
    parser.add_argument("--checkpoint", default="recipes_checkpoint.json", help="检查点文件路径")
    parser.add_argument("--output", default="terraria_recipes.json", help="输出文件路径")
    parser.add_argument("--retries", type=int, default=3, help="请求失败时的最大重试次数")
    args = parser.parse_args()

    # 开始爬取
    print(f"开始爬取泰拉瑞亚Wiki的合成配方...")
    recipes = scrape_recipes(
        resume=not args.no_resume,
        checkpoint_file=args.checkpoint,
        max_retries=args.retries
    )

    # 保存结果
    if recipes:
        save_to_json(recipes, filename=args.output)
        print(f"爬取完成，共保存 {len(recipes)} 个配方到文件: {args.output}")
    else:
        print("爬取失败，未获取到任何配方数据")
